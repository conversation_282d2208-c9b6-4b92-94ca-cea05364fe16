[program:low_sec_detect]
command = /usr/bin/python3 /data/home/<USER>/low_sec_detect/scanner/main.py  ; 主程序命令
directory = /data/home/<USER>/low_sec_detect/  ; 工作目录
user = root                ; 运行用户
autostart = true                ; 随 supervisor 自动启动
autorestart = true              ; 崩溃时自动重启
startretries = 3                ; 启动失败重试次数
startsecs = 10                  ; 启动后10秒无异常视为成功
stopsignal = TERM               ; 停止信号
stopwaitsecs = 60               ; 优雅停止等待时
; 日志配置
stdout_logfile = /var/log/supervisor/low_sec_detect.log
stdout_logfile_maxbytes = 50MB
stdout_logfile_backups = 10
stderr_logfile = /var/log/supervisor/low_sec_detect_err.log