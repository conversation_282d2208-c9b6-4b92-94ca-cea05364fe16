# database/models.py
from dataclasses import dataclass
from datetime import datetime
from ..config import settings

@dataclass
class Vulnerability:
    """用于表示一个扫描发现的通用漏洞的数据模型。"""
    host: str
    port: int
    url: str
    date_key: str = ""
    leak_type: str = "内网数据库漏洞"
    risk_name: str = "redis未授权漏洞"
    risk_tag: str = "敏感数据泄露"
    risk_level: str = "1"
    risk_status: str = "2"
    risk_type: int = settings.RISK_TYPE_REDIS
    create_by: str = "oldbuddyxin"
    case_content: str = ""
    create_time: str = ""
    guide_link: str = "https://iwiki.woa.com/p/4007424235"

    @property
    def risk_desc(self) -> str:
        return "高危：redis启动时默认监听在0.0.0.0:6379上，任何人都可以直接连接访问redis服务，若此时业务对redis服务的安全配置不当（如无密码、root权限启动、高危命令未重命名等），则可能会导致被恶意拉取/删除数据、服务器被入侵等安全风险。修复指引：https://iwiki.woa.com/p/4007424235"

    def __post_init__(self):
        now = datetime.now()
        self.date_key = now.strftime("%Y%m%d%H")
        self.create_time = now.strftime("%Y-%m-%d %H:%M:%S")
        self.case_content = ""


    def to_db_tuple(self, owner_info: dict) -> tuple:
        """将漏洞信息和负责人信息转换为用于数据库插入的元组。"""
        return (
            self.date_key,
            self.leak_type,
            self.url,
            self.host,
            self.port,
            owner_info.get("user_name", ""),
            owner_info.get("bg", ""),
            owner_info.get("dept", ""),
            owner_info.get("center", ""),
            self.risk_name,
            self.risk_desc,
            self.risk_tag,
            self.risk_level,
            self.risk_status,
            owner_info.get("oa_group", ""),
            self.create_by,
            self.case_content,
            self.create_time,
            self.risk_type,
            self.guide_link,
        )
