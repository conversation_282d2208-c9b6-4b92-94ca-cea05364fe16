#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import re
from typing import Set

def run_fscan(target_file_path: str, ports: str, log_file_path: str, fscan_executable: str, threads: int) -> bool:
    """
    执行 fscan 扫描，并将结果追加到日志文件中。
    """
    print("-" * 50)
    print("信息: 正在启动 fscan 扫描...")
    
    # 构建 fscan 命令
    # 注意: 我们使用 shell=True 和输出重定向 `>>` 来将结果追加到日志文件
    command_str = (
        f"cd {os.path.dirname(fscan_executable)} && "
        f"./{os.path.basename(fscan_executable)} -nobr -nopoc -np "
        f"-p {ports} "
        f"-hf {target_file_path} "
        f"-o {log_file_path} "
        f"-t {threads} "
    )
    
    print(f"执行命令: {command_str}")
    
    try:
        # 使用 shell=True 来执行包含重定向的完整命令字符串
        process = subprocess.run(
            command_str,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='ignore'
        )
        print("成功: fscan 扫描分片执行完成。")
        if process.stderr:
            print(f"警告: fscan 执行过程中产生了一些标准错误输出:\n{process.stderr}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: fscan 执行失败。返回码: {e.returncode}")
        print(f"标准输出: {e.stdout}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"致命错误: fscan 可执行文件未找到: {fscan_executable}")
        return False
    except Exception as e:
        print(f"致命错误: 执行 fscan 时发生未知错误: {e}")
        return False

def parse_fscan_log(log_path: str) -> Set[str]:
    """
    解析fscan的日志，提取Redis未授权访问的 'host:port' 集合。
    """
    live_vulns = set()
    if not os.path.exists(log_path):
        print(f"警告: fscan 日志文件不存在: {log_path}，无法解析。")
        return live_vulns
        
    # 正则表达式匹配 "[+] Redis 127.0.0.1:6379" 这样的行
    pattern = re.compile(r'\[\+\] Redis\s+([\d\.:]+)')
    
    try:
        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                # 检查行中是否包含 "Redis" 关键字以提高效率
                if "Redis" in line:
                    match = pattern.search(line)
                    if match:
                        # group(1) 捕获的是 "ip:port"
                        host_port = match.group(1).strip()
                        live_vulns.add(host_port)
    except Exception as e:
        print(f"错误: 解析 fscan 日志失败。原因: {e}")
    
    print(f"信息: 从 fscan 日志中解析出 {len(live_vulns)} 条唯一的 Redis 未授权访问漏洞。")
    return live_vulns
