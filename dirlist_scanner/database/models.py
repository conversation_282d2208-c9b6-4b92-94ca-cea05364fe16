# database/models.py
from dataclasses import dataclass
from datetime import datetime
from ..config import settings

@dataclass
class Vulnerability:
    """用于表示一个扫描发现的通用漏洞的数据模型。"""
    host: str
    port: int
    url: str
    date_key: str = ""
    leak_type: str = "内网web漏洞"
    risk_name: str = "文件服务暴露漏洞"
    risk_tag: str = "敏感数据泄露"
    risk_level: str = "1"
    risk_status: str = "2"
    risk_type: int = settings.RISK_TYPE_DIRLIST
    create_by: str = "oldbuddyxin"
    case_content: str = ""
    create_time: str = ""
    guide_link: str = "https://iwiki.woa.com/p/1894248130"

    @property
    def risk_desc(self) -> str:
        return "高危：目录浏览漏洞主要是由于中间件(IIS、apache、nginx等)配置不当，当访问到某一目录中没有索引文件时（或者手工开启了目录浏览功能）即把当前目录中的所有文件及相关下层目录一一在页面中显示出来。通过该漏洞攻击者可获得服务器上的文件目录结构，从而下载敏感文件（备份文件存放地址、数据文件、数据库文件、源代码文件等）。修复指引：https://iwiki.woa.com/p/1894248130"

    def __post_init__(self):
        now = datetime.now()
        self.date_key = now.strftime("%Y%m%d%H")
        self.create_time = now.strftime("%Y-%m-%d %H:%M:%S")
        self.case_content = ""


    def to_db_tuple(self, owner_info: dict) -> tuple:
        """将漏洞信息和负责人信息转换为用于数据库插入的元组。"""
        return (
            self.date_key,
            self.leak_type,
            self.url,
            self.host,
            self.port,
            owner_info.get("user_name", ""),
            owner_info.get("bg", ""),
            owner_info.get("dept", ""),
            owner_info.get("center", ""),
            self.risk_name,
            self.risk_desc,
            self.risk_tag,
            self.risk_level,
            self.risk_status,
            owner_info.get("oa_group", ""),
            self.create_by,
            self.case_content,
            self.create_time,
            self.risk_type,
            self.guide_link,
        )

