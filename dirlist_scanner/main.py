#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from typing import Set
from datetime import datetime

from .config import settings
from .core import scanner, utils
from .database.db_handler import DatabaseHandler
from .database.models import Vulnerability

# 添加项目根目录到Python路径，以便导入日志模块
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)
from routine_scan_logs.logger import log_initial_scan, log_audit_scan

def run_initial_scan():
    """
    任务一: 执行原始扫描
    对大规模的原始IP列表文件进行分片、扫描，并将发现的漏洞存入数据库。
    """
    print("\n" + "="*20 + " 任务一: 开始执行目录泄露原始扫描 " + "="*20)
    
    # 1. 准备工作：检查原始目标文件、加载IP信息、初始化数据库
    if not os.path.exists(settings.RAW_TARGETS_FILE):
        print(f"致命错误: 原始扫描目标文件未找到: {settings.RAW_TARGETS_FILE}")
        return

    print("信息: 正在加载IP负责人数据...")
    ip_owner_info = utils.load_ip_owner_info(settings.RAW_CSV_FILE)
    db_handler = DatabaseHandler(settings.DB_CONFIG)

    # 统计扫描的IP总数
    total_ips = 0
    try:
        with open(settings.RAW_TARGETS_FILE, 'r', encoding='utf-8') as f:
            total_ips = sum(1 for line in f if line.strip())
        print(f"信息: 本次将扫描 {total_ips} 个IP地址")
    except Exception as e:
        print(f"警告: 无法统计IP总数: {e}")
        total_ips = 0

    # 2. 对大文件进行分片
    print("信息: 正在将大型IP列表文件分片...")
    chunk_files = utils.split_file_into_chunks(
        filepath=settings.RAW_TARGETS_FILE,
        chunk_size=settings.IP_CHUNK_SIZE,
        temp_dir=settings.TEMP_CHUNK_DIR
    )
    if not chunk_files:
        print("错误: IP文件分片失败，中止原始扫描。")
        return

    # 3. 遍历分片进行扫描
    all_live_vulns_set: Set[str] = set()
    total_chunks = len(chunk_files)
    for i, chunk_file in enumerate(chunk_files):
        print(f"\n--- 正在处理分片 {i + 1}/{total_chunks}: {os.path.basename(chunk_file)} ---")
        
        # 为每个分片指定一个临时的日志文件，避免并发问题（虽然目前是串行）
        chunk_log_path = os.path.join(settings.LOGS_DIR, f"initial_scan_chunk_{i+1}.log")

        scan_success = scanner.run_txportmap(
            target_file_path=chunk_file,
            ports=settings.TXPORTMAP_PORTS,
            log_file_path=chunk_log_path,
            txportmap_executable=settings.TXPORTMAP_EXECUTABLE
        )

        if scan_success:
            live_vulns_in_chunk = scanner.parse_txportmap_log(chunk_log_path)
            all_live_vulns_set.update(live_vulns_in_chunk)
            os.remove(chunk_log_path) # 清理分片日志
        else:
            print(f"警告: 分片 {os.path.basename(chunk_file)} 扫描失败，跳过此分片。")
    
    utils.cleanup_temp_chunks(settings.TEMP_CHUNK_DIR) # 清理所有分片文件
    print(f"\n信息: 所有分片扫描完成，共发现 {len(all_live_vulns_set)} 个疑似漏洞。")

    # 4. 将所有发现的漏洞写入数据库
    uploaded_count = 0
    if all_live_vulns_set:
        print("\n信息: 开始将所有扫描结果统一写入数据库...")
        for host_port in all_live_vulns_set:
            try:
                host, port_str = host_port.split(':')
                port = int(port_str)
                # 构造URL并创建Vulnerability对象
                url = f"http://{host}:{port}"
                vuln = Vulnerability(host=host, port=port, url=url)
                owner = ip_owner_info.get(host, {})
                db_handler.add_vulnerability(vuln, owner)
                uploaded_count += 1
            except Exception as e:
                print(f"错误: 处理漏洞 {host_port} 并存入数据库时失败: {e}")

    # 5. 记录扫描统计信息
    try:
        log_initial_scan(
            tool_name="dirlist_scanner",
            scanned_ips=total_ips,
            found_vulns=len(all_live_vulns_set),
            uploaded_to_db=uploaded_count
        )
    except Exception as e:
        print(f"警告: 记录扫描日志失败: {e}")

    print("\n" + "="*20 + " 任务一: 目录泄露原始扫描完成 " + "="*20)


def run_audit_scan():
    """
    任务二: 执行复测扫描
    从数据库拉取活跃的目录泄露漏洞，再次扫描以确认其状态。
    """
    print("\n" + "="*20 + " 任务二: 开始执行目录泄露复测扫描 " + "="*20)
    db_handler = DatabaseHandler(settings.DB_CONFIG)

    # 1. 拉取待复测目标
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_DIRLIST
    )
    if not targets_to_retest:
        print("信息: 数据库中没有需要复测的目录泄露活跃漏洞，任务结束。")
        return

    # 2. 将目标写入临时文件
    initial_targets_set: Set[str] = set()
    with open(settings.AUDIT_TARGETS_FILE, 'w') as f:
        for item in targets_to_retest:
            host_port_str = f"{item['host']}:{item['port']}"
            f.write(f"{item['host']}\n") # TxPortMap 只需 host
            initial_targets_set.add(host_port_str)
    print(f"信息: 已将 {len(initial_targets_set)} 个唯一目标写入复测文件: {settings.AUDIT_TARGETS_FILE}")
    
    # 3. 执行复测扫描
    scan_success = scanner.run_txportmap(
        target_file_path=settings.AUDIT_TARGETS_FILE,
        ports=settings.TXPORTMAP_PORTS,
        log_file_path=settings.AUDIT_SCAN_LOG_FILE,
        txportmap_executable=settings.TXPORTMAP_EXECUTABLE
    )

    if not scan_success:
        print("警告: 复测扫描执行失败，将中止本次复测流程。")
        return
        
    # 4. 解析结果并更新数据库
    live_vulns_in_audit = scanner.parse_txportmap_log(settings.AUDIT_SCAN_LOG_FILE)
    print(f"信息: 复测扫描发现 {len(live_vulns_in_audit)} 个仍然存活的漏洞。")

    fixed_vulns_set = initial_targets_set - live_vulns_in_audit
    print(f"信息: 计算得出 {len(fixed_vulns_set)} 个漏洞已被修复。")
    
    db_handler.batch_update_status_to_fixed(
        fixed_host_ports=list(fixed_vulns_set),
        risk_type=settings.RISK_TYPE_DIRLIST
    )
    
    # 5. 记录复测统计信息
    try:
        log_audit_scan(
            tool_name="dirlist_scanner",
            checked_vulns=len(initial_targets_set),
            fixed_vulns=len(fixed_vulns_set)
        )
    except Exception as e:
        print(f"警告: 记录复测日志失败: {e}")

    # 6. 清理临时文件
    os.remove(settings.AUDIT_TARGETS_FILE)
    if os.path.exists(settings.AUDIT_SCAN_LOG_FILE):
        os.remove(settings.AUDIT_SCAN_LOG_FILE)
    print("信息: 已清理临时复测文件和日志。")

    print("\n" + "="*20 + " 任务二: 目录泄露复测扫描完成 " + "="*20)
