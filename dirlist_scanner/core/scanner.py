#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import re
from typing import List, Set

def run_txportmap(target_file_path: str, ports: str, log_file_path: str, txportmap_executable: str) -> bool:
    """
    执行 TxPortMap 扫描。
    """
    print("-" * 50)
    print("信息: 正在启动 TxPortMap 扫描...")
    
    # 在运行前，如果旧的日志文件存在，则删除它，以确保日志是本次扫描的
    if os.path.exists(log_file_path):
        os.remove(log_file_path)
        print(f"信息: 已删除旧的日志文件: {log_file_path}")

    command = [
        txportmap_executable,
        "-l", target_file_path,
        "-p", ports,
        "-o", log_file_path
    ]
    print(f"执行命令: {' '.join(command)}")
    
    try:
        subprocess.run(
            command,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        print("成功: TxPortMap 扫描执行完成。")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: TxPortMap 执行失败。返回码: {e.returncode}")
        stderr_output = e.stderr.decode('utf-8', errors='ignore')
        print(f"错误输出: {stderr_output}")
        return False
    except FileNotFoundError:
        print(f"致命错误: TxPortMap可执行文件未找到: {command[0]}")
        return False
    except Exception as e:
        print(f"致命错误: 执行 TxPortMap 时发生未知错误: {e}")
        return False

def parse_txportmap_log(log_path: str) -> Set[str]:
    """
    解析TxPortMap的日志，返回存活的 'host:port' 集合。
    """
    live_vulns = set()
    if not os.path.exists(log_path):
        print(f"警告: TxPortMap日志文件不存在: {log_path}，无法解析。")
        return live_vulns
        
    # 正则表达式匹配 "ip:port ... Directory listing" 格式
    pattern = re.compile(r'^([\d\.]+:\d+).*?Directory listing')
    try:
        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                match = pattern.search(line)
                if match:
                    live_vulns.add(match.group(1))
    except Exception as e:
        print(f"错误: 解析TxPortMap日志失败。原因: {e}")
    
    print(f"信息: 从TxPortMap日志中解析出 {len(live_vulns)} 条存活漏洞。")
    return live_vulns
