from apscheduler.schedulers.blocking import BlockingScheduler
import sys
import os

# 将项目根目录添加到Python路径中，以确保可以正确导入各个扫描模块
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 从各个扫描器模块中导入核心执行函数
from dirlist_scanner.main import run_initial_scan as dir_initial_scan, run_audit_scan as dir_audit_scan
from bscan_ck.main import run_initial_scan as ck_initial_scan, run_audit_scan as ck_audit_scan
from bscan_es.main import run_initial_scan as es_initial_scan, run_audit_scan as es_audit_scan
from bscan_common.main import run_initial_scan as common_initial_scan, run_audit_scan as common_audit_scan
from redis_scanner.main import run_initial_scan as redis_initial_scan, run_audit_scan as redis_audit_scan

# 导入日志聚合功能
from routine_scan_logs.logger import generate_daily_summary

def begin_scan_dir_vul():
    print("开始执行 文件目录扫描...")
    dir_initial_scan()

def retest_scan_dir_vul():
    """复测通用性文件目录"""
    print("开始复测 文件目录扫描...")
    dir_audit_scan()

def begin_scan_ck_vul():
    print("开始执行 click house 未授权扫描...")
    ck_initial_scan()

def retest_scan_ck_vul():
    """复测ClickHouse未授权访问"""
    print("开始复测 click house 未授权扫描...")
    ck_audit_scan()

def begin_scan_es_vul():
    print("开始执行 es 未授权扫描...")
    es_initial_scan()

def retest_scan_es_vul():
    """复测ElasticSearch未授权访问"""
    print("开始复测 es 未授权扫描...")
    es_audit_scan()

def begin_scan_common_vul():
    print("开始执行 常规端口扫描...")
    common_initial_scan()

def retest_scan_common_vul():
    """复测常规端口扫描"""
    print("开始复测 常规端口扫描...")
    common_audit_scan()

def begin_scan_redis_vul():
    print("开始执行 redis 未授权扫描...")
    redis_initial_scan()

def retest_scan_redis_vul():
    """复测Redis未授权访问"""
    print("开始复测 redis 未授权扫描...")
    redis_audit_scan()


def generate_daily_log_summary():
    """生成每日扫描日志汇总"""
    print("开始生成每日扫描日志汇总...")
    try:
        total_stats = generate_daily_summary()
        print(f"日志汇总完成: {total_stats}")
    except Exception as e:
        print(f"生成日志汇总失败: {e}")


scheduler = BlockingScheduler()


# 扫描任务
# 每30秒执行一次
# scheduler.add_job(job, 'interval', seconds=30)
# 0 10 开始执行 通路文件扫描
scheduler.add_job(begin_scan_dir_vul, 'cron', hour=0, minute=10)

# 0 20 开始执行 ck未授权扫描
scheduler.add_job(begin_scan_ck_vul, 'cron', hour=0, minute=20)

# 0 30 开始执行 es未授权扫描
scheduler.add_job(begin_scan_es_vul, 'cron', hour=0, minute=30)

# 0 40 开始执行 redis未授权扫描
scheduler.add_job(begin_scan_redis_vul, 'cron', hour=0, minute=40)

# 0 50 开始执行 常规端口扫描
scheduler.add_job(begin_scan_common_vul, 'cron', hour=0, minute=50)

# 复测任务
# 9-23 点开始复测 每一小时执行一次
scheduler.add_job(
    retest_scan_dir_vul,
    'cron',
    hour='9-23',
    minute=10
)


scheduler.add_job(
    retest_scan_ck_vul,
    'cron',
    hour='9-23',
    minute=20
)


scheduler.add_job(
    retest_scan_es_vul,
    'cron',
    hour='9-23',
    minute=30
)

scheduler.add_job(
    retest_scan_redis_vul,
    'cron',
    hour='9-23',
    minute=40
)

scheduler.add_job(
    retest_scan_common_vul,
    'cron',
    hour='9-23',
    minute=50
)

# 每天23:50生成日志汇总
scheduler.add_job(
    generate_daily_log_summary,
    'cron',
    hour=23,
    minute=50
)

# 开启任务
scheduler.start()