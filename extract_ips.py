#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从ip_info.csv中提取只包含IP列的CSV文件
"""

import pandas as pd

def extract_ip_only():
    """从ip_info.csv提取只包含IP列的CSV"""
    print("开始从ip_info.csv提取IP列...")
    
    # 读取ip_info.csv文件
    df = pd.read_csv('temp_ipfo_assets/teg_ipinfo.csv')
    
    print(f"原始数据量: {len(df)} 条记录")
    
    # 只保留IP列
    ip_only_df = df[['ip']].copy()
    
    # 保存为ips.csv
    output_path = 'temp_ipfo_assets/ips.csv'
    ip_only_df.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"IP列提取完成，保存到: {output_path}")
    print(f"输出数据量: {len(ip_only_df)} 条记录")
    
    # 显示前几行作为验证
    print("\n前5行数据预览:")
    print(ip_only_df.head())
    
    return ip_only_df

def main():
    """主函数"""
    extract_ip_only()
    print("\n提取完成！")

if __name__ == "__main__":
    main()
