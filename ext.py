import csv
from collections import Counter

def read_csv_and_count_distribution(file_path):
    # 初始化统计字典
    bg_count = Counter()
    dept_count = Counter()
    center_count = Counter()
    oa_group_count = Counter()

    # 打开CSV文件并读取数据
    with open(file_path, newline='', encoding='utf-8') as file:
        reader = csv.DictReader(file)  # 使用 DictReader 以便按列名读取
        for row in reader:
            # 获取每一行中字段的值，并统计
            # bg_count[row['oper_bg']] += 1
            # dept_count[row['oper_dept']] += 1
            # center_count[row['oper_center']] += 1
            # oa_group_count[row['oper_group']] += 1
            bg_count[row['bg']] += 1
            dept_count[row['dept']] += 1
            center_count[row['center']] += 1
            oa_group_count[row['oa_group']] += 1
    # 输出统计结果
    print("bg字段分布情况:")
    for key, count in bg_count.items():
        print(f"{key}: {count}")

    print("\ndepartment字段分布情况:")
    for key, count in dept_count.items():
        print(f"{key}: {count}")

    print("\ncenter字段分布情况:")
    for key, count in center_count.items():
        print(f"{key}: {count}")

    print("\noa_group字段分布情况:")
    for key, count in oa_group_count.items():
        print(f"{key}: {count}")

if __name__ == "__main__":
    # CSV 文件路径
    file_path = '/Users/<USER>/low_sec_detect/temp_ip_info_assets/ip_info.csv'  # 这里请替换为你的文件路径
    read_csv_and_count_distribution(file_path)
