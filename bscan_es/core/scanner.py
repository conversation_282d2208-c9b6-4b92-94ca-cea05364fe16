#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import re
import pandas as pd
from typing import List, Set
from urllib.parse import urlparse
from datetime import datetime
from ..config import settings

def run_bscan(target_file_path: str, ports: str, bscan_executable: str, output_dir: str) -> bool:
    """
    执行bscan扫描 (针对ElasticSearch)，并将报告输出到指定目录。
    """
    print("-" * 50)
    print(f"信息: 正在启动 bscan 扫描... 输出目录: {output_dir}")
    
    os.makedirs(output_dir, exist_ok=True)

    # 确定bscan的工作目录，通常是其可执行文件所在的目录，以确保能加载配置文件
    bscan_home = os.path.dirname(bscan_executable)
    if not bscan_home:
        bscan_home = '.' # 如果路径为空，则回退到当前目录

    # 将目标文件路径转换为绝对路径，以避免因切换工作目录(cwd)导致路径找不到的问题
    abs_target_file_path = os.path.abspath(target_file_path)

    command = [
        bscan_executable,
        "-target", abs_target_file_path,
        "-ports", ports,
    ]
    print(f"执行命令: {' '.join(command)}")
    print(f"信息: 将在bscan所在目录 '{bscan_home}' 执行命令, 以确保其能正确加载配置文件。")
    
    try:
        subprocess.run(
            command,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=bscan_home  # 核心改动：切换工作目录以加载配置文件
        )
        print("成功: bscan 扫描执行完成。")

        # bscan默认将报告输出到其当前工作目录(CWD)，即 bscan_home。
        # 我们需要找到最新的报告并将其移动到用户指定的 output_dir。
        latest_report = find_latest_bscan_report(bscan_home)
        if latest_report:
            report_filename = os.path.basename(latest_report)
            destination_path = os.path.join(output_dir, report_filename)
            # 如果目标文件已存在，先删除，以确保os.rename成功
            if os.path.exists(destination_path):
                os.remove(destination_path)
            print(f"信息: 移动报告 '{report_filename}' 从 '{bscan_home}' 到 '{output_dir}'")
            os.rename(latest_report, destination_path)
        else:
            print("警告: bscan执行完毕，但在其工作目录中未找到任何 .xlsx 报告文件。")

        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: bscan 执行失败。返回码: {e.returncode}")
        stderr_output = e.stderr.decode('utf-8', errors='ignore')
        print(f"错误输出: {stderr_output}")
        return False
    except FileNotFoundError:
        print(f"致命错误: bscan可执行文件未找到: {command[0]}")
        return False
    except Exception as e:
        print(f"致命错误: 执行bscan时发生未知错误: {e}")
        return False

def find_latest_bscan_report(directory: str) -> str or None:
    """在一个目录下查找最新修改的 .xlsx 文件"""
    print(f"信息: 正在目录 '{directory}' 中查找最新的 .xlsx 报告...")
    try:
        xlsx_files = [f for f in os.listdir(directory) if f.lower().endswith('.xlsx')]
        if not xlsx_files:
            print(f"警告: 在目录 '{directory}' 中没有找到任何 .xlsx 文件。")
            return None
        
        full_paths = [os.path.join(directory, f) for f in xlsx_files]
        latest_file = max(full_paths, key=os.path.getmtime)
        print(f"信息: 已找到最新报告文件: {os.path.basename(latest_file)}")
        return latest_file
    except FileNotFoundError:
        print(f"错误: 报告目录不存在: {directory}")
        return None
    except Exception as e:
        print(f"错误: 查找最新报告时发生未知错误: {e}")
        return None

def parse_elasticsearch_report(report_path: str) -> Set[str]:
    """
    使用pandas直接读取xlsx报告的'Sheet1'，并通过匹配'Application'列筛选出ES漏洞。
    """
    live_vulns = set()
    if not os.path.exists(report_path):
        print(f"警告: bscan报告文件不存在: {report_path}，无法解析。")
        return live_vulns

    try:
        df = pd.read_excel(report_path, sheet_name='Sheet1', engine='openpyxl')

        # --- 详细调试信息 ---
        print(f"[DEBUG] pandas读取Sheet1成功。维度(行,列): {df.shape}")
        print(f"[DEBUG] 检测到的列名: {df.columns.tolist()}")
        if not df.empty:
            print(f"[DEBUG] 前5行数据预览:\n{df.head().to_string()}")
        else:
            print("[DEBUG] 数据表为空。")
        # --- 调试结束 ---

        app_col = 'Application'
        # 动态确定URL列的名称
        url_col = 'URL' if 'URL' in df.columns else 'Target'

        if app_col not in df.columns or url_col not in df.columns:
            print(f"信息: 报告 '{os.path.basename(report_path)}' 的Sheet1中缺少必需的列 ('{app_col}' 和 '{url_col}')。")
            return live_vulns

        # 修正：使用更可靠的核心关键字进行匹配，而不是依赖配置文件的完整描述
        keyword = "elasticsearch"

        for index, row in df.iterrows():
            app_name = str(row.get(app_col, '')).lower()
            if keyword in app_name:
                target_str = row.get(url_col)
                if pd.isna(target_str):
                    continue
                
                host_port_str = str(target_str).strip()
                if "://" in host_port_str:
                    parsed = urlparse(host_port_str)
                    if parsed.hostname and parsed.port:
                        live_vulns.add(f"{parsed.hostname}:{parsed.port}")
                elif ":" in host_port_str:
                    parts = host_port_str.split(':')
                    if len(parts) == 2 and parts[1].isdigit():
                        live_vulns.add(host_port_str)

    except Exception as e:
        print(f"错误: 使用pandas解析bscan报告时发生意外错误。原因: {e}")
        
    print(f"信息: 从报告中筛选出 {len(live_vulns)} 条ElasticSearch存活漏洞。")
    return live_vulns

def process_bscan_report_to_db(report_path: str, db_handler, ip_owner_info: dict):
    """
    解析指定的bscan报告，筛选出ES漏洞，并将结果写入数据库。

    :param report_path: bscan生成的xlsx报告的路径。
    :param db_handler: 已经实例化的DatabaseHandler。
    :param ip_owner_info: IP与负责人信息的映射字典。
    """
    from ..database.models import Vulnerability  # 延迟导入以避免循环依赖
    print(f"\n--- 开始处理报告文件: {os.path.basename(report_path)} ---")
    
    live_vulns_set = parse_elasticsearch_report(report_path)
    if not live_vulns_set:
        print("信息: 报告中未发现有效漏洞，处理结束。")
        return {"found_vulns": 0, "uploaded_to_db": 0}

    print(f"信息: 从报告中解析出 {len(live_vulns_set)} 个ES漏洞，正在写入数据库...")
    uploaded_count = 0
    for host_port in live_vulns_set:
        try:
            host, port_str = host_port.split(":")
            port = int(port_str)
            # 构造一个URL用于存储
            url = f"http://{host}:{port}"  # ES通常使用HTTP
            vuln = Vulnerability(host=host, port=port, url=url)
            owner = ip_owner_info.get(host, {})
            db_handler.add_vulnerability(vuln, owner)
            uploaded_count += 1
        except Exception as e:
            print(f"错误: 处理漏洞 {host_port} 并存入数据库时失败: {e}")

    print(f"--- 报告文件 {os.path.basename(report_path)} 处理完毕 ---")
    return {"found_vulns": len(live_vulns_set), "uploaded_to_db": uploaded_count}


def process_audit_report_and_update_db(report_path: str, db_handler):
    """
    解析指定的复测报告，与数据库中的ES活跃漏洞比对，并更新已修复的漏洞状态。

    :param report_path: bscan生成的xlsx报告的路径。
    :param db_handler: 已经实例化的DatabaseHandler。
    """
    from ..config import settings  # 延迟导入
    print(f"\n--- 开始处理ES复测报告文件: {os.path.basename(report_path)} ---")

    # 1. 从数据库拉取当前所有活跃漏洞，作为复测的基准
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_ELASTICSEARCH
    )
    if not targets_to_retest:
        print("信息: 数据库中没有需要复测的ES活跃漏洞，处理中止。")
        return {"checked_vulns": 0, "fixed_vulns": 0}

    initial_targets_set = {f"{item['host']}:{item['port']}" for item in targets_to_retest}
    print(f"信息: 从数据库中加载了 {len(initial_targets_set)} 个ES活跃漏洞作为比对基准。")

    # 2. 从报告中解析出本次扫描仍然存活的漏洞
    live_vulns_in_audit = parse_elasticsearch_report(report_path)
    print(f"信息: 复测报告中发现 {len(live_vulns_in_audit)} 个仍然存活的ES漏洞。")

    # 3. 计算已修复的漏洞（在基准中，但本次未存活）并更新数据库
    fixed_vulns_set = initial_targets_set - live_vulns_in_audit
    print(f"信息: 计算得出 {len(fixed_vulns_set)} 个ES漏洞已被修复。")

    if fixed_vulns_set:
        db_handler.batch_update_status_to_fixed(
            fixed_host_ports=list(fixed_vulns_set),
            risk_type=settings.RISK_TYPE_ELASTICSEARCH
        )

    print(f"--- ES复测报告文件 {os.path.basename(report_path)} 处理完毕 ---")
    return {"checked_vulns": len(initial_targets_set), "fixed_vulns": len(fixed_vulns_set)}