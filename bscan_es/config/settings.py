# config/settings.py
import os
from datetime import datetime
# 使用绝对路径从项目根目录导入共享配置
from common_config import *

# ==============================================================================
# 基础配置 (项目特有)
# ==============================================================================
# 项目根目录
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

# 工作目录
WORKSPACE_DIR = os.path.join(BASE_DIR, "assets")

# ==============================================================================
# 漏洞与扫描配置 (项目特有)
# ==============================================================================

# 漏洞类型定义
RISK_TYPE_ELASTICSEARCH = 3
ELASTICSEARCH_VULN_DETAILS = {
    'risk_id': RISK_TYPE_ELASTICSEARCH,
    'risk_type': 'ElasticSearch 未授权访问',
    'risk_level': '1', # 修正：统一为数字 '1' (高危)
    'risk_source': 'bscan',
    'risk_name_pattern': '%ElasticSearch%',
}

# bscan (ElasticSearch扫描器) 相关配置
BSCAN_DIR = os.path.join(WORKSPACE_DIR, "bscan_es")
BSCAN_EXECUTABLE = "bscan_linux_amd64"
BSCAN_PORTS = "9200"

# 分片扫描相关配置
IP_CHUNK_SIZE = 100000  # 原始IP文件分片大小
TEMP_CHUNK_DIR = os.path.join(BSCAN_DIR, 'temp_chunks')
CHUNK_LOG_DIR = os.path.join(BSCAN_DIR, 'chunk_log') # 用于存放分片扫描的临时xlsx结果

# 为不同扫描任务定义独立的报告输出目录
BSCAN_ORIGIN_LOG_DIR = os.path.join(BSCAN_DIR, "origin_results_log")
BSCAN_AUDIT_LOG_DIR = os.path.join(BSCAN_DIR, "repeat_results_log")

# 原始扫描目标文件
RAW_TARGETS_FILE = SELECT_IP_FILE # 使用从common_config导入的路径
# 复测扫描目标文件 (临时生成)
AUDIT_TARGETS_FILE = os.path.join(WORKSPACE_DIR, f"elasticsearch_audit_targets_{datetime.now().strftime('%Y%m%d')}.txt")

# ==============================================================================
# 定时任务配置 (项目特有)
# ==============================================================================
# 每日执行扫描任务的时间 (24小时制)

# --- 漏洞信息配置 ---
VULN_CREATOR = "oldbuddyxin"


