# sec 低级问题扫描工具集

## 🛡️ 项目概述
本工具集用于自动化扫描组内常见低级安全问题，集成多款开源扫描工具，通过定时任务持续监控风险点。主要检测类型：
- ClickHouse 数据库未授权访问
- AGI 工具安全风险
- 文件路径遍历漏洞
- ES 数据库未鉴权访问
- 其他基础设施安全风险

**核心组件**：
- Python 扫描脚本（主程序）
- fscan/bscan/txportscan 开源工具
- Supervisor 进程管理
- Crontab 定时任务

## 📂 目录结构

todo

## 快速开始

### 环境初始化
todo


### 执行扫描任务
todo


## 📌 注意事项
扫描可能影响网络性能，建议避开业务高峰期
定期更新tools/中的扫描引擎
敏感结果文件需设置访问权限
首次使用前配置所有检测白名单


## 维护成员&提需
### 开发
oldbuddyxin(辛铁良) shangyuyang(杨尚谕)  lukecanli(李成龙)

### 提需
oldbuddyxin(辛铁良) shangyuyang(杨尚谕) 