#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IP统计分析脚本
统计ip_info.csv中的IP总数和按部门分组的分布
结果输出到daily_ip_stats文件夹中，按日期分组
"""

import pandas as pd
import sys
import os
from collections import Counter
from datetime import datetime
from common_config import IP_INFO_PATH, DB_CONFIG, DB_TABLE_SCAN_NAME
import pymysql

def create_output_directory():
    """
    创建输出目录结构
    """
    today = datetime.now().strftime("%Y-%m-%d")
    output_dir = os.path.join("daily_ip_stats", today)
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def deduplicate_ip_file(ip_file_path, unique_ips_from_csv):
    """
    基于CSV文件中的唯一IP列表来更新原始IP文件
    """
    print(f"正在基于CSV数据对 {ip_file_path} 进行去重操作...")
    try:
        # 读取原始IP文件
        with open(ip_file_path, 'r', encoding='utf-8') as f:
            original_ips = [line.strip() for line in f if line.strip()]

        original_count = len(original_ips)
        unique_count = len(unique_ips_from_csv)
        duplicate_count = original_count - unique_count

        if duplicate_count > 0:
            # 备份原文件
            backup_path = ip_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(ip_file_path, backup_path)
            print(f"原文件已备份为: {backup_path}")

            # 写入去重后的IP（使用CSV中的唯一IP列表）
            with open(ip_file_path, 'w', encoding='utf-8') as f:
                for ip in unique_ips_from_csv:
                    f.write(ip + '\n')

            print(f"去重完成: 原有 {original_count:,} 个IP，去重后 {unique_count:,} 个IP，删除重复 {duplicate_count:,} 个")
            return True, duplicate_count
        else:
            print("未发现重复IP，无需去重")
            return False, 0

    except Exception as e:
        print(f"去重操作失败: {e}")
        return False, 0

def insert_combination_stats_to_db(df):
    """
    将组合统计数据插入到远端数据库
    """
    print("正在将组合统计数据写入远端数据库...")

    try:
        # 建立数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()

        # 先清理今天的数据（避免重复插入）
        delete_sql = f"DELETE FROM {DB_TABLE_SCAN_NAME} WHERE date_key = CURDATE()"
        cursor.execute(delete_sql)
        print("已清理今天的历史数据")

        # 使用pandas的value_counts来高效统计组合
        # 创建组合字段
        df_clean = df.dropna(subset=['dept', 'center', 'oa_group'])
        combo_counts = df_clean.groupby(['dept', 'center', 'oa_group']).size().reset_index(name='ip_count')

        # 准备批量插入数据
        batch_data = []
        for _, row in combo_counts.iterrows():
            batch_data.append((
                int(row['ip_count']),
                str(row['dept']),
                str(row['center']),
                str(row['oa_group'])
            ))

        # 批量插入数据
        if batch_data:
            insert_sql = f"""
                INSERT INTO {DB_TABLE_SCAN_NAME} (date_key, ip_count, dept, center, oa_group)
                VALUES (CURDATE(), %s, %s, %s, %s)
            """

            # 分批插入，避免单次插入数据过多
            batch_size = 1000
            insert_count = 0
            for i in range(0, len(batch_data), batch_size):
                batch = batch_data[i:i + batch_size]
                cursor.executemany(insert_sql, batch)
                connection.commit()
                insert_count += len(batch)
                print(f"已插入 {insert_count}/{len(batch_data)} 条记录")

        cursor.close()
        connection.close()

        print(f"成功插入 {insert_count} 条组合统计记录到数据库")
        return True, insert_count

    except Exception as e:
        print(f"数据库插入失败: {e}")
        try:
            if 'connection' in locals():
                connection.close()
        except:
            pass
        return False, 0

def analyze_ip_distribution(csv_file):
    """
    分析IP分布情况并输出到文件
    """
    # 创建输出目录
    output_dir = create_output_directory()

    print("正在读取CSV文件...")
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        print(f"成功读取文件，共 {len(df)} 行数据")
        
        # 统计IP总数
        total_ips = len(df)

        # 创建总体统计报告
        summary_file = os.path.join(output_dir, "summary_report.txt")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("=== IP统计分析报告 ===\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据源文件: {csv_file}\n\n")

            f.write("=== IP总数统计 ===\n")
            f.write(f"IP总数: {total_ips:,}\n")

            # 检查是否有重复IP（理论上应该没有）
            unique_ips = df['ip'].nunique()
            f.write(f"唯一IP数: {unique_ips:,}\n")
            if total_ips != unique_ips:
                duplicate_count = total_ips - unique_ips
                f.write(f"警告: 发现重复IP，重复数量: {duplicate_count}\n")

        print(f"\n=== IP总数统计 ===")
        print(f"IP总数: {total_ips:,}")
        print(f"唯一IP数: {unique_ips:,}")

        # 如果发现重复IP，对原始IP文件进行去重
        if total_ips != unique_ips:
            duplicate_count = total_ips - unique_ips
            print(f"警告: 发现重复IP，重复数量: {duplicate_count}")

            # 获取CSV中的唯一IP列表
            unique_ips_list = df['ip'].drop_duplicates().tolist()

            # 对原始IP文件进行去重
            dedup_success, removed_count = deduplicate_ip_file(IP_INFO_PATH, unique_ips_list)
            if dedup_success:
                print(f"已对原始IP文件进行去重，删除了 {removed_count:,} 个重复IP")
                # 更新summary报告
                with open(summary_file, 'a', encoding='utf-8') as f:
                    f.write(f"去重操作: 已对原始IP文件 {IP_INFO_PATH} 进行去重，删除了 {removed_count:,} 个重复IP\n")
            else:
                print("IP文件去重操作失败")
        
        print(f"\n=== 按部门(dept)分组统计 ===")
        # 按dept分组统计
        dept_counts = df['dept'].value_counts()

        # 保存部门统计到文件
        dept_file = os.path.join(output_dir, "dept_statistics.txt")
        with open(dept_file, 'w', encoding='utf-8') as f:
            f.write("=== 按部门(dept)分组统计 ===\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            for dept, count in dept_counts.items():
                if pd.isna(dept):
                    f.write(f"未知部门: {count:,} 条\n")
                else:
                    f.write(f"{dept}: {count:,} 条\n")

        # 保存部门统计CSV
        dept_csv = os.path.join(output_dir, "dept_statistics.csv")
        dept_df = pd.DataFrame({
            'department': dept_counts.index,
            'ip_count': dept_counts.values
        })
        dept_df.to_csv(dept_csv, index=False, encoding='utf-8')

        print("部门分布:")
        for dept, count in dept_counts.items():
            if pd.isna(dept):
                print(f"  未知部门: {count:,} 条")
            else:
                print(f"  {dept}: {count:,} 条")
        
        print(f"\n=== 按中心(center)分组统计 ===")
        # 按center分组统计
        center_counts = df['center'].value_counts()

        # 保存中心统计到文件
        center_file = os.path.join(output_dir, "center_statistics.txt")
        with open(center_file, 'w', encoding='utf-8') as f:
            f.write("=== 按中心(center)分组统计 ===\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            for center, count in center_counts.items():
                if pd.isna(center):
                    f.write(f"未知中心: {count:,} 条\n")
                else:
                    f.write(f"{center}: {count:,} 条\n")

        # 保存中心统计CSV
        center_csv = os.path.join(output_dir, "center_statistics.csv")
        center_df = pd.DataFrame({
            'center': center_counts.index,
            'ip_count': center_counts.values
        })
        center_df.to_csv(center_csv, index=False, encoding='utf-8')

        print("中心分布:")
        for center, count in center_counts.items():
            if pd.isna(center):
                print(f"  未知中心: {count:,} 条")
            else:
                print(f"  {center}: {count:,} 条")
        
        print(f"\n=== 按组织(oa_group)分组统计 ===")
        # 按oa_group分组统计
        oa_group_counts = df['oa_group'].value_counts()

        # 保存组织统计到文件
        oa_group_file = os.path.join(output_dir, "oa_group_statistics.txt")
        with open(oa_group_file, 'w', encoding='utf-8') as f:
            f.write("=== 按组织(oa_group)分组统计 ===\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            for oa_group, count in oa_group_counts.items():
                if pd.isna(oa_group):
                    f.write(f"未知组织: {count:,} 条\n")
                else:
                    f.write(f"{oa_group}: {count:,} 条\n")

        # 保存组织统计CSV
        oa_group_csv = os.path.join(output_dir, "oa_group_statistics.csv")
        oa_group_df = pd.DataFrame({
            'oa_group': oa_group_counts.index,
            'ip_count': oa_group_counts.values
        })
        oa_group_df.to_csv(oa_group_csv, index=False, encoding='utf-8')

        print("组织分布:")
        for oa_group, count in oa_group_counts.items():
            if pd.isna(oa_group):
                print(f"  未知组织: {count:,} 条")
            else:
                print(f"  {oa_group}: {count:,} 条")
        
        # 统计空值情况
        print(f"\n=== 数据完整性统计 ===")

        # 保存数据完整性统计到文件
        integrity_file = os.path.join(output_dir, "data_integrity.txt")
        with open(integrity_file, 'w', encoding='utf-8') as f:
            f.write("=== 数据完整性统计 ===\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("各字段空值统计:\n")
            for col in ['dept', 'center', 'oa_group']:
                null_count = df[col].isnull().sum()
                null_percentage = (null_count / total_ips) * 100
                f.write(f"{col}: {null_count:,} 个空值 ({null_percentage:.2f}%)\n")

        print("各字段空值统计:")
        for col in ['dept', 'center', 'oa_group']:
            null_count = df[col].isnull().sum()
            null_percentage = (null_count / total_ips) * 100
            print(f"  {col}: {null_count:,} 个空值 ({null_percentage:.2f}%)")
        
        # 组合统计：dept + center + oa_group
        print(f"\n=== 部门-中心-组织组合统计 (前20名) ===")
        df['dept_center_group'] = df['dept'].astype(str) + ' - ' + df['center'].astype(str) + ' - ' + df['oa_group'].astype(str)
        combo_counts = df['dept_center_group'].value_counts()

        # 保存组合统计到文件
        combo_file = os.path.join(output_dir, "combination_statistics.txt")
        with open(combo_file, 'w', encoding='utf-8') as f:
            f.write("=== 部门-中心-组织组合统计 ===\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            for combo, count in combo_counts.items():
                f.write(f"{combo}: {count:,} 条\n")

        # 保存组合统计CSV（完整版）
        combo_csv = os.path.join(output_dir, "combination_statistics.csv")
        combo_df = pd.DataFrame({
            'dept_center_group': combo_counts.index,
            'ip_count': combo_counts.values
        })
        combo_df.to_csv(combo_csv, index=False, encoding='utf-8')

        # 将组合统计数据插入到远端数据库
        db_success, db_insert_count = insert_combination_stats_to_db(df)
        if db_success:
            print(f"已将 {db_insert_count} 条组合统计记录插入到远端数据库")
        else:
            print("数据库插入操作失败")

        # 显示前20名
        combo_top20 = combo_counts.head(20)
        for combo, count in combo_top20.items():
            print(f"  {combo}: {count:,} 条")

        # 将所有统计信息追加到总体报告
        with open(summary_file, 'a', encoding='utf-8') as f:
            f.write(f"\n=== 按部门分组统计 Top10 ===\n")
            f.write(f"部门总数: {len(dept_counts)}\n")
            dept_top10 = dept_counts.head(10)
            for i, (dept, count) in enumerate(dept_top10.items(), 1):
                if pd.isna(dept):
                    f.write(f"{i:2d}. 未知部门: {count:,} 条\n")
                else:
                    f.write(f"{i:2d}. {dept}: {count:,} 条\n")

            f.write(f"\n=== 按中心分组统计 Top10 ===\n")
            f.write(f"中心总数: {len(center_counts)}\n")
            center_top10 = center_counts.head(10)
            for i, (center, count) in enumerate(center_top10.items(), 1):
                if pd.isna(center):
                    f.write(f"{i:2d}. 未知中心: {count:,} 条\n")
                else:
                    f.write(f"{i:2d}. {center}: {count:,} 条\n")

            f.write(f"\n=== 按组织分组统计 Top10 ===\n")
            f.write(f"组织总数: {len(oa_group_counts)}\n")
            oa_group_top10 = oa_group_counts.head(10)
            for i, (oa_group, count) in enumerate(oa_group_top10.items(), 1):
                if pd.isna(oa_group):
                    f.write(f"{i:2d}. 未知组织: {count:,} 条\n")
                else:
                    f.write(f"{i:2d}. {oa_group}: {count:,} 条\n")

            f.write(f"\n=== 组合统计 Top10 ===\n")
            f.write(f"组合总数: {len(combo_counts)}\n")
            combo_top10 = combo_counts.head(10)
            for i, (combo, count) in enumerate(combo_top10.items(), 1):
                f.write(f"{i:2d}. {combo}: {count:,} 条\n")

            # 记录数据库插入结果
            f.write(f"\n=== 数据库插入结果 ===\n")
            if db_success:
                f.write(f"成功插入 {db_insert_count} 条组合统计记录到远端数据库 ({DB_TABLE_SCAN_NAME})\n")
            else:
                f.write("数据库插入操作失败\n")

        print(f"\n统计结果已保存到: {output_dir}")
        print("生成的文件:")
        print(f"  - summary_report.txt: 总体统计报告")
        print(f"  - dept_statistics.txt/csv: 部门统计")
        print(f"  - center_statistics.txt/csv: 中心统计")
        print(f"  - oa_group_statistics.txt/csv: 组织统计")
        print(f"  - combination_statistics.txt/csv: 组合统计")
        print(f"  - data_integrity.txt: 数据完整性统计")
        if db_success:
            print(f"  - 远端数据库: 已插入 {db_insert_count} 条记录到 {DB_TABLE_SCAN_NAME} 表")
            
    except Exception as e:
        print(f"错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    csv_file = "temp_ipfo_assets/ip_info.csv"
    
    print("开始分析IP分布...")
    success = analyze_ip_distribution(csv_file)
    
    if success:
        print("\n分析完成！")
    else:
        print("\n分析失败！")
        sys.exit(1)
