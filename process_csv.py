#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件处理脚本
处理big.csv和container.csv文件，提取指定列并合并
"""

import pandas as pd
import os
import re
from collections import Counter

# ==================== 配置区域 ====================
# 文件路径配置 - 根据实际文件位置修改这些路径
BIG_CSV_PATH = '/Users/<USER>/low_sec_detect/temp_ipfo_assets/tegams_ip_info.csv'
CONTAINER_CSV_PATH = '/Users/<USER>/low_sec_detect/temp_ipfo_assets/container.csv'

# container.csv 筛选条件 - 只保留指定 business_id 的记录
CONTAINER_BUSINESS_ID_FILTER = '1000100'
# ================================================

def clean_ip_data(ip_str):
    """清洗IP数据"""
    if pd.isna(ip_str) or ip_str == '':
        return None

    # 转换为字符串
    ip_str = str(ip_str).strip()

    # 1. 去掉 "::ffff:" 前缀
    if ip_str.startswith("::ffff:"):
        ip_str = ip_str[7:]  # 去掉 "::ffff:" (7个字符)

    # 2. 检查是否包含英文字母，如果包含则返回None（表示需要删除）
    if re.search(r'[a-zA-Z]', ip_str):
        return None

    return ip_str

def clean_dataframe_ips(df, ip_column='ip'):
    """清洗DataFrame中的IP数据"""
    print(f"清洗前数据量: {len(df)}")

    # 应用IP清洗函数
    df[ip_column] = df[ip_column].apply(clean_ip_data)

    # 删除IP为None的行（即包含英文字母的IP）
    df_cleaned = df.dropna(subset=[ip_column]).copy()

    print(f"清洗后数据量: {len(df_cleaned)}")
    print(f"删除了 {len(df) - len(df_cleaned)} 条脏数据")

    return df_cleaned

def clean_generated_files():
    """清洗已生成的4个文件"""
    print("开始清洗已生成的文件...")

    # 1. 清洗 ip_info.csv
    print("\n=== 清洗 ip_info.csv ===")
    ip_info_df = pd.read_csv('temp_ip_info_assets/ip_info.csv')
    ip_info_cleaned = clean_dataframe_ips(ip_info_df)
    ip_info_cleaned.to_csv('temp_ip_info_assets/ip_info.csv', index=False, encoding='utf-8')

    # 2. 清洗 conflict_ip.csv
    print("\n=== 清洗 conflict_ip.csv ===")
    conflict_df = pd.read_csv('temp_ip_info_assets/conflict_ip.csv')
    conflict_cleaned = clean_dataframe_ips(conflict_df)
    conflict_cleaned.to_csv('temp_ip_info_assets/conflict_ip.csv', index=False, encoding='utf-8')

    # 3. 重新生成 merged_ip.txt
    print("\n=== 重新生成 merged_ip.txt ===")
    clean_ips = ip_info_cleaned['ip'].dropna().tolist()
    with open('temp_ip_info_assets/merged_ip.txt', 'w', encoding='utf-8') as f:
        for ip in clean_ips:
            f.write(f"{ip}\n")
    print(f"merged_ip.txt 更新完成，包含 {len(clean_ips)} 个IP")

    # 4. 重新生成 conflict_ip.txt
    print("\n=== 重新生成 conflict_ip.txt ===")
    conflict_ips = conflict_cleaned['ip'].dropna().unique().tolist()
    with open('temp_ip_info_assets/conflict_ip.txt', 'w', encoding='utf-8') as f:
        for ip in conflict_ips:
            f.write(f"{ip}\n")
    print(f"conflict_ip.txt 更新完成，包含 {len(conflict_ips)} 个重复IP")

    # 5. 生成只包含IP列的CSV文件
    extract_ip_only_csv(ip_info_cleaned)

    print("\n文件清洗完成！")
    return ip_info_cleaned, conflict_cleaned

def extract_ip_only_csv(ip_info_df):
    """从ip_info.csv提取只包含IP列的CSV文件"""
    print("\n=== 生成只包含IP列的CSV文件 ===")

    # 只保留IP列
    ip_only_df = ip_info_df[['ip']].copy()

    # 保存为ips.csv
    output_path = 'temp_ip_info_assets/ips.csv'
    ip_only_df.to_csv(output_path, index=False, encoding='utf-8')

    print(f"IP列CSV文件生成完成，保存到: {output_path}")
    print(f"包含 {len(ip_only_df)} 条IP记录")

    return ip_only_df

def process_big_csv():
    """处理big.csv文件"""
    print(f"正在处理big.csv文件: {BIG_CSV_PATH}")

    # 尝试不同的编码读取big.csv文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
    big_df = None

    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            big_df = pd.read_csv(BIG_CSV_PATH, encoding=encoding)
            print(f"成功使用编码: {encoding}")
            break
        except UnicodeDecodeError:
            print(f"编码 {encoding} 失败，尝试下一个...")
            continue

    if big_df is None:
        raise Exception("无法读取big.csv文件，所有编码都失败了")
    
    # 检查列名
    print(f"big.csv的列名: {list(big_df.columns)}")
    
    # 提取指定的6列
    required_columns = ['sitename', 'operator', 'oper_bg', 'oper_dept', 'oper_center', 'oper_group']
    
    # 检查所需列是否存在
    missing_columns = [col for col in required_columns if col not in big_df.columns]
    if missing_columns:
        print(f"警告: big.csv中缺少以下列: {missing_columns}")
        # 使用实际存在的列
        available_columns = [col for col in required_columns if col in big_df.columns]
        big_extracted = big_df[available_columns].copy()
    else:
        big_extracted = big_df[required_columns].copy()
    
    # 重命名列
    new_column_names = ['ip', 'user_name', 'bg', 'dept', 'center', 'oa_group']
    # 只重命名实际存在的列
    rename_dict = {}
    for i, old_col in enumerate(big_extracted.columns):
        if i < len(new_column_names):
            rename_dict[old_col] = new_column_names[i]
    
    big_extracted.rename(columns=rename_dict, inplace=True)
    
    # 保存到temp_ip_info_assets目录
    output_path = 'temp_ip_info_assets/big_processed.csv'
    big_extracted.to_csv(output_path, index=False, encoding='utf-8')
    print(f"big.csv处理完成，保存到: {output_path}")
    print(f"处理后的数据形状: {big_extracted.shape}")
    
    return big_extracted

def process_container_csv():
    """处理container.csv文件"""
    print(f"正在处理container.csv文件: {CONTAINER_CSV_PATH}")

    # 尝试不同的编码读取container.csv文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
    container_df = None

    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            container_df = pd.read_csv(CONTAINER_CSV_PATH, encoding=encoding)
            print(f"成功使用编码: {encoding}")
            break
        except UnicodeDecodeError:
            print(f"编码 {encoding} 失败，尝试下一个...")
            continue

    if container_df is None:
        raise Exception("无法读取container.csv文件，所有编码都失败了")

    # 第一步：根据 business_id 进行筛选
    print(f"原始container.csv数据量: {len(container_df)}")
    if 'workload_name' in container_df.columns:
        container_df = container_df[container_df['workload_name'] == CONTAINER_BUSINESS_ID_FILTER].copy()
        print(f"筛选 workload_name = '{CONTAINER_BUSINESS_ID_FILTER}' 后数据量: {len(container_df)}")
    else:
        print("警告: container.csv中没有找到workload_name列，跳过筛选")
    
    # 检查列名
    print(f"container.csv的列名: {list(container_df.columns)}")
    
    # 提取指定的6列
    required_columns = ['pod_ip', 'operator', 'oa_unit_name_1', 'oa_unit_name_2', 'oa_unit_name_3', 'oa_unit_name_4']
    
    # 检查所需列是否存在
    missing_columns = [col for col in required_columns if col not in container_df.columns]
    if missing_columns:
        print(f"警告: container.csv中缺少以下列: {missing_columns}")
        # 使用实际存在的列
        available_columns = [col for col in required_columns if col in container_df.columns]
        container_extracted = container_df[available_columns].copy()
    else:
        container_extracted = container_df[required_columns].copy()
    
    # 重命名列
    new_column_names = ['ip', 'user_name', 'bg', 'dept', 'center', 'oa_group']
    # 只重命名实际存在的列
    rename_dict = {}
    for i, old_col in enumerate(container_extracted.columns):
        if i < len(new_column_names):
            rename_dict[old_col] = new_column_names[i]
    
    container_extracted.rename(columns=rename_dict, inplace=True)
    
    # 保存到temp_ip_info_assets目录
    output_path = 'temp_ip_info_assets/container_processed.csv'
    container_extracted.to_csv(output_path, index=False, encoding='utf-8')
    print(f"container.csv处理完成，保存到: {output_path}")
    print(f"处理后的数据形状: {container_extracted.shape}")
    
    return container_extracted

def merge_files(big_df, container_df):
    """合并两个处理后的文件"""
    print("正在合并文件...")
    
    # 合并两个DataFrame
    merged_df = pd.concat([big_df, container_df], ignore_index=True)
    
    # 保存合并后的文件
    output_path = 'temp_ip_info_assets/ip_info.csv'
    merged_df.to_csv(output_path, index=False, encoding='utf-8')
    print(f"文件合并完成，保存到: {output_path}")
    print(f"合并后的数据形状: {merged_df.shape}")
    
    return merged_df

def extract_ip_list(merged_df):
    """提取IP列表"""
    print("正在提取IP列表...")
    
    # 提取IP列（第一列）
    ip_list = merged_df['ip'].dropna().tolist()
    
    # 保存到merged_ip.txt
    with open('temp_ip_info_assets/merged_ip.txt', 'w', encoding='utf-8') as f:
        for ip in ip_list:
            f.write(f"{ip}\n")
    
    print(f"IP列表提取完成，共{len(ip_list)}个IP")
    return ip_list

def handle_duplicate_ips(merged_df, ip_list):
    """处理重复IP"""
    print("正在处理重复IP...")
    
    # 统计IP出现次数
    ip_counts = Counter(ip_list)
    duplicate_ips = [ip for ip, count in ip_counts.items() if count >= 2]
    
    print(f"发现{len(duplicate_ips)}个重复IP")
    
    if duplicate_ips:
        # 保存重复IP到conflict_ip.txt
        with open('temp_ip_info_assets/conflict_ip.txt', 'w', encoding='utf-8') as f:
            for ip in duplicate_ips:
                f.write(f"{ip}\n")
        
        # 提取包含重复IP的所有记录到conflict_ip.csv
        conflict_records = merged_df[merged_df['ip'].isin(duplicate_ips)]
        conflict_records.to_csv('temp_ip_info_assets/conflict_ip.csv', index=False, encoding='utf-8')
        
        # 从原数据中移除重复IP的记录
        clean_df = merged_df[~merged_df['ip'].isin(duplicate_ips)]
        clean_df.to_csv('temp_ip_info_assets/ip_info.csv', index=False, encoding='utf-8')
        
        # 更新merged_ip.txt，移除重复IP
        clean_ip_list = clean_df['ip'].dropna().tolist()
        with open('temp_ip_info_assets/merged_ip.txt', 'w', encoding='utf-8') as f:
            for ip in clean_ip_list:
                f.write(f"{ip}\n")
        
        print(f"重复IP处理完成:")
        print(f"  - 冲突IP数量: {len(duplicate_ips)}")
        print(f"  - 冲突记录数量: {len(conflict_records)}")
        print(f"  - 清理后记录数量: {len(clean_df)}")
        print(f"  - 清理后IP数量: {len(clean_ip_list)}")
    else:
        print("没有发现重复IP")

def main():
    """主函数"""
    print("开始处理CSV文件...")

    # 确保输出目录存在
    os.makedirs('temp_ip_info_assets', exist_ok=True)

    # 检查是否已有生成的文件，如果有则直接清洗
    if (os.path.exists('temp_ip_info_assets/ip_info.csv') and
        os.path.exists('temp_ip_info_assets/conflict_ip.csv') and
        os.path.exists('temp_ip_info_assets/merged_ip.txt') and
        os.path.exists('temp_ip_info_assets/conflict_ip.txt')):

        print("检测到已生成的文件，直接进行数据清洗...")
        clean_generated_files()

    else:
        print("未检测到已生成的文件，开始完整处理流程...")

        # 处理big.csv
        big_df = process_big_csv()

        # 处理container.csv
        container_df = process_container_csv()

        # 合并文件
        merged_df = merge_files(big_df, container_df)

        # 提取IP列表
        ip_list = extract_ip_list(merged_df)

        # 处理重复IP
        handle_duplicate_ips(merged_df, ip_list)

        print("\n初始处理完成，现在进行数据清洗...")
        clean_generated_files()

    print("\n所有处理完成！")
    print("生成的文件:")
    print("  - temp_ip_info_assets/ip_info.csv (最终的IP信息文件)")
    print("  - temp_ip_info_assets/ips.csv (只包含IP列的CSV文件)")
    print("  - temp_ip_info_assets/merged_ip.txt (去重后的IP列表)")
    print("  - temp_ip_info_assets/conflict_ip.txt (重复的IP列表)")
    print("  - temp_ip_info_assets/conflict_ip.csv (包含重复IP的记录)")

if __name__ == "__main__":
    main()
