#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 process_csv.py 中的配置是否正确
"""

import os

# 从 process_csv.py 导入配置
from process_csv import BIG_CSV_PATH, CONTAINER_CSV_PATH, CONTAINER_BUSINESS_ID_FILTER

def test_config():
    """测试配置"""
    print("=== 配置测试 ===")
    print(f"BIG_CSV_PATH: {BIG_CSV_PATH}")
    print(f"CONTAINER_CSV_PATH: {CONTAINER_CSV_PATH}")
    print(f"CONTAINER_BUSINESS_ID_FILTER: {CONTAINER_BUSINESS_ID_FILTER}")
    
    print("\n=== 文件存在性检查 ===")
    print(f"big.csv 存在: {os.path.exists(BIG_CSV_PATH)}")
    print(f"container.csv 存在: {os.path.exists(CONTAINER_CSV_PATH)}")
    
    if os.path.exists(BIG_CSV_PATH):
        size = os.path.getsize(BIG_CSV_PATH) / (1024*1024*1024)  # GB
        print(f"big.csv 大小: {size:.2f} GB")
    
    if os.path.exists(CONTAINER_CSV_PATH):
        size = os.path.getsize(CONTAINER_CSV_PATH) / (1024*1024)  # MB
        print(f"container.csv 大小: {size:.2f} MB")

if __name__ == "__main__":
    test_config()
