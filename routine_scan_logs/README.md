# 日常扫描日志记录系统

## 概述

这个系统用于记录各个扫描工具的日常运行统计，包括：
1. 每天扫描的IP数
2. 每天扫出并上传至数据库的漏洞数
3. 每日复测时修复的漏洞数

## 目录结构

```
routine_scan_logs/
├── README.md                    # 本说明文档
├── logger.py                    # 核心日志记录模块
├── YYYY-MM-DD/                  # 按日期创建的目录
│   ├── dirlist_scanner.txt      # 目录泄露扫描工具日志
│   ├── bscan_ck.txt            # ClickHouse扫描工具日志
│   ├── bscan_es.txt            # ElasticSearch扫描工具日志
│   ├── redis_scanner.txt       # Redis扫描工具日志
│   └── all.txt                 # 聚合所有工具的汇总日志
└── ...
```

## 工具列表

系统目前支持以下扫描工具：

1. **dirlist_scanner** - 目录泄露扫描（使用TxPortMap）
2. **bscan_ck** - ClickHouse未授权扫描（使用bscan）
3. **bscan_es** - ElasticSearch未授权扫描（使用bscan）
4. **redis_scanner** - Redis未授权扫描（使用fscan）

## 日志格式

### 单个工具日志格式
每个工具的日志文件（如 `dirlist_scanner.txt`）包含该工具的所有扫描记录：

```
[2025-07-22 10:30:15] INITIAL - 扫描IP数: 50000, 发现漏洞: 25, 上传数据库: 23, 修复漏洞: 0
[2025-07-22 14:20:30] AUDIT - 扫描IP数: 0, 发现漏洞: 0, 上传数据库: 0, 修复漏洞: 3
[2025-07-22 15:20:30] AUDIT - 扫描IP数: 0, 发现漏洞: 0, 上传数据库: 0, 修复漏洞: 1
```

### 聚合日志格式
`all.txt` 文件包含当日所有工具的汇总统计：

```
=== 2025-07-22 扫描统计汇总 ===
总扫描IP数: 200000
总发现漏洞: 85
总上传数据库: 78
总修复漏洞: 12

==================================================

=== DIRLIST_SCANNER ===
[2025-07-22 10:30:15] INITIAL - 扫描IP数: 50000, 发现漏洞: 25, 上传数据库: 23, 修复漏洞: 0
[2025-07-22 14:20:30] AUDIT - 扫描IP数: 0, 发现漏洞: 0, 上传数据库: 0, 修复漏洞: 3

=== BSCAN_CK ===
...
```

## 自动化运行

系统已集成到主调度器 `main.py` 中：

- **扫描任务**: 每天凌晨自动执行各工具的初始扫描
- **复测任务**: 每小时执行复测扫描
- **日志汇总**: 每天23:50自动生成当日汇总日志

## 手动操作

### 生成日志汇总
```bash
# 生成今天的汇总
python3 generate_daily_summary.py

# 生成指定日期的汇总
python3 generate_daily_summary.py 2025-07-22
```

### 测试系统
```bash
# 运行测试脚本
python3 test_logging_system.py

# 清理测试数据
python3 test_logging_system.py clean
```

## API 使用

### 记录初始扫描统计
```python
from routine_scan_logs.logger import log_initial_scan

log_initial_scan(
    tool_name="dirlist_scanner",
    scanned_ips=50000,
    found_vulns=25,
    uploaded_to_db=23
)
```

### 记录复测扫描统计
```python
from routine_scan_logs.logger import log_audit_scan

log_audit_scan(
    tool_name="dirlist_scanner",
    checked_vulns=100,
    fixed_vulns=5
)
```

### 生成日志汇总
```python
from routine_scan_logs.logger import generate_daily_summary

total_stats = generate_daily_summary()
print(total_stats)
```

## 数据统计说明

- **扫描IP数**: 本次扫描涉及的IP地址总数
- **发现漏洞**: 本次扫描新发现的漏洞数量
- **上传数据库**: 成功写入数据库的漏洞数量
- **修复漏洞**: 复测时发现已修复的漏洞数量

## 注意事项

1. 日志文件按日期自动创建，无需手动管理
2. 系统会自动处理编码问题，支持中文内容
3. 聚合日志会在每次调用时重新生成，确保数据最新
4. 所有时间戳使用本地时间格式
5. 日志文件采用追加模式，不会覆盖历史记录

## 故障排除

如果遇到问题，请检查：

1. 确保 `routine_scan_logs` 目录有写入权限
2. 检查 Python 路径是否正确设置
3. 运行测试脚本验证系统功能
4. 查看具体的错误信息进行调试
