#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict


@dataclass
class ScanStats:
    """扫描统计数据结构"""
    tool_name: str
    scan_type: str  # "initial" 或 "audit"
    timestamp: str
    scanned_ips: int = 0
    found_vulnerabilities: int = 0
    fixed_vulnerabilities: int = 0
    uploaded_to_db: int = 0
    
    def to_dict(self) -> Dict:
        return asdict(self)


class RoutineScanLogger:
    """日常扫描日志记录器"""
    
    def __init__(self, base_dir: str = "routine_scan_logs"):
        self.base_dir = base_dir
        self.ensure_base_dir()
    
    def ensure_base_dir(self):
        """确保基础日志目录存在"""
        if not os.path.exists(self.base_dir):
            os.makedirs(self.base_dir, exist_ok=True)
    
    def get_today_dir(self) -> str:
        """获取今天的日志目录路径"""
        today = datetime.now().strftime("%Y-%m-%d")
        today_dir = os.path.join(self.base_dir, today)
        if not os.path.exists(today_dir):
            os.makedirs(today_dir, exist_ok=True)
        return today_dir
    
    def get_tool_log_path(self, tool_name: str) -> str:
        """获取指定工具的日志文件路径"""
        today_dir = self.get_today_dir()
        return os.path.join(today_dir, f"{tool_name}.txt")
    
    def get_all_log_path(self) -> str:
        """获取聚合日志文件路径"""
        today_dir = self.get_today_dir()
        return os.path.join(today_dir, "all.txt")
    
    def log_scan_stats(self, stats: ScanStats):
        """记录扫描统计信息到工具专用日志文件"""
        log_path = self.get_tool_log_path(stats.tool_name)
        
        # 格式化日志条目
        log_entry = self._format_log_entry(stats)
        
        # 追加到工具日志文件
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
        
        print(f"信息: 已记录 {stats.tool_name} 扫描统计到 {os.path.basename(log_path)}")
    
    def _format_log_entry(self, stats: ScanStats) -> str:
        """格式化日志条目"""
        return (
            f"[{stats.timestamp}] {stats.scan_type.upper()} - "
            f"扫描IP数: {stats.scanned_ips}, "
            f"发现漏洞: {stats.found_vulnerabilities}, "
            f"上传数据库: {stats.uploaded_to_db}, "
            f"修复漏洞: {stats.fixed_vulnerabilities}"
        )
    
    def generate_aggregated_log(self):
        """生成聚合日志文件 all.txt"""
        today_dir = self.get_today_dir()
        all_log_path = self.get_all_log_path()
        
        # 收集所有工具的日志
        tool_logs = []
        total_stats = {
            'scanned_ips': 0,
            'found_vulnerabilities': 0,
            'uploaded_to_db': 0,
            'fixed_vulnerabilities': 0
        }
        
        # 扫描今天目录中的所有工具日志文件
        for filename in os.listdir(today_dir):
            if filename.endswith('.txt') and filename != 'all.txt':
                tool_name = filename[:-4]  # 移除 .txt 后缀
                tool_log_path = os.path.join(today_dir, filename)
                
                if os.path.exists(tool_log_path):
                    with open(tool_log_path, 'r', encoding='utf-8') as f:
                        tool_content = f.read().strip()
                        if tool_content:
                            tool_logs.append(f"=== {tool_name.upper()} ===")
                            tool_logs.append(tool_content)
                            tool_logs.append("")  # 空行分隔
                            
                            # 解析并累加统计数据
                            tool_stats = self._parse_tool_stats(tool_content)
                            for key in total_stats:
                                total_stats[key] += tool_stats.get(key, 0)
        
        # 生成聚合日志内容
        today = datetime.now().strftime("%Y-%m-%d")
        aggregated_content = []
        
        # 总体统计（放在最前面）
        aggregated_content.append(f"=== {today} 扫描统计汇总 ===")
        aggregated_content.append(f"总扫描IP数: {total_stats['scanned_ips']}")
        aggregated_content.append(f"总发现漏洞: {total_stats['found_vulnerabilities']}")
        aggregated_content.append(f"总上传数据库: {total_stats['uploaded_to_db']}")
        aggregated_content.append(f"总修复漏洞: {total_stats['fixed_vulnerabilities']}")
        aggregated_content.append("")
        aggregated_content.append("=" * 50)
        aggregated_content.append("")
        
        # 各工具详细日志
        aggregated_content.extend(tool_logs)
        
        # 写入聚合日志文件
        with open(all_log_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(aggregated_content))
        
        print(f"信息: 已生成聚合日志 {os.path.basename(all_log_path)}")
        return total_stats
    
    def _parse_tool_stats(self, content: str) -> Dict[str, int]:
        """从工具日志内容中解析统计数据"""
        stats = {
            'scanned_ips': 0,
            'found_vulnerabilities': 0,
            'uploaded_to_db': 0,
            'fixed_vulnerabilities': 0
        }
        
        lines = content.split('\n')
        for line in lines:
            if '扫描IP数:' in line:
                try:
                    # 解析格式: "扫描IP数: 123, 发现漏洞: 45, 上传数据库: 30, 修复漏洞: 10"
                    parts = line.split(', ')
                    for part in parts:
                        if '扫描IP数:' in part:
                            stats['scanned_ips'] += int(part.split(':')[1].strip())
                        elif '发现漏洞:' in part:
                            stats['found_vulnerabilities'] += int(part.split(':')[1].strip())
                        elif '上传数据库:' in part:
                            stats['uploaded_to_db'] += int(part.split(':')[1].strip())
                        elif '修复漏洞:' in part:
                            stats['fixed_vulnerabilities'] += int(part.split(':')[1].strip())
                except (ValueError, IndexError) as e:
                    print(f"警告: 解析日志行时出错: {line}, 错误: {e}")
                    continue
        
        return stats
    
    def get_daily_summary(self, date: Optional[str] = None) -> Dict:
        """获取指定日期的扫描汇总（默认今天）"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        
        date_dir = os.path.join(self.base_dir, date)
        if not os.path.exists(date_dir):
            return {"error": f"日期 {date} 的日志目录不存在"}
        
        all_log_path = os.path.join(date_dir, "all.txt")
        if not os.path.exists(all_log_path):
            # 如果聚合日志不存在，尝试生成
            self.generate_aggregated_log()
        
        if os.path.exists(all_log_path):
            with open(all_log_path, 'r', encoding='utf-8') as f:
                content = f.read()
                return {"date": date, "content": content}
        else:
            return {"error": f"无法生成或读取 {date} 的聚合日志"}


# 全局日志记录器实例
routine_logger = RoutineScanLogger()


def log_initial_scan(tool_name: str, scanned_ips: int, found_vulns: int, uploaded_to_db: int):
    """记录初始扫描统计"""
    stats = ScanStats(
        tool_name=tool_name,
        scan_type="initial",
        timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        scanned_ips=scanned_ips,
        found_vulnerabilities=found_vulns,
        uploaded_to_db=uploaded_to_db,
        fixed_vulnerabilities=0
    )
    routine_logger.log_scan_stats(stats)


def log_audit_scan(tool_name: str, checked_vulns: int, fixed_vulns: int):
    """记录复测扫描统计"""
    stats = ScanStats(
        tool_name=tool_name,
        scan_type="audit",
        timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        scanned_ips=0,  # 复测不算新扫描的IP
        found_vulnerabilities=0,  # 复测不算新发现的漏洞
        uploaded_to_db=0,  # 复测不上传新数据
        fixed_vulnerabilities=fixed_vulns
    )
    routine_logger.log_scan_stats(stats)


def generate_daily_summary():
    """生成当日汇总日志"""
    return routine_logger.generate_aggregated_log()
