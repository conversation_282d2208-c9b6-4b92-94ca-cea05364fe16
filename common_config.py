#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import pymysql

# ==============================================================================
# 全局共享配置 (Global Shared Configuration)
# ==============================================================================

# --- 数据库配置 ---
# DB_CONFIG = {
#     'host': '*************',
#     'user': 'hunyuan_staff',
#     'password': 'hunyuan_staff0820',
#     'database': 'hunyuan_sec_data',
#     'charset': 'utf8mb4'
# }

# debug DB
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'Tegsec_09040',
    'database': 'web_scan',
    'charset': 'utf8mb4'
}

DB_TABLE_NAME = 'dir_list_vul'
DB_TABLE_SCAN_NAME = 'dir_list_scan'


# --- 共享资产路径 ---
# 定义项目根目录的绝对路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 共享IP资产文件的目录路径
IP_ASSETS_DIR = os.path.join(PROJECT_ROOT, 'ipinfo_assets')

# 具体的IP资产文件路径
IP_INFO_PATH = os.path.join(IP_ASSETS_DIR, 'merged_ip.txt')
CONFLICT_IP_INFO_PATH = os.path.join(IP_ASSETS_DIR, 'merged_conflict_ip.txt')
IP_INFO_CSV_PATH = os.path.join(IP_ASSETS_DIR, 'ip_info.csv')
CONFLICT_IP_INFO_CSV_PATH = os.path.join(IP_ASSETS_DIR, 'conflict_ip_info.csv') 

# 选择具体要测试ip数据集
SELECT_IP_FILE = IP_INFO_PATH
