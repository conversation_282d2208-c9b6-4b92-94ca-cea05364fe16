#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 统一导入所有模块的函数
from dirlist_scanner.main import run_initial_scan as dir_initial_scan, run_audit_scan as dir_audit_scan
from bscan_ck.main import (
    run_initial_scan as ck_initial_scan, 
    run_audit_scan as ck_audit_scan,
    process_latest_initial_report as ck_process_initial,
    process_latest_audit_report as ck_process_audit
)
from bscan_es.main import (
    run_initial_scan as es_initial_scan,
    run_audit_scan as es_audit_scan,
    process_latest_initial_report as es_process_initial,
    process_latest_audit_report as es_process_audit
)
from bscan_common.main import (
    run_initial_scan as common_initial_scan,
    run_audit_scan as common_audit_scan,
    process_latest_initial_report as common_process_initial,
    process_latest_audit_report as common_process_audit
)
from redis_scanner.main import (
    run_initial_scan as redis_initial_scan,
    run_audit_scan as redis_audit_scan,
    process_latest_initial_report as redis_process_initial,
    process_latest_audit_report as redis_process_audit
)

# 新的嵌套任务结构
TOOLS_MENU = {
    '1': {
        'name': '文件目录泄露 (dirlist_scanner)',
        'tasks': {
            '1': ('扫描并处理原始报告', dir_initial_scan),
            '2': ('扫描并处理复测报告', dir_audit_scan),
        }
    },
    '2': {
        'name': 'ClickHouse (bscan_ck)',
        'tasks': {
            '1': ('扫描并处理原始报告', ck_initial_scan),
            '2': ('扫描并处理复测报告', ck_audit_scan),
            '3': ('仅处理最新的原始报告', ck_process_initial),
            '4': ('仅处理最新的复测报告', ck_process_audit),
        }
    },
    '3': {
        'name': 'ElasticSearch (bscan_es)',
        'tasks': {
            '1': ('扫描并处理原始报告', es_initial_scan),
            '2': ('扫描并处理复测报告', es_audit_scan),
            '3': ('仅处理最新的原始报告', es_process_initial),
            '4': ('仅处理最新的复测报告', es_process_audit),
        }
    },
    '4': {
        'name': '常规端口扫描 (bscan_common)',
        'tasks': {
            '1': ('扫描并处理原始报告', common_initial_scan),
            '2': ('扫描并处理复测报告', common_audit_scan),
            '3': ('仅处理最新的原始报告', common_process_initial),
            '4': ('仅处理最新的复测报告', common_process_audit),
        }
    },
    '5': {
        'name': 'Redis (redis_scanner)',
        'tasks': {
            '1': ('扫描并处理原始报告', redis_initial_scan),
            '2': ('扫描并处理复测报告', redis_audit_scan),
            '3': ('仅处理最新的原始报告', redis_process_initial),
            '4': ('仅处理最新的复测报告', redis_process_audit),
        }
    }
}

def display_tools_menu():
    """显示第一级菜单：选择工具"""
    print("\n=====================================")
    print("  手动调试执行器 (Debug Runner)")
    print("=====================================")
    print("第一步: 请选择要使用的工具:")
    for key, tool_info in TOOLS_MENU.items():
        print(f"{key}. {tool_info['name']}")
    print("\n0. 退出 (Exit)")
    print("-------------------------------------")

def display_tasks_menu(tool_info):
    """显示第二级菜单：选择特定工具的任务"""
    print(f"\n--- 工具: {tool_info['name']} ---")
    print("第二步: 请选择要执行的任务:")
    for key, (description, _) in tool_info['tasks'].items():
        print(f"  {key}. {description}")
    print("\n  9. 返回上一级 (Back)")
    print("  0. 退出 (Exit)")
    print("-------------------------------------")

def execute_task(task_function, description):
    """执行选定的任务并处理异常"""
    print(f"\n>>> 准备执行任务: {description}...\n")
    try:
        task_function()
        print(f"\n<<< 任务: {description} 执行完成。")
    except Exception as e:
        print(f"\nXXX 任务执行出错: {description} XXX")
        print(f"错误详情: {e}")

def main():
    """主函数，处理两级菜单的循环"""
    while True:
        display_tools_menu()
        tool_choice = input("请输入工具编号: ").strip()

        if tool_choice == '0':
            print("程序退出。")
            break

        tool_info = TOOLS_MENU.get(tool_choice)
        if not tool_info:
            print("\n无效的工具编号，请重试。")
            continue

        while True:
            display_tasks_menu(tool_info)
            task_choice = input("请输入任务编号: ").strip()

            if task_choice == '9':
                break  # 返回工具菜单
            
            if task_choice == '0':
                print("程序退出。")
                return # 直接退出整个程序

            task_details = tool_info['tasks'].get(task_choice)
            if not task_details:
                print("\n无效的任务编号，请重试。")
                continue
            
            description, task_function = task_details
            full_description = f"[{tool_info['name']}] -> {description}"
            execute_task(task_function, full_description)

if __name__ == "__main__":
    main() 