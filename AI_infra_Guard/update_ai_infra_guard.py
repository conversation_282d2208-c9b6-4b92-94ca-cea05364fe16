#!/usr/bin/env python3

import os
import requests
import re
import json
import time
import hashlib
from datetime import datetime, timezone
from urllib.parse import urlparse
import zipfile


# ===== 配置区域 =====
REPO_OWNER = "Tencent"  # 仓库所有者
REPO_NAME = "AI-Infra-Guard"  # 仓库名称
TARGET_DIR = "/Users/<USER>/low_sec_detect"  # 存放可执行文件的目录（必须替换）
PLATFORM_FILTER = "linux"  # 平台过滤（可选：linux, windows, darwin等）
ARCH_FILTER = "amd64"  # 架构过滤（可选：x86_64, arm64等）
CHECK_INTERVAL = 3600  # 检查间隔（秒），默认1小时
GITHUB_TOKEN = "****************************************"  # 可选：GitHub访问令牌（提高API限制）
# ===================

# GitHub API 端点
RELEASES_API = f"https://api.github.com/repos/{REPO_OWNER}/{REPO_NAME}/releases/latest"
STATE_FILE = os.path.join(TARGET_DIR, ".release_monitor_state.json")


def get_latest_release():
    """获取最新发布版本信息"""
    headers = {"Accept": "application/vnd.github.v3+json"}
    if GITHUB_TOKEN:
        headers["Authorization"] = f"token {GITHUB_TOKEN}"

    try:
        response = requests.get(RELEASES_API, headers=headers)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"[错误] 获取发布信息失败: {str(e)}")
        return None


def download_file(url, filepath):
    """下载文件并保存到指定路径"""
    headers = {}
    if GITHUB_TOKEN:
        headers["Authorization"] = f"token {GITHUB_TOKEN}"

    try:
        response = requests.get(url, headers=headers, stream=True)
        response.raise_for_status()

        # 创建目标目录
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # 写入文件
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        return True
    except Exception as e:
        print(f"[错误] 下载文件失败: {str(e)}")
        return False


def calculate_sha256(filepath):
    """计算文件的SHA256哈希值"""
    sha256 = hashlib.sha256()
    with open(filepath, 'rb') as f:
        while True:
            data = f.read(65536)  # 64KB chunks
            if not data:
                break
            sha256.update(data)
    return sha256.hexdigest()


def should_download_asset(asset_name):
    """判断是否应该下载该资源文件（基于配置的过滤条件）"""
    name_lower = asset_name.lower()

    # 应用平台过滤
    if PLATFORM_FILTER and PLATFORM_FILTER.lower() not in name_lower:
        return False

    # 应用架构过滤
    if ARCH_FILTER and ARCH_FILTER.lower() not in name_lower:
        return False

    # # 常见可执行文件扩展名
    # executable_ext = ('.exe', '.bin', '.app', '.sh', '.run', '')
    # if not any(name_lower.endswith(ext) for ext in executable_ext):
    #     return False
    #
    # # 跳过调试文件、符号文件等
    # exclude_terms = ('debug', 'symbol', 'dbg', 'test', 'source')
    # if any(term in name_lower for term in exclude_terms):
    #     return False

    return True


def save_state(release_data):
    """保存当前状态"""
    state = {
        'version': release_data['tag_name'],
        'published_at': release_data['published_at'],
        'assets': {},
        'last_checked': datetime.now(timezone.utc).isoformat()
    }

    for asset in release_data.get('assets', []):
        state['assets'][asset['name']] = {
            'download_url': asset['browser_download_url'],
            'size': asset['size'],
            'download_count': asset['download_count']
        }

    with open(STATE_FILE, 'w') as f:
        json.dump(state, f, indent=2)


def load_state():
    """加载上次检查状态"""
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, 'r') as f:
                return json.load(f)
        except:
            return None
    return None


def main():
    asset_zip = None
    print(f"监控仓库发布版本: {REPO_OWNER}/{REPO_NAME}")
    print(f"目标目录: {TARGET_DIR}")
    print(f"平台过滤: {PLATFORM_FILTER}")
    print(f"架构过滤: {ARCH_FILTER}")
    print("=" * 50)

    # 确保目标目录存在
    os.makedirs(TARGET_DIR, exist_ok=True)

    last_state = load_state()


    print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 检查新发布版本...")

    # 获取最新发布信息
    release = get_latest_release()

    if not release:
        print("获取发布信息失败，稍后重试...")
        return False


    # 提取基本信息
    version = release['tag_name']
    published_at = release['published_at']
    assets = release.get('assets', [])

    print(f"最新版本: {version} | 发布时间: {published_at}")
    print(f"找到 {len(assets)} 个资源文件")

    # 首次运行或发现新版本
    if not last_state or last_state.get('version') != version:
        if not last_state:
            print("初始化监控器...")
        else:
            print(f"发现新版本! 旧版本: {last_state['version']} -> 新版本: {version}")

        # 下载符合条件的资源文件
        downloaded_count = 0
        for asset in assets:
            asset_name = asset['name']
            download_url = asset['browser_download_url']

            if should_download_asset(asset_name):
                print(f"  - 下载: {asset_name} ({asset['size'] / 1024 / 1024:.2f} MB)")
                filepath = os.path.join(TARGET_DIR, asset_name)
                asset_zip = filepath
                if download_file(download_url, filepath):
                    # 验证文件大小
                    if os.path.getsize(filepath) != asset['size']:
                        print(f"  警告: 文件大小不匹配 ({os.path.getsize(filepath)} vs {asset['size']})")
                    else:
                        # 计算哈希值（可选）
                        file_hash = calculate_sha256(filepath)
                        print(f"  下载完成 | SHA256: {file_hash[:16]}...")
                        downloaded_count += 1

        print(f"共下载 {downloaded_count} 个文件")

        # 保存新状态
        save_state(release)
        last_state = load_state()
    else:
        print("当前已是最新版本")


    if asset_zip:
        print(asset_zip)
        # 解压到bin执行文件
        zip_path = asset_zip
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # 解压文件到指定目录
            zip_ref.extractall('./bin')
            print("ZIP文件解压完成")
            return None
    # 增加可执行权限
    os.system("chmod +x ./bin/ai-infra-guard")
    return None
    # print(f"下次检查: {datetime.now().strftime('%H:%M:%S')}")
    # time.sleep(CHECK_INTERVAL)


if __name__ == "__main__":
    main()