#!/usr/bin/env python3
import subprocess
import os
import sys
from datetime import datetime
import logging

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)


def execute_scan():
    try:
        # 创建带日期的日志目录
        date_str = datetime.now().strftime("%Y-%m-%d")
        log_dir = os.path.abspath(f"./AI-Infra-Guard_log/{date_str}")
        os.makedirs(log_dir, exist_ok=True)

        # 构造日志文件名（含时间戳）
        log_file = os.path.join(
            log_dir,
            f"scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        )

        # 准备执行命令
        cmd = ["./bin/ai-infra-guard", "scan", "--file", "../temp_ipfo_assets/ips.csv"]
        logger.info("执行命令: %s", " ".join(cmd))

        # 实时执行并记录日志
        start_time = datetime.now()

        # 打开日志文件
        with open(log_file, "w", encoding="utf-8") as log_fd:
            # 写入开始标记
            log_fd.write(f"[Scan Start] {start_time.isoformat()}\n")
            log_fd.flush()

            # 启动子进程
            with subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    # text=True,  # 文本模式输出
                    bufsize=1,  # 行缓冲
                    universal_newlines=True
            ) as proc:
                # 实时处理输出
                while True:
                    line = proc.stdout.readline()
                    if not line:
                        if proc.poll() is not None:
                            break
                        continue

                    # 同时写入日志文件和控制台
                    log_fd.write(line)
                    sys.stdout.write(line)
                    log_fd.flush()

            # 等待进程结束
            return_code = proc.returncode

        end_time = datetime.now()

        # 打开日志文件追加结束标记
        with open(log_file, "a", encoding="utf-8") as log_fd:
            log_fd.write(f"\n[Scan End] {end_time.isoformat()}\n")
            log_fd.write(f"Exit Code: {return_code}\n")
            duration = end_time - start_time
            log_fd.write(f"Duration: {duration.total_seconds():.2f} seconds\n")

        logger.info("扫描完成 (返回码: %d)", return_code)
        logger.info("日志文件: %s", log_file)
        logger.info("耗时: %.2f 秒", duration.total_seconds())

        if return_code != 0:
            logger.error("命令执行异常，请检查日志")
            sys.exit(return_code)

    except Exception as e:
        logger.exception("执行扫描时出错: %s", str(e))
        sys.exit(1)


if __name__ == "__main__":
    execute_scan()