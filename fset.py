# 定义读取文件的函数
def read_ips(file_path):
    with open(file_path, 'r') as file:
        ips = set(line.strip() for line in file.readlines())
    return ips

# 定义写入文件的函数
def write_ips(file_path, ips):
    with open(file_path, 'a') as file:  # 'a' 表示追加内容到文件
        for ip in ips:
            file.write(ip + '\n')

# 读取文件1和文件2
file1_ips = read_ips('ipinfo_assets/2df65389.txt')
file2_ips = read_ips('ipinfo_assets/merged_ip.txt')

# 计算交集
intersection = file1_ips & file2_ips

# 计算 file1 中除了交集外的 IP
file1_difference = file1_ips - intersection

# 将 file1 中不在交集中的 IP 写入到 file2 中
write_ips('ipinfo_assets/merged_ip.txt', file1_difference)

# 输出结果
if file1_difference:
    print(f"共添加 {len(file1_difference)} 个 IP 地址到 file2。")
else:
    print("没有需要添加的 IP 地址。")
