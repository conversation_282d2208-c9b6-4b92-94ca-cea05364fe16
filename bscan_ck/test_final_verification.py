#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试：模拟完整的数据库上传流程
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from bscan_ck.database.models import Vulnerability

def test_database_upload_simulation():
    """模拟完整的数据库上传流程，验证风险等级调整功能"""
    print("=" * 80)
    print("最终验证测试：模拟完整的数据库上传流程")
    print("=" * 80)
    
    # 模拟从扫描报告解析出的漏洞列表
    mock_vulnerabilities = ["************:8123", "**********:8123", "***********:8123"]
    
    # 模拟从IP信息CSV文件加载的负责人信息
    mock_ip_owner_info = {
        "************": {
            "user_name": "testuser",  # 在名字列表中
            "bg": "技术部",
            "dept": "基础架构",
            "center": "数据中心",
            "oa_group": "test_team"
        },
        "**********": {
            "user_name": "prod_manager",  # 不在名字列表中
            "bg": "技术部",
            "dept": "基础架构",
            "center": "数据中心",
            "oa_group": "prod_team"
        },
        "***********": {
            "user_name": "dev_team",  # 在名字列表中
            "bg": "研发部",
            "dept": "开发团队",
            "center": "研发中心",
            "oa_group": "dev_group"
        }
    }
    
    print(f"\n模拟场景：")
    print(f"发现漏洞: {len(mock_vulnerabilities)} 个")
    print(f"IP负责人信息: {len(mock_ip_owner_info)} 条")
    
    print(f"\n开始模拟数据库上传流程...")
    print("-" * 80)
    
    uploaded_count = 0
    results = []
    
    # 模拟 process_bscan_report_to_db 函数中的核心逻辑
    for host_port in mock_vulnerabilities:
        try:
            host, port_str = host_port.split(":")
            port = int(port_str)
            url = f"http://{host}:{port}"
            
            # 创建漏洞对象
            vuln = Vulnerability(host=host, port=port, url=url)
            
            # 获取负责人信息
            owner = mock_ip_owner_info.get(host, {})
            
            print(f"\n处理漏洞: {host}:{port}")
            print(f"  负责人信息: {owner}")
            
            # 模拟数据库插入前的数据准备（这会触发风险等级调整）
            db_tuple = vuln.to_db_tuple(owner)
            
            # 模拟数据库插入操作（实际项目中会调用 db_handler.add_vulnerability）
            print(f"  ✓ 准备插入数据库")
            print(f"    最终风险等级: {vuln.risk_level} ({'高危' if vuln.risk_level == '1' else '中危'})")
            print(f"    用户名: {owner.get('user_name', '未知')}")
            print(f"    数据库元组长度: {len(db_tuple)} 个字段")
            
            results.append({
                'host': host,
                'port': port,
                'user_name': owner.get('user_name', ''),
                'original_risk_level': '1',  # 默认高危
                'final_risk_level': vuln.risk_level,
                'adjusted': vuln.risk_level == '2'
            })
            
            uploaded_count += 1
            
        except Exception as e:
            print(f"  ✗ 处理失败: {host_port} - {e}")
    
    print("\n" + "=" * 80)
    print("处理结果汇总:")
    print("=" * 80)
    
    print(f"总计处理漏洞: {len(results)} 个")
    print(f"成功准备上传: {uploaded_count} 个")
    
    adjusted_count = sum(1 for r in results if r['adjusted'])
    unchanged_count = len(results) - adjusted_count
    
    print(f"风险等级调整: {adjusted_count} 个")
    print(f"保持原等级: {unchanged_count} 个")
    
    print(f"\n详细结果:")
    for result in results:
        status = "已调整为中危" if result['adjusted'] else "保持高危"
        print(f"  - {result['host']}:{result['port']} (用户: {result['user_name']}) -> {status}")
    
    print(f"\n风险等级调整的漏洞详情:")
    for result in results:
        if result['adjusted']:
            print(f"  - {result['host']}:{result['port']}")
            print(f"    负责人: {result['user_name']}")
            print(f"    调整原因: 用户名在name_list.txt中")
            print(f"    风险等级: {result['original_risk_level']} -> {result['final_risk_level']}")
    
    print("\n" + "=" * 80)
    print("最终验证测试完成 - 功能正常工作！")
    print("=" * 80)

if __name__ == "__main__":
    test_database_upload_simulation()
