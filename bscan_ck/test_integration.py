#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试：模拟完整的漏洞处理和数据库上传流程
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from bscan_ck.database.models import Vulnerability

def simulate_vulnerability_processing():
    """模拟基于user_name的漏洞处理流程"""
    print("=" * 70)
    print("集成测试：模拟基于user_name的完整漏洞处理流程")
    print("=" * 70)

    # 模拟从扫描报告中解析出的漏洞列表和对应的负责人信息
    mock_scan_data = [
        {
            "host_port": "test-server.company.com:8123",
            "owner_info": {
                "user_name": "testuser",  # 在名字列表中
                "bg": "技术部",
                "dept": "基础架构",
                "center": "数据中心",
                "oa_group": "test_team"
            }
        },
        {
            "host_port": "production-db.company.com:8123",
            "owner_info": {
                "user_name": "prod_admin",  # 不在名字列表中
                "bg": "技术部",
                "dept": "基础架构",
                "center": "数据中心",
                "oa_group": "prod_team"
            }
        },
        {
            "host_port": "dev-clickhouse.internal:8123",
            "owner_info": {
                "user_name": "dev_team",  # 在名字列表中
                "bg": "研发部",
                "dept": "开发团队",
                "center": "研发中心",
                "oa_group": "dev_group"
            }
        },
        {
            "host_port": "*************:8123",
            "owner_info": {
                "user_name": "测试用户",  # 中文名字，在名字列表中
                "bg": "技术部",
                "dept": "测试部门",
                "center": "数据中心",
                "oa_group": "test_group"
            }
        },
        {
            "host_port": "staging-env.company.com:8123",
            "owner_info": {
                "user_name": "staging_user",  # 在名字列表中
                "bg": "技术部",
                "dept": "基础架构",
                "center": "数据中心",
                "oa_group": "staging_team"
            }
        },
        {
            "host_port": "live-database.company.com:8123",
            "owner_info": {
                "user_name": "live_admin",  # 不在名字列表中
                "bg": "技术部",
                "dept": "基础架构",
                "center": "数据中心",
                "oa_group": "live_team"
            }
        }
    ]

    print(f"\n模拟扫描结果，发现 {len(mock_scan_data)} 个漏洞:")
    for data in mock_scan_data:
        print(f"  - {data['host_port']} (负责人: {data['owner_info']['user_name']})")

    print("\n开始处理漏洞并准备上传数据库...")
    print("-" * 70)

    processed_vulns = []

    for data in mock_scan_data:
        try:
            host_port = data["host_port"]
            owner_info = data["owner_info"]

            host, port_str = host_port.split(":")
            port = int(port_str)
            url = f"http://{host}:{port}"

            # 创建漏洞对象
            vuln = Vulnerability(host=host, port=port, url=url)

            # 生成数据库插入元组（这会触发基于user_name的风险等级检查）
            db_tuple = vuln.to_db_tuple(owner_info)

            processed_vulns.append({
                'host': host,
                'port': port,
                'user_name': owner_info['user_name'],
                'risk_level': vuln.risk_level,
                'risk_desc': vuln.risk_desc,
                'db_tuple': db_tuple
            })

            print(f"✓ 处理完成: {host}:{port}")
            print(f"  负责人: {owner_info['user_name']}")
            print(f"  风险等级: {vuln.risk_level} ({'高危' if vuln.risk_level == '1' else '中危'})")
            print(f"  风险描述: {vuln.risk_desc[:60]}...")
            print()

        except Exception as e:
            print(f"✗ 处理失败: {data['host_port']} - {e}")
    
    print("-" * 70)
    print("处理结果统计:")

    high_risk_count = sum(1 for v in processed_vulns if v['risk_level'] == '1')
    medium_risk_count = sum(1 for v in processed_vulns if v['risk_level'] == '2')

    print(f"总计处理漏洞: {len(processed_vulns)} 个")
    print(f"高危漏洞: {high_risk_count} 个")
    print(f"中危漏洞: {medium_risk_count} 个")

    print("\n中危漏洞详情（基于user_name匹配）:")
    for vuln in processed_vulns:
        if vuln['risk_level'] == '2':
            print(f"  - {vuln['host']}:{vuln['port']} (负责人: {vuln['user_name']}) -> 已调整为中危")

    print("\n高危漏洞详情:")
    for vuln in processed_vulns:
        if vuln['risk_level'] == '1':
            print(f"  - {vuln['host']}:{vuln['port']} (负责人: {vuln['user_name']}) -> 保持高危")

    print("\n" + "=" * 70)
    print("集成测试完成")
    print("=" * 70)

if __name__ == "__main__":
    simulate_vulnerability_processing()
