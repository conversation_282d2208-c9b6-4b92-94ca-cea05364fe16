# database/models.py
from dataclasses import dataclass, field
from datetime import datetime
from typing import List
from ..config import settings
from ..core.utils import load_name_list

@dataclass
class Vulnerability:
    """用于表示一个ClickHouse未授权访问漏洞的数据模型。"""
    host: str
    port: int
    url: str

    # --- 从 settings.py 加载的固定信息 ---
    risk_type: int = field(default=settings.CLICKHOUSE_VULN_DETAILS['risk_id'], init=False)
    risk_name: str = field(default=settings.CLICKHOUSE_VULN_DETAILS['risk_type'], init=False)
    risk_level: str = field(default=settings.CLICKHOUSE_VULN_DETAILS['risk_level'], init=False)
    risk_tag: str = field(default="未授权访问", init=False) # 假设一个tag
    create_by: str = field(default=settings.VULN_CREATOR, init=False)
    risk_status: str = field(default="2", init=False) # 活跃状态

    # --- 动态生成的字段 ---
    date_key: str = field(init=False)
    create_time: str = field(init=False)
    case_content: str = field(init=False)
    guide_link: str = field(default="https://iwiki.woa.com/p/4008446861", init=False)

    @property
    def risk_desc(self) -> str:
        risk_level_text = "高危" if self.risk_level == "1" else "中危"
        return f"{risk_level_text}：发现ClickHouse数据库存在未授权访问漏洞，位于 {self.host}:{self.port}。攻击者可能利用此漏洞直接访问、查询、修改或删除敏感数据，对业务造成严重影响。修复指引：为ClickHouse服务添加身份验证并限制公网访问。"

    def __post_init__(self):
        """在对象创建后自动填充动态字段"""
        now = datetime.now()
        self.date_key = now.strftime("%Y%m%d%H")
        self.create_time = now.strftime("%Y-%m-%d %H:%M:%S")
        self.case_content = ""

    def adjust_risk_level_by_user_name(self, user_name: str):
        """
        根据name_list.txt中的名字列表检查user_name字段，如果匹配则调整为中危。

        :param user_name: 从owner_info中获取的用户名
        """
        try:
            # 如果user_name为空，不做任何调整
            if not user_name or not user_name.strip():
                return

            # 加载名字列表
            name_list = load_name_list(settings.NAME_LIST_FILE)
            if not name_list:
                return  # 如果名字列表为空，不做任何调整

            # 检查user_name是否与名字列表中的任何一个名字相同（精确匹配）
            for name in name_list:
                if user_name.strip() == name.strip():
                    self.risk_level = "2"  # 设置为中危
                    break  # 找到一个匹配就足够了，不需要继续检查

        except Exception as e:
            print(f"警告: 检查名字列表时发生错误: {e}，将使用默认风险等级。")

    def to_db_tuple(self, owner_info: dict) -> tuple:
        """将漏洞信息和负责人信息转换为用于数据库插入的元组 (20个字段)。"""
        # 在生成数据库元组之前，根据user_name调整风险等级
        user_name = owner_info.get("user_name", "")
        self.adjust_risk_level_by_user_name(user_name)

        return (
            self.date_key,
            "内网web漏洞",  # leak_type, 可根据需要调整
            self.url,
            self.host,
            self.port,
            owner_info.get("user_name", ""),
            owner_info.get("bg", ""),
            owner_info.get("dept", ""),
            owner_info.get("center", ""),
            self.risk_name,
            self.risk_desc,
            self.risk_tag,
            self.risk_level,
            self.risk_status,
            owner_info.get("oa_group", ""),
            self.create_by,
            self.case_content,
            self.create_time,
            self.risk_type,
            self.guide_link,
        )
