# 基于用户名的风险等级调整功能

## 功能说明

在bscan_ck项目中，现在支持根据配置文件中的名字列表自动调整漏洞的风险等级。如果漏洞条目的`user_name`字段与配置文件中的任何一个名字完全匹配，该漏洞的风险等级将自动从高危(risk_level=1)调整为中危(risk_level=2)。

## 配置文件

配置文件位置：`bscan_ck/assets/name_list.txt`

### 配置文件格式

```
# 名字列表配置文件
# 如果漏洞条目的user_name字段等于以下任何一个名字，则将该漏洞的风险等级设置为中危(risk_level=2)
# 每行一个名字，支持中文和英文
# 以#开头的行为注释行，会被忽略
# 注意：这里是精确匹配，不是包含匹配

测试用户
testuser
demo_user
example_user
sandbox_admin
dev_team
development_user
staging_user
临时用户
temp_user
```

### 配置规则

1. 每行一个名字
2. 支持中文和英文
3. 以`#`开头的行为注释行，会被忽略
4. 空行会被忽略
5. 名字匹配是精确匹配（完全相等），区分大小写

## 检查范围

系统会检查漏洞条目的`user_name`字段是否与名字列表中的任何名字完全匹配：

- `user_name`: 从IP负责人信息CSV文件中获取的用户名字段

如果`user_name`与名字列表中的任何一个名字完全匹配，漏洞的风险等级将自动调整为中危。

## 工作流程

1. 扫描完成后，系统解析扫描报告获取漏洞信息
2. 从IP负责人信息CSV文件中获取每个IP对应的`user_name`
3. 创建漏洞对象并调用`to_db_tuple()`方法准备数据库插入
4. 在`to_db_tuple()`方法中，系统自动读取`name_list.txt`文件
5. 检查`user_name`是否与名字列表中的任何名字完全匹配
6. 如果匹配，将`risk_level`从默认的"1"(高危)调整为"2"(中危)
7. 同时更新`risk_desc`字段，显示正确的风险等级描述
8. 将调整后的漏洞数据插入数据库

## 测试

可以运行测试脚本来验证功能：

```bash
cd /path/to/low_sec_detect
python3 bscan_ck/test_name_list.py
```

## 日志输出

当检测到匹配的用户名时，系统会输出如下日志：

```
信息: 检测到漏洞 production.example.com:8123 的负责人 'testuser' 在名字列表中，将风险等级调整为中危。
```

## 注意事项

1. 名字匹配是精确匹配（完全相等），区分大小写
2. 只检查`user_name`字段，不检查其他字段
3. 如果`user_name`为空或不存在，不会进行任何调整
4. 如果配置文件不存在或读取失败，系统会输出警告但不会影响正常功能
5. 每次调用`to_db_tuple()`方法时都会重新读取配置文件，支持动态更新配置
6. 确保IP负责人信息CSV文件中的`user_name`字段准确无误
