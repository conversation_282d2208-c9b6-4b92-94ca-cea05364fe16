#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试name_list.txt功能的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 直接导入模块，避免相对导入问题
from bscan_ck.database.models import Vulnerability
from bscan_ck.core.utils import load_name_list
from bscan_ck.config import settings

def test_name_list_functionality():
    """测试基于user_name的名字列表功能"""
    print("=" * 60)
    print("测试基于user_name的name_list.txt功能")
    print("=" * 60)

    # 1. 测试加载名字列表
    print("\n1. 测试加载名字列表:")
    name_list = load_name_list(settings.NAME_LIST_FILE)
    print(f"加载的名字列表: {name_list}")

    # 2. 测试user_name在名字列表中的漏洞（应该被调整为中危）
    print("\n2. 测试user_name在名字列表中的漏洞:")
    test_vuln1 = Vulnerability(
        host="production.example.com",
        port=8123,
        url="http://production.example.com:8123"
    )

    # 模拟owner_info，user_name在名字列表中
    owner_info1 = {
        "user_name": "testuser",  # 这个名字在name_list.txt中
        "bg": "技术部",
        "dept": "基础架构",
        "center": "数据中心",
        "oa_group": "db_team"
    }

    # 生成数据库元组（这会触发风险等级检查）
    db_tuple1 = test_vuln1.to_db_tuple(owner_info1)
    print(f"漏洞1 - Host: {test_vuln1.host}, User: {owner_info1['user_name']}, Risk Level: {test_vuln1.risk_level}")
    print(f"Risk Desc: {test_vuln1.risk_desc}")

    # 3. 测试user_name不在名字列表中的漏洞（应该保持高危）
    print("\n3. 测试user_name不在名字列表中的漏洞:")
    test_vuln2 = Vulnerability(
        host="production.internal.com",
        port=8123,
        url="http://production.internal.com:8123"
    )

    # 模拟owner_info，user_name不在名字列表中
    owner_info2 = {
        "user_name": "production_admin",  # 这个名字不在name_list.txt中
        "bg": "技术部",
        "dept": "基础架构",
        "center": "数据中心",
        "oa_group": "prod_team"
    }

    db_tuple2 = test_vuln2.to_db_tuple(owner_info2)
    print(f"漏洞2 - Host: {test_vuln2.host}, User: {owner_info2['user_name']}, Risk Level: {test_vuln2.risk_level}")
    print(f"Risk Desc: {test_vuln2.risk_desc}")

    # 4. 测试中文用户名的情况
    print("\n4. 测试中文用户名的情况:")
    test_vuln3 = Vulnerability(
        host="*************",
        port=8123,
        url="http://*************:8123"
    )

    # 模拟owner_info，中文用户名在名字列表中
    owner_info3 = {
        "user_name": "测试用户",  # 这个中文名字在name_list.txt中
        "bg": "技术部",
        "dept": "测试部门",
        "center": "数据中心",
        "oa_group": "test_team"
    }

    db_tuple3 = test_vuln3.to_db_tuple(owner_info3)
    print(f"漏洞3 - Host: {test_vuln3.host}, User: {owner_info3['user_name']}, Risk Level: {test_vuln3.risk_level}")
    print(f"Risk Desc: {test_vuln3.risk_desc}")

    # 5. 测试空用户名的情况
    print("\n5. 测试空用户名的情况:")
    test_vuln4 = Vulnerability(
        host="*************",
        port=8123,
        url="http://*************:8123"
    )

    # 模拟owner_info，用户名为空
    owner_info4 = {
        "user_name": "",  # 空用户名
        "bg": "技术部",
        "dept": "基础架构",
        "center": "数据中心",
        "oa_group": "unknown"
    }

    db_tuple4 = test_vuln4.to_db_tuple(owner_info4)
    print(f"漏洞4 - Host: {test_vuln4.host}, User: '{owner_info4['user_name']}', Risk Level: {test_vuln4.risk_level}")
    print(f"Risk Desc: {test_vuln4.risk_desc}")

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_name_list_functionality()
