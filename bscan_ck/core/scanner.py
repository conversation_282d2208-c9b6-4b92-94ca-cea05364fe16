#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import re
from typing import List, Set
from urllib.parse import urlparse
from datetime import datetime
from ..config import settings
# 恢复pandas的导入
import pandas as pd

def run_bscan(target_file_path: str, ports: str, poc_file: str, bscan_executable: str, output_dir: str) -> bool:
    """
    执行bscan扫描，并将报告输出到指定目录。

    :param target_file_path: 目标IP文件路径 (绝对路径)
    :param ports: 扫描端口
    :param poc_file: PoC文件路径 (绝对路径)
    :param bscan_executable: bscan可执行文件路径 (绝对路径)
    :param output_dir: 扫描报告的输出目录
    :return: 扫描是否成功
    """
    print("-" * 50)
    print(f"信息: 正在启动 bscan 扫描... 输出目录: {output_dir}")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    command = [
        bscan_executable,
        "-target", target_file_path,
        "-ports", ports,
        "-exploit",
        "-poc", poc_file,
    ]
    print(f"执行命令: {' '.join(command)}")
    
    try:
        # 将工作目录切换到指定的输出目录，bscan会自动在该目录下生成报告
        subprocess.run(
            command,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=output_dir
        )
        print("成功: bscan 扫描执行完成。")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: bscan 执行失败。返回码: {e.returncode}")
        stderr_output = e.stderr.decode('utf-8', errors='ignore')
        print(f"错误输出: {stderr_output}")
        return False
    except FileNotFoundError:
        print(f"致命错误: bscan可执行文件未找到: {command[0]}")
        return False
    except Exception as e:
        print(f"致命错误: 执行bscan时发生未知错误: {e}")
        return False

def find_latest_bscan_report(directory: str) -> str or None:
    """在bscan输出目录中查找最新的xlsx报告。"""
    print(f"信息: 正在目录 '{directory}' 中查找最新的 .xlsx 报告...")
    try:
        xlsx_files = [f for f in os.listdir(directory) if f.lower().endswith('.xlsx')]
        if not xlsx_files:
            print(f"警告: 在目录 '{directory}' 中没有找到任何 .xlsx 文件。")
            return None
        
        full_paths = [os.path.join(directory, f) for f in xlsx_files]
        latest_file = max(full_paths, key=os.path.getmtime)
        print(f"信息: 已找到最新报告文件: {os.path.basename(latest_file)}")
        return latest_file
    except FileNotFoundError:
        print(f"错误: 报告目录不存在: {directory}")
        return None
    except Exception as e:
        print(f"错误: 查找最新报告时发生未知错误: {e}")
        return None

def parse_bscan_report(report_path: str) -> Set[str]:
    """
    使用pandas直接读取xlsx报告的'Sheet2'，并返回存活的 'host:port' 集合。
    包含详细的调试信息。
    """
    live_vulns = set()
    if not os.path.exists(report_path):
        print(f"警告: bscan报告文件不存在: {report_path}，无法解析。")
        return live_vulns

    try:
        # 直接使用pandas读取，明确指定sheet_name
        df = pd.read_excel(report_path, sheet_name='Sheet2', engine='openpyxl')
        
        # --- 详细调试信息 ---
        print(f"[DEBUG] pandas读取Sheet2成功。维度(行,列): {df.shape}")
        print(f"[DEBUG] 检测到的列名: {df.columns.tolist()}")
        if not df.empty:
            print(f"[DEBUG] 前5行数据预览:\n{df.head().to_string()}")
        else:
            print("[DEBUG] 数据表为空。")
        # --- 调试结束 ---

        url_col = 'URL' if 'URL' in df.columns else 'Target'
        if url_col not in df.columns:
            print(f"错误: 在Sheet2中找不到 'URL' 或 'Target' 列。")
            return live_vulns

        for url_str in df[url_col].dropna():
            host_port_str = str(url_str).strip()
            if "://" in host_port_str:
                parsed = urlparse(host_port_str)
                if parsed.hostname and parsed.port:
                    live_vulns.add(f"{parsed.hostname}:{parsed.port}")
            elif ":" in host_port_str:
                parts = host_port_str.split(':')
                if len(parts) == 2 and parts[1].isdigit():
                    live_vulns.add(host_port_str)

    except Exception as e:
        print(f"错误: 使用pandas解析bscan报告时发生意外错误。原因: {e}")

    print(f"信息: 从报告中解析出 {len(live_vulns)} 条存活漏洞。")
    return live_vulns

def process_bscan_report_to_db(report_path: str, db_handler, ip_owner_info: dict, chunk_log_dir: str = None):
    """
    解析bscan报告并写入数据库。
    如果提供了 chunk_log_dir，则会遍历该目录下的所有xlsx文件，从它们的Sheet2中聚合漏洞。
    否则，只解析 report_path 的Sheet2。

    :param report_path: 主报告文件路径 (合并后的)
    :param db_handler: DatabaseHandler实例
    :param ip_owner_info: IP负责人信息
    :param chunk_log_dir: (可选) 存放分片报告的目录
    """
    from ..database.models import Vulnerability  # 延迟导入以避免循环依赖
    
    all_live_vulns_set: Set[str] = set()

    if chunk_log_dir and os.path.isdir(chunk_log_dir):
        print(f"\n信息: 检测到分片日志目录，将从 '{os.path.basename(chunk_log_dir)}' 聚合所有Sheet2结果。")
        chunk_xlsx_files = [os.path.join(chunk_log_dir, f) for f in os.listdir(chunk_log_dir) if f.lower().endswith('.xlsx')]
        
        if not chunk_xlsx_files:
            print(f"警告: 分片日志目录 '{chunk_log_dir}' 中没有找到任何 .xlsx 文件。")
            return

        for i, chunk_report in enumerate(chunk_xlsx_files):
            print(f"  -> 正在处理分片报告 {i+1}/{len(chunk_xlsx_files)}: {os.path.basename(chunk_report)}")
            vulns_in_chunk = parse_bscan_report(chunk_report)
            all_live_vulns_set.update(vulns_in_chunk)
    else:
        print(f"\n--- 开始处理单个报告文件: {os.path.basename(report_path)} ---")
        # 兼容旧逻辑，或在非分片模式下运行
        all_live_vulns_set = parse_bscan_report(report_path)

    if not all_live_vulns_set:
        print("信息: 未发现有效漏洞，处理结束。")
        return {"found_vulns": 0, "uploaded_to_db": 0}

    print(f"\n信息: 共聚合 {len(all_live_vulns_set)} 个漏洞，正在统一写入数据库...")
    uploaded_count = 0
    for host_port in all_live_vulns_set:
        try:
            host, port_str = host_port.split(":")
            port = int(port_str)
            url = f"http://{host}:{port}"
            vuln = Vulnerability(host=host, port=port, url=url)
            owner = ip_owner_info.get(host, {})
            db_handler.add_vulnerability(vuln, owner)
            uploaded_count += 1
        except Exception as e:
            print(f"错误: 处理漏洞 {host_port} 并存入数据库时失败: {e}")

    print(f"--- 所有报告处理完毕 ---")
    return {"found_vulns": len(all_live_vulns_set), "uploaded_to_db": uploaded_count}


def process_audit_report_and_update_db(report_path: str, db_handler):
    """
    解析指定的复测报告，与数据库中的活跃漏洞比对，并更新已修复的漏洞状态。

    :param report_path: bscan生成的xlsx报告的路径。
    :param db_handler: 已经实例化的DatabaseHandler。
    """
    from ..config import settings  # 延迟导入
    print(f"\n--- 开始处理复测报告文件: {os.path.basename(report_path)} ---")

    # 1. 从数据库拉取当前所有活跃漏洞，作为复测的基准
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_CLICKHOUSE
    )
    if not targets_to_retest:
        print("信息: 数据库中没有需要复测的活跃漏洞，处理中止。")
        return {"checked_vulns": 0, "fixed_vulns": 0}

    initial_targets_set = {f"{item['host']}:{item['port']}" for item in targets_to_retest}
    print(f"信息: 从数据库中加载了 {len(initial_targets_set)} 个活跃漏洞作为比对基准。")

    # 2. 从报告中解析出本次扫描仍然存活的漏洞
    live_vulns_in_audit = parse_bscan_report(report_path)
    print(f"信息: 复测报告中发现 {len(live_vulns_in_audit)} 个仍然存活的漏洞。")

    # 3. 计算已修复的漏洞（在基准中，但本次未存活）并更新数据库
    fixed_vulns_set = initial_targets_set - live_vulns_in_audit
    print(f"信息: 计算得出 {len(fixed_vulns_set)} 个漏洞已被修复。")

    if fixed_vulns_set:
        db_handler.batch_update_status_to_fixed(
            fixed_host_ports=list(fixed_vulns_set),
            risk_type=settings.RISK_TYPE_CLICKHOUSE
        )

    print(f"--- 复测报告文件 {os.path.basename(report_path)} 处理完毕 ---")
    return {"checked_vulns": len(initial_targets_set), "fixed_vulns": len(fixed_vulns_set)}