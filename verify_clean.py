#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据清洗效果的脚本
"""

import pandas as pd
import re

def verify_ip_clean(file_path, file_type="csv"):
    """验证IP清洗效果"""
    print(f"\n=== 验证 {file_path} ===")
    
    if file_type == "csv":
        df = pd.read_csv(file_path)
        ips = df['ip'].dropna().astype(str).tolist()
    else:  # txt文件
        with open(file_path, 'r', encoding='utf-8') as f:
            ips = [line.strip() for line in f.readlines() if line.strip()]
    
    print(f"总IP数量: {len(ips)}")
    
    # 检查是否还有 ::ffff: 前缀
    ffff_ips = [ip for ip in ips if "::ffff:" in ip]
    print(f"包含 '::ffff:' 的IP数量: {len(ffff_ips)}")
    if ffff_ips:
        print(f"示例: {ffff_ips[:5]}")
    
    # 检查是否还有包含英文字母的IP
    letter_ips = [ip for ip in ips if re.search(r'[a-zA-Z]', ip)]
    print(f"包含英文字母的IP数量: {len(letter_ips)}")
    if letter_ips:
        print(f"示例: {letter_ips[:5]}")
    
    # 显示一些正常的IP样本
    normal_ips = [ip for ip in ips if not re.search(r'[a-zA-Z]', ip) and "::ffff:" not in ip]
    print(f"正常IP数量: {len(normal_ips)}")
    print(f"正常IP示例: {normal_ips[:5]}")

def main():
    """主函数"""
    print("开始验证数据清洗效果...")
    
    # 验证4个文件
    verify_ip_clean('temp_ipfo_assets/ip_info.csv', 'csv')
    verify_ip_clean('temp_ipfo_assets/conflict_ip.csv', 'csv')
    verify_ip_clean('temp_ipfo_assets/merged_ip.txt', 'txt')
    verify_ip_clean('temp_ipfo_assets/conflict_ip.txt', 'txt')
    
    print("\n验证完成！")

if __name__ == "__main__":
    main()
