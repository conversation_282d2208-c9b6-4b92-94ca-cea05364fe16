#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
独立脚本：生成每日扫描日志汇总
用法：
    python generate_daily_summary.py                    # 生成今天的汇总
    python generate_daily_summary.py 2024-01-15        # 生成指定日期的汇总
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from routine_scan_logs.logger import routine_logger


def main():
    """主函数"""
    target_date = None
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        target_date = sys.argv[1]
        try:
            # 验证日期格式
            datetime.strptime(target_date, "%Y-%m-%d")
        except ValueError:
            print("错误: 日期格式不正确，请使用 YYYY-MM-DD 格式")
            print("用法: python generate_daily_summary.py [YYYY-MM-DD]")
            sys.exit(1)
    else:
        target_date = datetime.now().strftime("%Y-%m-%d")
    
    print(f"正在生成 {target_date} 的扫描日志汇总...")
    
    try:
        # 临时设置日期（如果需要）
        if target_date != datetime.now().strftime("%Y-%m-%d"):
            # 对于历史日期，我们需要修改logger的逻辑
            original_get_today_dir = routine_logger.get_today_dir
            
            def get_target_date_dir():
                target_dir = os.path.join(routine_logger.base_dir, target_date)
                if not os.path.exists(target_dir):
                    os.makedirs(target_dir, exist_ok=True)
                return target_dir
            
            def get_target_all_log_path():
                target_dir = get_target_date_dir()
                return os.path.join(target_dir, "all.txt")
            
            # 临时替换方法
            routine_logger.get_today_dir = get_target_date_dir
            routine_logger.get_all_log_path = get_target_all_log_path
        
        # 生成汇总
        total_stats = routine_logger.generate_aggregated_log()
        
        # 恢复原始方法（如果修改过）
        if target_date != datetime.now().strftime("%Y-%m-%d"):
            routine_logger.get_today_dir = original_get_today_dir
            del routine_logger.get_all_log_path
        
        print(f"\n=== {target_date} 扫描汇总统计 ===")
        print(f"总扫描IP数: {total_stats['scanned_ips']}")
        print(f"总发现漏洞: {total_stats['found_vulnerabilities']}")
        print(f"总上传数据库: {total_stats['uploaded_to_db']}")
        print(f"总修复漏洞: {total_stats['fixed_vulnerabilities']}")
        print(f"\n汇总日志已生成: routine_scan_logs/{target_date}/all.txt")
        
    except Exception as e:
        print(f"生成汇总失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
