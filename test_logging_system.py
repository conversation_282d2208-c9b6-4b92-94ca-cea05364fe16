#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本：验证日志记录系统
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from routine_scan_logs.logger import log_initial_scan, log_audit_scan, generate_daily_summary


def test_logging_system():
    """测试日志记录系统"""
    print("开始测试日志记录系统...")
    
    # 测试数据
    test_tools = [
        "dirlist_scanner",
        "bscan_ck", 
        "bscan_es",
        "redis_scanner"
    ]
    
    print("\n1. 测试初始扫描日志记录...")
    for i, tool in enumerate(test_tools):
        try:
            log_initial_scan(
                tool_name=tool,
                scanned_ips=1000 + i * 500,
                found_vulns=10 + i * 5,
                uploaded_to_db=8 + i * 4
            )
            print(f"✓ {tool} 初始扫描日志记录成功")
        except Exception as e:
            print(f"✗ {tool} 初始扫描日志记录失败: {e}")
    
    print("\n2. 测试复测扫描日志记录...")
    for i, tool in enumerate(test_tools):
        try:
            log_audit_scan(
                tool_name=tool,
                checked_vulns=20 + i * 3,
                fixed_vulns=5 + i * 2
            )
            print(f"✓ {tool} 复测扫描日志记录成功")
        except Exception as e:
            print(f"✗ {tool} 复测扫描日志记录失败: {e}")
    
    print("\n3. 测试聚合日志生成...")
    try:
        total_stats = generate_daily_summary()
        print("✓ 聚合日志生成成功")
        print(f"统计结果: {total_stats}")
    except Exception as e:
        print(f"✗ 聚合日志生成失败: {e}")
    
    print("\n4. 检查文件结构...")
    today = datetime.now().strftime("%Y-%m-%d")
    base_dir = "routine_scan_logs"
    today_dir = os.path.join(base_dir, today)
    
    if os.path.exists(today_dir):
        print(f"✓ 今日目录存在: {today_dir}")
        
        # 检查工具日志文件
        for tool in test_tools:
            tool_log = os.path.join(today_dir, f"{tool}.txt")
            if os.path.exists(tool_log):
                print(f"✓ {tool} 日志文件存在")
                # 显示文件内容
                with open(tool_log, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        print(f"  内容预览: {content.split(chr(10))[0]}...")
            else:
                print(f"✗ {tool} 日志文件不存在")
        
        # 检查聚合日志文件
        all_log = os.path.join(today_dir, "all.txt")
        if os.path.exists(all_log):
            print(f"✓ 聚合日志文件存在: all.txt")
            with open(all_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"  文件行数: {len(lines)}")
                if lines:
                    print(f"  首行内容: {lines[0].strip()}")
        else:
            print("✗ 聚合日志文件不存在")
    else:
        print(f"✗ 今日目录不存在: {today_dir}")
    
    print("\n5. 测试完成!")
    print(f"请检查 {today_dir} 目录下的日志文件")


def clean_test_logs():
    """清理测试日志（可选）"""
    today = datetime.now().strftime("%Y-%m-%d")
    today_dir = os.path.join("routine_scan_logs", today)
    
    if os.path.exists(today_dir):
        import shutil
        try:
            shutil.rmtree(today_dir)
            print(f"已清理测试日志目录: {today_dir}")
        except Exception as e:
            print(f"清理失败: {e}")
    else:
        print("没有找到需要清理的测试日志")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "clean":
        clean_test_logs()
    else:
        test_logging_system()
        print("\n提示: 运行 'python test_logging_system.py clean' 可以清理测试日志")
