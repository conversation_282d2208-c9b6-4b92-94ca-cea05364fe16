info:
  name: ComfyUI-Ace-Nodes
  cve: CVE-2024-21577
  summary: Code Injection vulnerability in ComfyUI-Ace-Nodes
  details: |
    The ACE_ExpressionEval node in ComfyUI-Ace-Nodes contains an eval() in its entrypoint function that accepts arbitrary user-controlled data.
    A user can create a workflow that results in executing arbitrary code on the server.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately update to the latest version of ComfyUI-Ace-Nodes.
    2. Review and modify the ACE_ExpressionEval node to eliminate the use of eval().
    3. Implement strict input validation to prevent arbitrary code execution.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-21577
  - https://github.com/hay86/ComfyUI_AceNodes/blob/5ba01db8a3b7afb8e4aecfaa48823ddeb132bbbb/nodes.py#L1193