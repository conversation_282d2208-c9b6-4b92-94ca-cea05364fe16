info:
  name: mcp sse
  cve: mcp server security tips
  summary: Authentication and API abuse risks require attention in MCP server
  details: |
    The MCP server requires attention to authentication and interface abuse risks.
    Ensure the server is properly configured to prevent unauthorized access and API misuse.
  cvss: 
  severity: LOW
  security_advise: |
    1. It is recommended to only expose the MCP server locally.
    2. Implement strict authentication mechanisms.
    3. Regularly review and update server configurations to mitigate potential risks.
rule: is_internal=="true"
references: []