info:
  name: ComfyUI-Impact-Pack
  cve: CVE-2024-21575
  summary: Path Traversal vulnerability in ComfyUI-Impact-Pack leading to potential RCE
  details: |
    The vulnerability arises from the absence of validation for the `image.filename` field
    in POST requests to the `/upload/temp` endpoint. This allows attackers to write arbitrary
    files to the file system, potentially enabling remote code execution under certain conditions.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:N/I:H/A:N
  severity: CRITICAL
  security_advise: |
    1. Immediately update to the latest version of ComfyUI-Impact-Pack.
    2. Review and implement proper input validation for file uploads.
    3. Monitor for any suspicious activities related to file uploads and system changes.
rule: version < "1.0.0" # Assuming the vulnerability is present in all versions below 1.0.0, adjust as per actual versioning
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-21575
  - https://github.com/ltdrdata/ComfyUI-Impact-Pack/commit/a43dae373e648ae0f0cc0c9768c3cea6a72acff7
  - https://github.com/ltdrdata/ComfyUI-Impact-Pack/blob/1087f2ee063c9d53cd198add79b41a7a3465c05a/modules/impact/impact_server.py#L28