info:
  name: dify
  cve: CVE-2025-0184
  summary: Server-Side Request Forgery (SSRF) vulnerability in WordExtractor of langgenius/dify
  details: |
    The vulnerability arises when uploading DOCX files in the "Create Knowledge" section of Dify. If an external relationship exists in the DOCX file, the reltype value is requested as a URL using the 'requests' module instead of the 'ssrf_proxy', leading to an SSRF vulnerability. Attackers can exploit this to access internal network assets, attack internal web services, scan internal hosts, and potentially access AWS metadata endpoints.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to dify version 0.11.0 or later.
    2. Ensure that all external requests are proxied through the 'ssrf_proxy' to mitigate SSRF risks.
    3. Implement strict validation for URLs requested from external relationships in DOCX files.
    4. Monitor and restrict access to the 'HTTP Requests' block in Workflow to prevent unauthorized SSRF exploitation.
rule: version < "0.11.0"
references:
  - https://huntr.com/bounties/a7eac4ae-5d5e-4ac1-894b-7a8cce5cba9b
  - https://github.com/langgenius/dify/compare/main...fix/docx-extract-image-ssrf
  - https://drive.google.com/file/d/1EIHnqOVSlEQN9_vP4CxYqA-77xOPKa1p/view?usp=sharing