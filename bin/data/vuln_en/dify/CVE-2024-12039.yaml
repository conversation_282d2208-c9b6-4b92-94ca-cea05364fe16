info:
  name: dify
  cve: CVE-2024-12039
  summary: Admin account takeover due to excessive password reset code guessing attempts in langgenius/dify
  details: |
    The vulnerability allows unauthenticated attackers to reset admin passwords by exploiting the lack of limits on password reset code guessing attempts. With sufficient network resources, an attacker can crack a six-digit code within the 5-minute interval, leading to a complete compromise of the application.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:N
  severity: HIGH
  security_advise: |
    1. Implement rate limiting for password reset code requests.
    2. Increase the complexity of password reset codes beyond six digits.
    3. Introduce a CAPTCHA or other human verification mechanism for password reset attempts.
    4. Monitor and alert on unusual activity related to password resets.
rule: version < "0.10.2"
references:
  - https://huntr.com/bounties/61af30d5-6055-4c6c-8a55-3fa43dada512
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12039