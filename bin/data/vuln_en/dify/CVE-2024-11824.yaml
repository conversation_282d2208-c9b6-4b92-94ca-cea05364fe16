info:
  name: dify
  cve: CVE-2024-11824
  summary: Stored XSS vulnerability in langgenius/dify chatbot logs
  details: |
    The vulnerability arises from insufficient sanitization of HTML content in chat logs. 
    While certain tags like `<script>` and `<iframe>` are disallowed, others such as `<input>` and `<form>` are permitted, 
    enabling attackers to inject malicious HTML via prompts. When an admin views the affected log, 
    the injected script can execute, potentially leading to credential theft and unauthorized access.
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:R/S:C/C:H/I:H/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to dify version 0.12.1 or later.
    2. Implement comprehensive HTML sanitization for all user-generated content.
    3. Regularly review and update the list of disallowed HTML elements and attributes.
rule: version < "0.12.1"
references:
  - https://huntr.com/bounties/72387deb-6e64-48ed-a8c3-b50d22a0970f
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11824