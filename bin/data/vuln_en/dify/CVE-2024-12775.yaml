info:
  name: dify
  cve: CVE-2024-12775
  summary: SSRF vulnerability in dify via Custom Tool Testing
  details: |
    The vulnerability is a non-blind SSRF in the test functionality for the Create Custom Tool option in dify. Attackers can set arbitrary URL targets in the servers dictionary of OpenAI's schema, allowing them to abuse the victim dify server's credentials to access unauthorized web resources.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Apply a whitelist of allowed URLs to prevent illegal values for the server_url parameter.
    2. Update dify to the latest version that includes this vulnerability fix.
    3. Review and restrict API access to ensure only authorized users can create custom tools.
rule: version < "0.10.2"
references:
  - https://huntr.com/bounties/e90e929a-9bc9-46ad-a5e5-1f6f124d0f12
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12775