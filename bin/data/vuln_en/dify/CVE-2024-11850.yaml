info:
  name: dify
  cve: CVE-2024-11850
  summary: Stored XSS vulnerability in langgenius/dify due to unsanitized SVG input
  details: |
    The vulnerability arises from the unsanitized handling of SVG content in user inputs within the dify chatbot system. 
    Attackers can inject malicious SVG code containing JavaScript, which gets executed when an admin views the chat logs, 
    potentially leading to the theft of sensitive information such as admin credentials.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Implement strict input validation and sanitization for all user inputs, especially those containing markup languages like SVG.
    2. Update the dify system to the latest version that includes patches for this vulnerability.
    3. Regularly monitor and audit the system for any signs of unauthorized access or suspicious activities.
rule: version = "latest"
references:
  - https://huntr.com/bounties/893da115-028d-4718-b586-a2b77897a470
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11850