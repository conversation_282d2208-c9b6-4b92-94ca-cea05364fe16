info:
  name: dify
  cve: CVE-2025-1796
  summary: Admin account takeover via weak PRNG in password reset codes
  details: |
    Dify uses `random.randint` to generate password reset codes, which are not suitable for cryptographic purposes and can be cracked. An attacker with permission to use workflow tools (like the Cogview tool) can exploit this to reset passwords for any user, including administrators, leading to complete application compromise.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Replace `random.randint` with a cryptographically secure random number generator.
    2. Ensure that password reset codes are generated using a secure method that is resistant to prediction and brute force attacks.
    3. Regularly update and patch the application to address any known vulnerabilities in the random number generation process.
rule: version = "0.10.1"
references:
  - https://huntr.com/bounties/a60f3039-5394-4e22-8de7-a7da9c6a6e00
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1796
  - https://cwe.mitre.org/data/definitions/338.html