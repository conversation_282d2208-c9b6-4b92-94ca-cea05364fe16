info:
  name: dify
  cve: CVE-2024-12776
  summary: Admin user account takeover due to password reset code not being checked on the backend in langgenius/dify.
  details: |
    The vulnerability allows an attacker to reset the password of any user, including administrators, by bypassing the code verification step in the password reset process. This is achieved by directly accessing the password reset endpoint with a token obtained from a password reset request.
    
    Proof of Concept:
    1. Initiate a password reset for any user.
    2. Extract the token from the redirect URL.
    3. Send a POST request to /console/api/forgot-password/resets with the extracted token and a new password.
    
    This results in the attacker gaining full control over the targeted user account.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to dify version >= v0.10.2 (assuming this version contains the fix).
    2. Implement server-side validation for the password reset code before allowing password changes.
    3. Review and enhance all authentication and password reset workflows to prevent similar bypasses.
rule: version < "0.10.2"
references:
  - https://huntr.com/bounties/00a8b403-7da5-431e-afa3-40339cf734bf
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12776