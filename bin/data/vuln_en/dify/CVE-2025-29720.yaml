info:
  name: dify
  cve: CVE-2025-29720
  summary: Dify v1.0 contains a Server-Side Request Forgery (SSRF) vulnerability.
  details: |
    Dify v1.0 was discovered to contain a Server-Side Request Forgery (SSRF) via the component controllers.console.remote_files.RemoteFileUploadApi.
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:U/C:L/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of Dify that addresses this vulnerability.
    2. Implement strict input validation for remote file uploads.
    3. Monitor and restrict outbound network requests from the application.
rule: version < "1.0.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-29720
  - https://github.com/langgenius/dify/issues/15185
  - https://dify.ai