info:
  name: dify
  cve: CVE-2024-11821
  summary: Privilege escalation bug in dify allows modifying Orchestrate instructions.
  details: |
    The vulnerability allows a normal user to modify the Orchestrate instruction of a chatbot created by an admin user. 
    This can be exploited by creating a chatbot with an Orchestrate instruction, adding a normal user, and then having the normal user send a specific request to modify the Orchestrate instruction.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to dify version 0.9.2 or higher.
    2. Review and enforce strict access controls for modifying Orchestrate instructions.
    3. Implement proper authentication and authorization checks for API endpoints.
rule: version < "0.9.2"
references:
  - https://huntr.com/bounties/76d5986d-3882-4ea7-81cb-f00400e5c6b6
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11821