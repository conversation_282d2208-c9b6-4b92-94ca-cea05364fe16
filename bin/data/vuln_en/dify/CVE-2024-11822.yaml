info:
  name: dify
  cve: CVE-2024-11822
  summary: Server-Side Request Forgery (SSRF) in langgenius/dify
  details: |
    The vulnerability allows a direct request to be made to the input received with the `api_endpoint` parameter, enabling access to the local network and potentially the AWS metadata endpoint.
    Proof of Concept involves starting a local service and making a crafted request to trigger the vulnerability.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to dify >= 0.9.2 (assuming a fix is available in this version or later)
    2. Implement strict validation for the `api_endpoint` parameter to ensure it does not allow internal or sensitive network addresses.
    3. Consider implementing network-level protections such as firewalls to restrict outbound requests from the application.
rule: version < "0.9.2"
references:
  - https://huntr.com/bounties/f3042029-5d4e-41c6-850d-bbe02fae6592
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11822