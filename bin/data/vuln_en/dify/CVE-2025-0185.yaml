info:
  name: dify
  cve: CVE-2025-0185
  summary: Pandas Query Injection vulnerability in Dify Tools' Vanna module
  details: |
    The vulnerability in the Dify Tools' Vanna module allows for Pandas Query Injection due to improper sanitization of user inputs in the function vn.get_training_plan_generic(df_information_schema). This can lead to Remote Code Execution (RCE) if exploited.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Immediately update to the latest version of Dify Tools where the vulnerability is fixed.
    2. Review and enhance input sanitization practices in the Vanna module to prevent similar vulnerabilities.
    3. Monitor for any suspicious activities that might indicate an ongoing attack.
rule: ""
references:
  - https://huntr.com/bounties/7d9eb9b2-7b86-45ed-89bd-276c1350db7e
  - https://huntr.com/bounties/ea4bc85e-9639-42be-8db9-c0738025cb32