info:
  name: llamafactory
  cve: CVE-2024-52803
  summary: LLama Factory Remote OS Command Injection Vulnerability
  details: |
    A critical remote OS command injection vulnerability has been identified in the Llama Factory training process. This vulnerability arises from improper handling of user input, allowing malicious actors to execute arbitrary OS commands on the host system. The issue is caused by insecure usage of the `Popen` function with `shell=True`, coupled with unsanitized user input.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: CRITICAL
  security_advise: |
    1. Avoid using `shell=True` in `Popen`.
    2. Pass the command and its arguments as a list to prevent user inputs from being executed as part of a shell command.
    3. Upgrade to Llama Factory version 0.9.1 or later.
rule: version <= "0.9.0"
references:
  - https://github.com/hiyouga/LLaMA-Factory/security/advisories/GHSA-hj3w-wrh4-44vp
  - https://github.com/hiyouga/LLaMA-Factory/commit/b3aa80d54a67da45e9e237e349486fb9c162b2ac
  - https://github.com/hiyouga/LLaMA-Factory