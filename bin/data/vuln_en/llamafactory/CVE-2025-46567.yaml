info:
  name: llamafactory
  cve: CVE-2025-46567
  summary: LLaMA-Factory Allows Arbitrary Code Execution via Unsafe Deserialization in Ilamafy_baichuan2.py
  details: |
    A critical vulnerability exists in the `llamafy_baichuan2.py` script of the LLaMA-Factory project. The script performs insecure deserialization using `torch.load()` on user-supplied `.bin` files from an input directory, allowing attackers to execute arbitrary commands during deserialization.
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:U/C:H/I:L/A:L
  severity: CRITICAL
  security_advise: |
    1. Replace `torch.load()` with safer alternatives like `safetensors`.
    2. Validate and whitelist file types before deserialization.
    3. Require checksum validation for loaded files.
    4. Avoid running the script with untrusted `.bin` files.
    5. Use containers or VMs to isolate script execution.
rule: version < "0.9.2"
references:
  - https://github.com/hiyouga/LLaMA-Factory/security/advisories/GHSA-f2f7-gj54-6vpv
  - https://nvd.nist.gov/vuln/detail/CVE-2025-46567
  - https://github.com/hiyouga/LLaMA-Factory/commit/2989d39239d2f46e584c1e1180ba46b9768afb2a
  - https://github.com/hiyouga/LLaMA-Factory/blob/main/scripts/convert_ckpt/llamafy_baichuan2.py#L35