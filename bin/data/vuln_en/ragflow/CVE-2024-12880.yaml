info:
  name: ragflow
  cve: CVE-2024-12880
  summary: Partial account takeover via insecure data querying in RAGFlow
  details: |
    A vulnerability in infiniflow/ragflow version RAGFlow-0.13.0 allows for partial account takeover via insecure data querying. The issue arises from the way tenant IDs are handled in the application. If a user has access to multiple tenants, they can manipulate their tenant access to query and access API tokens of other tenants. This vulnerability affects the following endpoints: /v1/system/token_list, /v1/system/new_token, /v1/api/token_list, /v1/api/new_token, and /v1/api/rm. An attacker can exploit this to access other tenants' API tokens, perform actions on behalf of other tenants, and access their data.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to RAGFlow version RAGFlow-0.14.0 or later.
    2. Implement strict tenant ID validation to prevent unauthorized access to other tenants' data.
    3. Monitor and audit API token usage for any suspicious activity.
rule: version < "0.14.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12880
  - https://huntr.com/bounties/c41c7eaa-554a-408c-96be-9dba56113970