info:
  name: ragflow
  cve: CVE-2024-53450
  summary: Improper access control in RAGFlow 0.13.0 allows unauthorized access to user documents.
  details: |
    The vulnerability in RAGFlow 0.13.0 is due to improper access control in the `document-hooks.ts` file,
    which permits unauthorized access to user documents.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to RAGFlow version 0.13.1 or later.
    2. Review and modify the access control logic in `document-hooks.ts` to ensure proper authentication and authorization checks.
    3. Regularly audit and test access control mechanisms to prevent similar vulnerabilities.
rule: version < "0.13.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-53450
  - https://github.com/infiniflow/ragflow/blob/cec208051f6f5996fefc8f36b6b71231b1807533/web/src/hooks/document-hooks.ts#L23
  - https://github.com/thanhtung4102/Unauthentication-in-Ragflow