info:
  name: ragflow
  cve: CVE-2024-12871
  summary: XSS vulnerability in Ragflow allows session hijacking and data exfiltration
  details: |
    An XSS vulnerability in infiniflow/ragflow version 0.12.0 allows an attacker to upload a malicious PDF file to the knowledge base.
    When the file is viewed within Ragflow, the payload is executed in the context of the user's browser. This can lead to session hijacking,
    data exfiltration, or unauthorized actions performed on behalf of the victim, compromising sensitive user data and affecting the integrity
    of the entire application.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to Ragflow version 0.12.1 or later.
    2. Implement strict file upload validation to prevent malicious PDF uploads.
    3. Regularly review and patch any identified vulnerabilities in the Ragflow application.
rule: version < "0.12.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12871
  - https://huntr.com/bounties/7903945c-2839-4dd5-9d40-9ef47fe53118