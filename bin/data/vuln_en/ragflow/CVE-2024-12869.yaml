info:
  name: ragflow
  cve: CVE-2024-12869
  summary: Improper authentication vulnerability in RagFlow allows unauthorized viewing of invite lists.
  details: |
    In infiniflow/ragflow version v0.12.0, there is an improper authentication vulnerability that allows a user to view another user's invite list. This can lead to a privacy breach where users' personal or private information, such as email addresses or usernames in the invite list, could be exposed without their consent. This data leakage can facilitate further attacks, such as phishing or spam, and result in loss of trust and potential regulatory issues.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to RagFlow version v0.12.1 or later.
    2. Implement proper authentication checks to ensure users can only access their own invite lists.
    3. Monitor for any unauthorized access attempts and respond promptly to potential security incidents.
rule: version == "0.12.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12869
  - https://huntr.com/bounties/768b1a56-1e79-416a-8445-65953568b04a