info:
  name: ragflow
  cve: CVE-2024-12450
  summary: Multiple vulnerabilities in infiniflow/ragflow versions 0.12.0
  details: |
    The `web_crawl` function in `document_app.py` contains multiple vulnerabilities:
    - Does not filter URL parameters, allowing Full Read SSRF by accessing internal network addresses.
    - Lack of restrictions on the file protocol enables Arbitrary File Read.
    - Use of an outdated Chromium headless version with --no-sandbox mode enabled makes the application susceptible to Remote Code Execution (RCE) via known Chromium v8 vulnerabilities.
    These issues are resolved in version 0.14.0.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to ragflow>=0.14.0
    2. Review and restrict URL parameter handling in the `web_crawl` function
    3. Implement proper file protocol restrictions
    4. Update Chromium headless to the latest version and disable --no-sandbox mode if possible
rule: version >= "0.12.0" && version < "0.14.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12450
  - https://github.com/infiniflow/ragflow/commit/3faae0b2c2f8a26233ee1442ba04874b3406f6e9
  - https://huntr.com/bounties/da06360c-87c3-4ba9-be67-29f6eff9d44a