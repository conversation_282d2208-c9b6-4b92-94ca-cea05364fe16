info:
  name: ragflow
  cve: CVE-2024-12870
  summary: Stored XSS vulnerability in RagFlow allows unauthorized JavaScript execution.
  details: |
    A stored cross-site scripting (XSS) vulnerability exists in infiniflow/ragflow, affecting the latest commit on the main branch (cec2080). 
    The vulnerability allows an attacker to upload HTML/XML files that can host arbitrary JavaScript payloads. 
    These files are served with the 'application/xml' content type, which is automatically rendered by browsers. 
    This can lead to the execution of arbitrary JavaScript in the context of the user's browser, 
    potentially allowing attackers to steal cookies and gain unauthorized access to user files and resources. 
    The vulnerability does not require authentication, making it accessible to anyone with network access to the instance.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Immediately apply the latest security patches released by RagFlow.
    2. Implement strict file upload validation to prevent HTML/XML files containing JavaScript.
    3. Monitor and review logs for suspicious activities related to file uploads.
    4. Consider deploying a Web Application Firewall (WAF) to detect and block malicious requests.
rule: version = "cec2080" || version < "main"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12870
  - https://huntr.com/bounties/d6b497d2-5c95-4abc-8033-04b8068fed65