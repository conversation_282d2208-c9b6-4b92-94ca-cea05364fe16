info:
  name: ragflow
  cve: CVE-2024-12433
  summary: Remote code execution vulnerability in RagFlow due to hard-coded AuthKey and pickle deserialization
  details: |
    A vulnerability in infiniflow/ragflow versions v0.12.0 allows for remote code execution. 
    The RPC server in RagFlow uses a hard-coded AuthKey which can be easily fetched by attackers to join the group communication without restrictions. 
    Additionally, the server processes incoming data using pickle deserialization, making it vulnerable to remote code execution. 
    This issue is fixed in version 0.14.0.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to ragflow>=0.14.0
    2. Avoid using pickle for deserialization of untrusted data
    3. Implement proper authentication and authorization mechanisms
rule: version >= "0" && version < "0.14.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12433
  - https://github.com/infiniflow/ragflow/commit/49494d4e3c8f06a5e52cf1f7cce9fa03cadcfbf6
  - https://huntr.com/bounties/8a1465af-09e4-42af-9e54-0b70e7c87499