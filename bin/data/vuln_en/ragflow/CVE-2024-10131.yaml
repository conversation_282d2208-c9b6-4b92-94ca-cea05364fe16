info:
  name: ragflow
  cve: CVE-2024-10131
  summary: Remote Code Execution (RCE) vulnerability in RagFlow's `add_llm` function
  details: |
    The `add_llm` function in `llm_app.py` within RagFlow version 0.11.0 contains a remote code execution (RCE) vulnerability. 
    It utilizes user-supplied inputs `req['llm_factory']` and `req['llm_name']` to dynamically instantiate classes from 
    various model dictionaries without adequate input validation or sanitization, potentially allowing arbitrary code execution.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately upgrade to RagFlow version 0.11.1 or later.
    2. Review and enhance input validation and sanitization practices in the `add_llm` function.
    3. Monitor for any suspicious activities post-upgrade to ensure the vulnerability is fully mitigated.
rule: version == "0.11.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10131
  - https://huntr.com/bounties/42ae0b27-e851-4b58-a991-f691a437fbaa