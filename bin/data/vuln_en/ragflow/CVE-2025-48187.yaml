info:
  name: ragflow
  cve: CVE-2025-48187
  summary: RA<PERSON><PERSON><PERSON> through 0.18.1 allows account takeover via brute-force email verification codes.
  details: |
    RAGFlow through version 0.18.1 is vulnerable to account takeover due to the absence of rate limiting on six-digit email verification codes.
    Attackers can exploit this by conducting successful brute-force attacks, enabling arbitrary account registration, login, and password reset.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: |
    1. Upgrade to RAGFlow version 0.18.2 or later.
    2. Implement rate limiting on email verification codes to prevent brute-force attacks.
    3. Monitor for unusual account activity that may indicate a brute-force attempt.
rule: version < "0.18.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48187
  - https://github.com/infiniflow/ragflow/commits/main
  - https://www.cnblogs.com/qiushuo/p/********