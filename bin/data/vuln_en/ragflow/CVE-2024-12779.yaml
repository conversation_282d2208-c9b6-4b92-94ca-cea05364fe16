info:
  name: ragflow
  cve: CVE-2024-12779
  summary: SSRF vulnerability in infiniflow/ragflow version 0.12.0
  details: |
    A Server-Side Request Forgery (SSRF) vulnerability exists in the `POST /v1/llm/add_llm` and `POST /v1/conversation/tts` endpoints of infiniflow/ragflow version 0.12.0. Attackers can specify an arbitrary URL as the `api_base` when adding an `OPENAITTS` model, and subsequently access the `tts` REST API endpoint to read contents from the specified URL, potentially leading to unauthorized access to internal web resources.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to a version of infiniflow/ragflow that addresses this vulnerability.
    2. Implement strict validation for URLs provided in API requests.
    3. Monitor and restrict access to internal web resources from external sources.
rule: version == "0.12.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12779
  - https://huntr.com/bounties/3cc748ba-2afb-4bfe-8553-10eb6d6dd4f0