info:
  name: langflow
  cve: CVE-2024-48061
  summary: Langflow vulnerable to remote code execution
  details: |
    Langflow versions <=1.0.18 are vulnerable to Remote Code Execution (RCE). Any component provided with code functionality runs on the local machine without a sandbox, allowing attackers to execute arbitrary code.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langflow >=1.0.19
    2. Review and restrict component code functionality to prevent unauthorized code execution
    3. Implement a sandbox environment for running components to mitigate RCE risks
rule: version <= "1.0.18"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-48061
  - https://github.com/langflow-ai/langflow/issues/696
  - https://gist.github.com/AfterSnows/1e58257867002462923fd62dde2b5d61
  - https://github.com/langflow-ai/langflow
  - https://rumbling-slice-eb0.notion.site/There-is-a-Remote-Code-Execution-RCE-vulnerability-in-the-repository-https-github-com-langflow-a-105e3cda9e8c800fac92f1b571bd40d8