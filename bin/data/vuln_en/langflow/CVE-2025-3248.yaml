info:
  name: langflow
  cve: CVE-2025-3248
  summary: Langflow Vulnerable to Code Injection via the `/api/v1/validate/code` endpoint
  details: |
    Langflow versions prior to 1.3.0 are susceptible to code injection in the `/api/v1/validate/code` endpoint. 
    A remote and unauthenticated attacker can send crafted HTTP requests to execute arbitrary code.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langflow>=1.3.0
    2. Review and sanitize all inputs to the `/api/v1/validate/code` endpoint
    3. Implement additional security measures such as input validation and output encoding
rule: version < "1.3.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-3248
  - https://github.com/langflow-ai/langflow/pull/6911
  - https://github.com/langflow-ai/langflow
  - https://github.com/langflow-ai/langflow/releases/tag/1.3.0
  - https://www.horizon3.ai/attack-research/disclosures/unsafe-at-any-speed-abusing-python-exec-for-unauth-rce-in-langflow-ai