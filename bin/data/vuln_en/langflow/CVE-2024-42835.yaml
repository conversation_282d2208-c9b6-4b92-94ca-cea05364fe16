info:
  name: langflow
  cve: CVE-2024-42835
  summary: langflow has vulnerability in PythonCodeTool component
  details: |
    langflow v1.0.12 was discovered to contain a remote code execution (RCE) vulnerability via the PythonCodeTool component.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to langflow >= 1.0.13
    2. Monitor and restrict access to the PythonCodeTool component
    3. Regularly update dependencies and conduct security audits
rule: version < "1.0.13"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-42835
  - https://github.com/langflow-ai/langflow/issues/2908
  - https://github.com/langflow-ai/langflow