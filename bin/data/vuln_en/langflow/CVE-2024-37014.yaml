info:
  name: langflow
  cve: CVE-2024-37014
  summary: Langflow remote code execution vulnerability
  details: |
    Langflow allows remote code execution if untrusted users are able to reach the "POST /api/v1/custom_component" endpoint and provide a Python script.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to langflow>=1.0.15
    2. Ensure that only trusted users have access to the "POST /api/v1/custom_component" endpoint
    3. Implement input validation to prevent the execution of arbitrary Python scripts
rule: version >= "0" && version < "1.0.15"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-37014
  - https://github.com/langflow-ai/langflow/issues/1973
  - https://github.com/langflow-ai/langflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/langflow/PYSEC-2024-177.yaml