info:
  name: fastchat
  cve: CVE-2024-11603
  summary: Server-Side Request Forgery (SSRF) vulnerability in fastchat
  details: |
    A Server-Side Request Forgery (SSRF) vulnerability exists in lm-sys/fastchat version 0.2.36. 
    The vulnerability is present in the `/queue/join?` endpoint, where insufficient validation 
    of the path parameter allows an attacker to send crafted requests. This can lead to 
    unauthorized access to internal networks or the AWS metadata endpoint, potentially 
    exposing sensitive data and compromising internal servers.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to fastchat version 0.2.37 or higher
    2. Implement strict validation for path parameters in the `/queue/join?` endpoint
    3. Monitor and restrict access to internal networks and AWS metadata endpoints
rule: version < "0.2.37"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11603
  - https://huntr.com/bounties/89f1158d-4a75-4000-a1bd-f82dd1a62bff