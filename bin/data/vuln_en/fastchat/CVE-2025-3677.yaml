info:
  name: fastchat
  cve: CVE-2025-3677
  summary: Critical deserialization vulnerability in lm-sys fastchat up to version 0.2.36.
  details: |
    A critical vulnerability was discovered in the `split_files/apply_delta_low_cpu_mem` function
    of the file `fastchat/model/apply_delta.py`. This vulnerability allows for local deserialization
    attacks, potentially leading to unauthorized data access or manipulation.
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: CRITICAL
  security_advise: |
    1. Upgrade to fastchat version 0.2.37 or higher.
    2. Review and patch the `apply_delta.py` file to prevent unauthorized deserialization.
    3. Implement additional security measures to monitor and restrict local access where necessary.
rule: version < "0.2.37"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-3677
  - https://github.com/lm-sys/FastChat/issues/3713
  - https://vuldb.com/?ctiid.304966
  - https://vuldb.com/?id.304966
  - https://vuldb.com/?submit.552755