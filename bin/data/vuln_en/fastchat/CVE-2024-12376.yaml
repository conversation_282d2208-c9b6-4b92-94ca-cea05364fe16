info:
  name: fastchat
  cve: CVE-2024-12376
  summary: Server-Side Request Forgery (SSRF) vulnerability in lm-sys/fastchat
  details: |
    A Server-Side Request Forgery (SSRF) vulnerability was identified in the lm-sys/fastchat web server, specifically in the affected version git 2c68a13. This vulnerability allows an attacker to access internal server resources and data that are otherwise inaccessible, such as AWS metadata credentials.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of fastchat that addresses this vulnerability.
    2. Implement strict input validation for all external requests.
    3. Use a Web Application Firewall (WAF) to monitor and block suspicious requests.
rule: version < "git 2c68a13"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12376
  - https://huntr.com/bounties/c9cc3f28-ee9f-4d2d-9ee5-8c6455a11892