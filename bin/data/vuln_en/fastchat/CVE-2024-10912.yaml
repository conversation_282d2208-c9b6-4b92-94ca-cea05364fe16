info:
  name: fastchat
  cve: CVE-2024-10912
  summary: Denial of Service (DoS) vulnerability in file upload feature
  details: |
    A Denial of Service (DoS) vulnerability exists in the file upload feature of lm-sys/fastchat version 0.2.36. 
    The vulnerability is due to improper handling of form-data with a large filename in the file upload request. 
    An attacker can exploit this by sending a payload with an excessively large filename, causing the server to become overwhelmed and unavailable to legitimate users.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to fastchat >= 0.2.37
    2. Implement server-side validation for filename sizes in file upload requests
    3. Monitor server logs for unusual activity related to file uploads
rule: version < "0.2.37"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10912
  - https://huntr.com/bounties/52f335b8-1134-4d0f-acb4-efef516de414