info:
  name: fastchat
  cve: CVE-2024-10044
  summary: Server-Side Request Forgery (SSRF) vulnerability in fastchat
  details: |
    A Server-Side Request Forgery (SSRF) vulnerability exists in the POST /worker_generate_stream API endpoint of the Controller API Server in lm-sys/fastchat, as of commit e208d5677c6837d590b81cb03847c0b9de100765. This vulnerability allows attackers to exploit the victim controller API server's credentials to perform unauthorized web actions or access unauthorized web resources by combining it with the POST /register_worker endpoint.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
  severity: CRITICAL
  security_advise: |
    1. Immediately patch the SSRF vulnerability in the Controller API Server.
    2. Review and restrict access to the POST /worker_generate_stream and POST /register_worker endpoints.
    3. Monitor for any suspicious activities that may indicate exploitation of this vulnerability.
rule: version < "e208d5677c6837d590b81cb03847c0b9de100765"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10044
  - https://huntr.com/bounties/44633540-377d-4ac4-b3a3-c2d0fa19d0e6