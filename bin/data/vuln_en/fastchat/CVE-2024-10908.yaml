info:
  name: fastchat
  cve: CVE-2024-10908
  summary: Open redirect vulnerability in fastchat allows remote unauthenticated attackers to redirect users to arbitrary websites.
  details: |
    An open redirect vulnerability in lm-sys/fastchat Release v0.2.36 allows a remote unauthenticated attacker to redirect users to arbitrary websites via a specially crafted URL. This can be exploited for phishing attacks, malware distribution, and credential theft.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to fastchat >= v0.2.37
    2. Implement strict URL validation to prevent open redirects
    3. Monitor for suspicious activity and implement additional security measures as needed
rule: version < "0.2.37"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10908
  - https://huntr.com/bounties/61f5e725-5579-4d08-8a88-e4ba04e6d1f2