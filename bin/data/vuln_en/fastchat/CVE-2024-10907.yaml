info:
  name: fastchat
  cve: CVE-2024-10907
  summary: Denial of Service (DoS) vulnerability in fastchat due to improper handling of multipart boundaries
  details: |
    In lm-sys/fastchat Release v0.2.36, the server fails to handle excessive characters appended to the end of multipart boundaries. This flaw can be exploited by sending malformed multipart requests with arbitrary characters at the end of the boundary. Each extra character is processed in an infinite loop, leading to excessive resource consumption and a complete denial of service (DoS) for all users. The vulnerability is unauthenticated, meaning no user login or interaction is required for an attacker to exploit this issue.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to fastchat >= v0.2.37
    2. Implement input validation for multipart boundaries to prevent excessive character processing
    3. Monitor server logs for unusual activity that may indicate an attack
rule: version < "0.2.37"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10907
  - https://huntr.com/bounties/bf3ca81d-3508-4455-95d9-0b653e46d6e4