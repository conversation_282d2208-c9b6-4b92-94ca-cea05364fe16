info:
  name: vllm
  cve: CVE-2025-48944
  summary: vLLM Tool Schema allows DoS via Malformed pattern and type Fields
  details: |
    The vLLM backend used with the `/v1/chat/completions` OpenAPI endpoint fails to validate unexpected or malformed input in the `"pattern"` and `"type"` fields when the tools functionality is invoked. These inputs are not validated before being compiled or parsed, causing a crash of the inference worker with a single request. The worker will remain down until it is restarted.
    The `"type"` field is expected to be one of: `"string"`, `"number"`, `"object"`, `"boolean"`, `"array"`, or `"null"`. Supplying any other value will cause the worker to crash.
    The `"pattern"` field undergoes Jinja2 rendering prior to being passed unsafely into the native regex compiler without validation or escaping. This allows malformed expressions to reach the underlying C++ regex engine, resulting in fatal errors.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to vllm >= 0.9.0
    2. Validate and sanitize input for the `"pattern"` and `"type"` fields before processing
    3. Monitor for any worker crashes and implement automated restarts if necessary
rule: version >= "0.8.0" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-vrq3-r879-7m65
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48944
  - https://github.com/vllm-project/vllm/pull/17623
  - https://github.com/vllm-project/vllm