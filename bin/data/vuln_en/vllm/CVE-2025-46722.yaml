info:
  name: vllm
  cve: CVE-2025-46722
  summary: vLLM has a Weakness in MultiModalHasher Image Hashing Implementation
  details: |
    In the file `vllm/multimodal/hasher.py`, the `MultiModalHasher` class has a security and data integrity issue in its image hashing method. Currently, it serializes `PIL.Image.Image` objects using only `obj.tobytes()`, which returns only the raw pixel data, without including metadata such as the image’s shape (width, height, mode). As a result, two images of different sizes (e.g., 30x100 and 100x30) with the same pixel byte sequence could generate the same hash value. This may lead to hash collisions, incorrect cache hits, and even data leakage or security risks.
    
    The impact extends to other modalities, with video being affected due to incorrect numpy sequencing and audio being less impacted due to librosa's handling of single-channel encoding by default.
    
    **Recommendation:** In the `serialize_item` method, serialization of `Image.Image` objects should include not only pixel data but also all critical metadata—such as dimensions (`size`), color mode (`mode`), format, and especially the `info` dictionary.
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:N/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to vllm >= 0.9.0 to apply the fix.
    2. If upgrading is not immediately feasible, implement a custom hashing method that includes image metadata for `PIL.Image.Image` objects.
    3. Monitor for any unexpected cache hits or hash collisions and investigate potential security implications.
rule: version >= "0.7.0" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-c65p-x677-fgj6
  - https://nvd.nist.gov/vuln/detail/CVE-2025-46722
  - https://github.com/vllm-project/vllm/pull/17378
  - https://github.com/vllm-project/vllm/commit/99404f53c72965b41558aceb1bc2380875f5d848
  - https://github.com/pypa/advisory-database/tree/main/vulns/vllm/PYSEC-2025-43.yaml
  - https://github.com/vllm-project/vllm