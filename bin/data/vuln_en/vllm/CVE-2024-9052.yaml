info:
  name: vllm
  cve: CVE-2024-9052
  summary: Remote code execution vulnerability in vllm-project vllm distributed training API
  details: |
    The vllm-project vllm version 0.6.0 contains a vulnerability in the distributed training API.
    The function `vllm.distributed.GroupCoordinator.recv_object()` deserializes received object bytes using `pickle.loads()` without sanitization,
    leading to a remote code execution vulnerability.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately upgrade to a version of vllm-project vllm that addresses this vulnerability.
    2. Avoid using pickle for deserialization in networked applications where possible.
    3. Implement input validation and sanitization for all deserialized data.
rule: version == "0.6.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9052
  - https://huntr.com/bounties/ea75728f-4efe-4a3d-9f53-33f2c908e9f8