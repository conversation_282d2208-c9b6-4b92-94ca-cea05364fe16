info:
  name: vllm
  cve: CVE-2024-11040
  summary: vllm-project vllm version 0.5.2.2 is vulnerable to Denial of Service attacks.
  details: |
    The vulnerability affects the 'POST /v1/completions' and 'POST /v1/embeddings' endpoints.
    - For 'POST /v1/completions', enabling 'use_beam_search' and setting 'best_of' to a high value causes the HTTP connection to time out, with vllm ceasing effective work and the request remaining in a 'pending' state, blocking new completion requests.
    - For 'POST /v1/embeddings', supplying invalid inputs to the JSON object causes an issue in the background loop, resulting in all further completion requests returning a 500 HTTP error code ('Internal Server Error') until vllm is restarted.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to vllm-project vllm version 0.5.2.3 or later.
    2. Implement input validation for the 'POST /v1/embeddings' endpoint to prevent invalid inputs.
    3. Monitor and adjust 'use_beam_search' and 'best_of' settings for the 'POST /v1/completions' endpoint to prevent resource exhaustion.
rule: version < "0.5.2.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11040
  - https://huntr.com/bounties/8ce20bbe-3c96-4cd1-97e5-25a5630925be