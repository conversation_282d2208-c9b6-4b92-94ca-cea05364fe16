info:
  name: vllm
  cve: CVE-2025-48943
  summary: vLLM allows clients to crash the openai server with invalid regex
  details: |
    A denial of service bug caused the vLLM server to crash if an invalid regex was provided while using structured output. This vulnerability is similar to [GHSA-6qc9-v4r8-22xg](https://github.com/vllm-project/vllm/security/advisories/GHSA-6qc9-v4r8-22xg), but for regex instead of a JSON schema.
    
    Issue with more details: [vllm issue #17313](https://github.com/vllm-project/vllm/issues/17313)
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to vllm >= 0.9.0
    2. Review and enforce input validation for regex patterns to prevent server crashes
rule: version >= "0.8.0" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-9hcf-v7m4-6m2j
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48943
  - https://github.com/vllm-project/vllm/issues/17313
  - https://github.com/vllm-project/vllm/pull/17623
  - https://github.com/vllm-project/vllm/commit/08bf7840780980c7568c573c70a6a8db94fd45ff
  - https://github.com/vllm-project/vllm