info:
  name: vllm
  cve: CVE-2024-8768
  summary: vLLM denial of service vulnerability
  details: |
    A flaw was found in the vLLM library. A completions API request with an empty prompt will crash the vLLM API server, resulting in a denial of service.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to vllm>=0.5.5
    2. Implement input validation to prevent empty prompts in completions API requests
rule: version < "0.5.5"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8768
  - https://github.com/vllm-project/vllm/issues/7632
  - https://github.com/vllm-project/vllm/pull/7746
  - https://github.com/vllm-project/vllm/commit/e25fee57c2e69161bd261f5986dc5aeb198bbd42
  - https://access.redhat.com/security/cve/CVE-2024-8768
  - https://bugzilla.redhat.com/show_bug.cgi?id=2311895
  - https://github.com/vllm-project/vllm