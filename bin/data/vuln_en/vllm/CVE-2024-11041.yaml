info:
  name: vllm
  cve: CVE-2024-11041
  summary: Remote code execution vulnerability in vllm-project v0.6.2 via MessageQueue.dequeue()
  details: |
    The vulnerability in the MessageQueue.dequeue() API function of vllm-project v0.6.2 arises from the use of pickle.loads to parse received sockets directly. 
    This allows an attacker to send a malicious payload to the MessageQueue, enabling the execution of arbitrary code on the victim's machine.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately upgrade to a version of vllm-project that addresses this vulnerability.
    2. Avoid using pickle.loads for parsing socket data; consider alternative, safer methods for data deserialization.
    3. Implement strict input validation and sanitization for all data received via the MessageQueue.
rule: version == "0.6.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11041
  - https://huntr.com/bounties/00136195-11e0-4ad0-98d5-72db066e867f