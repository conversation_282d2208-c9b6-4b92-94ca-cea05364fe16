info:
  name: vllm
  cve: CVE-2025-24357
  summary: Malicious model remote code execution fix bypass with PyTorch < 2.6.0
  details: |
    Loading a malicious model could result in code execution on the vllm host. The fix applied to specify `weights_only=True` to calls to `torch.load()` did not solve the problem prior to PyTorch 2.6.0.
    This means that versions of vLLM using PyTorch before 2.6.0 are vulnerable to this problem.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to PyTorch version 2.6.0 or higher.
    2. Ensure that all installations of vLLM use a secure version of PyTorch as specified in the official documentation.
rule: version < "0.8.0"
references:
  - https://github.com/pytorch/pytorch/security/advisories/GHSA-53q9-r3pm-6pq6
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-ggpf-24jw-3fcw
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-rh4j-5rhw-hr54
  - https://github.com/vllm-project/vllm