info:
  name: vllm
  cve: CVE-2024-8939
  summary: vLLM Denial of Service via the best_of parameter
  details: |
    A vulnerability was found in the ilab model serve component, where improper handling of the best_of parameter in the vllm JSON web API can lead to a Denial of Service (DoS). The API used for LLM-based sentence or chat completion accepts a best_of parameter to return the best completion from several options. When this parameter is set to a large value, the API does not handle timeouts or resource exhaustion properly, allowing an attacker to cause a DoS by consuming excessive system resources. This leads to the API becoming unresponsive, preventing legitimate users from accessing the service.
  cvss: CVSS:3.1/AV:L/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to vllm >= 0.5.0.post2
    2. Implement input validation to restrict the best_of parameter to reasonable values
    3. Monitor system resources and implement rate limiting to prevent excessive consumption
rule: version < "0.5.0.post2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8939
  - https://github.com/vllm-project/vllm/issues/6137
  - https://access.redhat.com/security/cve/CVE-2024-8939
  - https://bugzilla.redhat.com/show_bug.cgi?id=2312782
  - https://github.com/vllm-project/vllm