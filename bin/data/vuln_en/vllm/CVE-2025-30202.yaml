info:
  name: vllm
  cve: CVE-2025-30202
  summary: Data exposure via ZeroMQ on multi-node vLLM deployment
  details: |
    In a multi-node vLLM deployment, vLLM uses ZeroMQ for some multi-node communication purposes. The primary vLLM host opens an `XPUB` ZeroMQ socket and binds it to ALL interfaces. While the socket is always opened for a multi-node deployment, it is only used when doing tensor parallelism across multiple hosts.
    Any client with network access to this host can connect to this `XPUB` socket unless its port is blocked by a firewall. Once connected, these arbitrary clients will receive all of the same data broadcasted to all of the secondary vLLM hosts. This data is internal vLLM state information that is not useful to an attacker.
    By potentially connecting to this socket many times and not reading data published to them, an attacker can also cause a denial of service by slowing down or potentially blocking the publisher.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to vllm>=0.8.5
    2. Ensure that only the other vLLM hosts are able to connect to the TCP port used for the `XPUB` socket.
    3. Block the port used by the `XPUB` socket at the firewall if tensor parallelism across multiple hosts is not required.
rule: version >= "0.5.2" && version < "0.8.5"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-9f8f-2vmf-885j
  - https://nvd.nist.gov/vuln/detail/CVE-2025-30202
  - https://github.com/vllm-project/vllm/pull/17197
  - https://github.com/vllm-project/vllm/pull/6183
  - https://github.com/vllm-project/vllm/commit/a0304dc504c85f421d38ef47c64f83046a13641c
  - https://github.com/vllm-project/vllm