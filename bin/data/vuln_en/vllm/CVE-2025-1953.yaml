info:
  name: vllm
  cve: CVE-2025-1953
  summary: Insufficiently random values in Prefix Caching component of vLLM AIBrix
  details: |
    A vulnerability has been found in the Prefix Caching component of vLLM AIBrix 0.2.0,
    specifically in the file pkg/plugins/gateway/prefixcacheindexer/hash.go. The manipulation
    leads to insufficiently random values, potentially affecting the security of the system.
    The complexity of an attack is rather high, and exploitation appears to be difficult.
  cvss: CVSS:3.1/AV:A/AC:H/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: LOW
  security_advise: |
    1. Upgrade to vLLM AIBrix version 0.3.0 or later.
    2. Monitor for any further updates or patches related to this vulnerability.
rule: version < "0.3.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1953
  - https://github.com/vllm-project/aibrix/issues/749
  - https://github.com/vllm-project/aibrix/pull/752
  - https://vuldb.com/?ctiid.298543