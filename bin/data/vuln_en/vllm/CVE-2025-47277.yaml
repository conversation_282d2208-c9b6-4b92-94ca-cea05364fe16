info:
  name: vllm
  cve: CVE-2025-47277
  summary: Remote Code Execution Vulnerability in vLLM Distributed Node Mode via PyNcclPipe Communication Service
  details: |
   Tencent Zhuque Lab Found: vLLM supports peer-to-peer communication for data transmission between distributed nodes. A remote code execution vulnerability exists in the PyNcclPipe Communication Service due to improper handling of client-provided serialized data, allowing attackers to gain server control privileges.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately update vLLM to the latest version.
    2. Review and patch the deserialization process to ensure it only handles trusted data.
    3. Implement network-level protections to restrict access to the PyNcclPipe Communication Service.
rule: version <= "v0.8.5"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-47277