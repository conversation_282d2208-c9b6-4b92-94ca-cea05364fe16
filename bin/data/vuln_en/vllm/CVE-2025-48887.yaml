info:
  name: vllm
  cve: CVE-2025-48887
  summary: vLLM has a Regular Expression Denial of Service (ReDoS) Vulnerability in `pythonic_tool_parser.py`
  details: |
    A Regular Expression Denial of Service (ReDoS) vulnerability exists in the file [`vllm/entrypoints/openai/tool_parsers/pythonic_tool_parser.py`](https://github.com/vllm-project/vllm/blob/main/vllm/entrypoints/openai/tool_parsers/pythonic_tool_parser.py) of the vLLM project. The root cause is the use of a highly complex and nested regular expression for tool call detection, which can be exploited by an attacker to cause severe performance degradation or make the service unavailable.
    The following regular expression is used to match tool/function call patterns:
    ```
    r\"\\[([a-zA-Z]+\\w*\\(([a-zA-Z]+\\w*=.*,\\s*)*([a-zA-Z]+\\w*=.*\\s)?\\),\\s*)*([a-zA-Z]+\\w*\\(([a-zA-Z]+\\w*=.*,\\s*)*([a-zA-Z]+\\w*=.*\\s*)?\\)\\s*)+\\]\"\"
    ```
    This pattern contains multiple nested quantifiers (`*`, `+`), optional groups, and inner repetitions which make it vulnerable to catastrophic backtracking.
    **Attack Example:**
    A malicious input such as  
    ```
    [A(A=\t)A(A=,\t\t)A(A=,\t\t)A(A=,\t\t)... (repeated dozens of times) ...]
    ```
    or
    ```
    "[A(A=\" + \"\\t)A(A=,\\t\" * repeat
    ```
    can cause the regular expression engine to consume CPU exponentially with the input length, effectively freezing or crashing the server (DoS).
    **Proof of Concept:**
    A Python script demonstrates that matching such a crafted string with the above regex results in exponential time complexity. Even moderate input lengths can bring the system to a halt.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to vllm>=0.9.0
    2. Review and simplify regular expressions used in tool call detection to prevent catastrophic backtracking
    3. Monitor CPU usage and implement rate limiting to mitigate the impact of potential DoS attacks
rule: version >= "0.6.4" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-w6q7-j642-7c25
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48887
  - https://github.com/vllm-project/vllm/pull/18454
  - https://github.com/vllm-project/vllm/commit/4fc1bf813ad80172c1db31264beaef7d93fe0601
  - https://github.com/vllm-project/vllm