info:
  name: vllm
  cve: CVE-2025-29783
  summary: v<PERSON><PERSON> Allows Remote Code Execution via Mooncake Integration
  details: |
    When vLLM is configured to use Mooncake, unsafe deserialization exposed directly over ZMQ/TCP on all network interfaces will allow attackers to execute remote code on distributed hosts.
    ### Details
    1. Pickle deserialization vulnerabilities are [well documented](https://docs.python.org/3/library/pickle.html).
    2. The [mooncake pipe](https://github.com/vllm-project/vllm/blob/9bebc9512f9340e94579b9bd69cfdc452c4d5bb0/vllm/distributed/kv_transfer/kv_pipe/mooncake_pipe.py#L206) is exposed over the network (by design to enable disaggregated prefilling across distributed environments) using ZMQ over TCP, greatly increasing exploitability. Further, the mooncake integration opens these sockets listening on all interfaces on the host, meaning it can not be configured to only use a private, trusted network.
    3. The root problem is [`recv_tensor()`](https://github.com/vllm-project/vllm/blob/9bebc9512f9340e94579b9bd69cfdc452c4d5bb0/vllm/distributed/kv_transfer/kv_pipe/mooncake_pipe.py#L257) calls [`_recv_impl`](https://github.com/vllm-project/vllm/blob/9bebc9512f9340e94579b9bd69cfdc452c4d5bb0/vllm/distributed/kv_transfer/kv_pipe/mooncake_pipe.py#L244) which passes the raw network bytes to `pickle.loads()`. Additionally, it does not appear that there are any controls (network, authentication, etc) to prevent arbitrary users from sending this payload to the affected service.
    ### Impact
    This is a remote code execution vulnerability impacting any deployments using Mooncake to distribute KV across distributed hosts.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to vllm >= 0.8.0
    2. Review and restrict network access to the Mooncake integration
    3. Implement additional security controls around the Mooncake pipe to prevent unauthorized access
rule: version < "0.8.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-x3m8-f7g5-qhm7
  - https://github.com/vllm-project/vllm/pull/14228
  - https://github.com/vllm-project/vllm/commit/288ca110f68d23909728627d3100e5a8db820aa2
  - https://github.com/vllm-project/vllm