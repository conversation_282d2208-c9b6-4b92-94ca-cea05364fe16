info:
  name: vllm
  cve: CVE-2025-29770
  summary: vLLM denial of service via outlines unbounded cache on disk
  details: |
    The outlines library, used by vLLM for structured output support, includes an optional cache for compiled grammars on the local filesystem. This cache is enabled by default in vLLM and can be exploited by sending a stream of short decoding requests with unique schemas, causing the cache to grow uncontrollably and potentially leading to a Denial of Service (DoS) if the filesystem runs out of space. This issue affects only the V0 engine of vLLM.
    Affected Code: [vllm/model_executor/guided_decoding/outlines_logits_processors.py](https://github.com/vllm-project/vllm/blob/53be4a863486d02bd96a59c674bbec23eec508f6/vllm/model_executor/guided_decoding/outlines_logits_processors.py)
    Note: Even if vLLM is configured to use a different backend by default, it is still possible to choose outlines on a per-request basis using the `guided_decoding_backend` key.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to vLLM version 0.8.0 or later.
    2. If using the outlines cache is necessary, set the `VLLM_V0_USE_OUTLINES_CACHE` environment variable to `1`.
    3. Prevent untrusted access to the OpenAI compatible API server.
rule: version >= "0" && version < "0.8.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-mgrm-fgjv-mhv8
  - https://github.com/vllm-project/vllm/pull/14837
  - https://github.com/vllm-project/vllm/blob/53be4a863486d02bd96a59c674bbec23eec508f6/vllm/model_executor/guided_decoding/outlines_logits_processors.py