info:
  name: vllm
  cve: CVE-2025-46570
  summary: Potential Timing Side-Channel Vulnerability in vLLM’s Chunk-Based Prefix Caching
  details: |
    This issue arises from the prefix caching mechanism, which may expose the system to a timing side-channel attack.
    When a new prompt is processed, if the PageAttention mechanism finds a matching prefix chunk, the prefill process speeds up, which is reflected in the TTFT (Time to First Token). Our tests revealed that the timing differences caused by matching chunks are significant enough to be recognized and exploited.
    For instance, if the victim has submitted a sensitive prompt or if a valuable system prompt has been cached, an attacker sharing the same backend could attempt to guess the victim's input. By measuring the TTFT based on prefix matches, the attacker could verify if their guess is correct, leading to potential leakage of private information.
    Unlike token-by-token sharing mechanisms, vLLM’s chunk-based approach (PageAttention) processes tokens in larger units (chunks). In our tests, with chunk_size=2, the timing differences became noticeable enough to allow attackers to infer whether portions of their input match the victim's prompt at the chunk level.
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:R/S:U/C:L/I:N/A:N
  severity: LOW
  security_advise: |
    1. Upgrade to vllm>=0.9.0
    2. Monitor for any suspicious activity related to timing side-channel attacks
    3. Review and adjust system configurations to minimize potential attack vectors
rule: version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-4qjh-9fv9-r85r
  - https://nvd.nist.gov/vuln/detail/CVE-2025-46570
  - https://github.com/vllm-project/vllm/pull/17045
  - https://github.com/vllm-project/vllm/commit/77073c77bc2006eb80ea6d5128f076f5e6c6f54f
  - https://github.com/vllm-project/vllm