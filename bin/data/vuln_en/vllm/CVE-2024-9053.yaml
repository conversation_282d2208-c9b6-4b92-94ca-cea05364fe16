info:
  name: vllm
  cve: CVE-2024-9053
  summary: Remote code execution vulnerability in vllm-project due to insecure pickle deserialization
  details: |
    The vulnerability in vllm-project version 0.6.0 affects the AsyncEngineRPCServer() RPC server entrypoints.
    The core functionality run_server_loop() calls the function _make_handler_coro(), which directly uses
    cloudpickle.loads() on received messages without any sanitization. This can result in remote code execution
    by deserializing malicious pickle data.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to vllm-project version 0.6.1 or later.
    2. Implement input validation and sanitization for all deserialized data.
    3. Consider alternative serialization methods that are less prone to abuse, such as JSON with proper schema validation.
rule: version < "0.6.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9053
  - https://huntr.com/bounties/75a544f3-34a3-4da0-b5a3-1495cb031e09