info:
  name: comfyui
  cve: CVE-2024-10099
  summary: Stored cross-site scripting (XSS) vulnerability in ComfyUI
  details: |
    A stored cross-site scripting (XSS) vulnerability exists in comfyanonymous/comfyui version 0.2.2 and possibly earlier. 
    The vulnerability occurs when an attacker uploads an HTML file containing a malicious XSS payload via the `/api/upload/image` endpoint. 
    The payload is executed when the file is viewed through the `/view` API endpoint, leading to potential execution of arbitrary JavaScript code.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to comfyui >=0.2.3
    2. Implement server-side validation for uploaded files to prevent malicious content
    3. Sanitize all user-generated content before rendering in the application
rule: version < "0.2.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10099
  - https://huntr.com/bounties/14fb8c9a-692a-4d8c-b4b2-24c6f91a383c