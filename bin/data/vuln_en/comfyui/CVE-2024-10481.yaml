info:
  name: comfyui
  cve: CVE-2024-10481
  summary: CSRF vulnerability in comfyanonymous/comfyui versions up to v0.2.2
  details: |
    A CSRF vulnerability exists in comfyanonymous/comfyui versions up to v0.2.2. This vulnerability allows attackers to host malicious websites that, when visited by authenticated ComfyUI users, can perform arbitrary API requests on behalf of the user. This can be exploited to perform actions such as uploading arbitrary files via the `/upload/image` endpoint. The lack of CSRF protections on API endpoints like `/upload/image`, `/prompt`, and `/history` leaves users vulnerable to unauthorized actions, which could be combined with other vulnerabilities such as stored-XSS to further compromise user sessions.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to comfyui >= v0.2.3
    2. Implement CSRF protection on all API endpoints
    3. Regularly review and update security measures to prevent similar vulnerabilities
rule: version <= "0.2.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10481
  - https://huntr.com/bounties/f4d5bfb5-6ff1-4356-b81f-f8c01d2e6ded