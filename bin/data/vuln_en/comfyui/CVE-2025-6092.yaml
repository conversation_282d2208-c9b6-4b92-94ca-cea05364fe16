info:
  name: comfyui
  cve: CVE-2025-6092
  summary: Cross-site scripting vulnerability in comfyui up to version 0.3.39
  details: |
    A vulnerability was discovered in comfyui versions up to 0.3.39 affecting an unknown functionality of the file /upload/image. Manipulation of the argument image leads to cross-site scripting, allowing remote attackers to execute arbitrary scripts in the context of the affected system.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to comfyui version 0.3.40 or later.
    2. Implement input validation to prevent malicious script injection in the /upload/image endpoint.
    3. Monitor for any suspicious activity related to the /upload/image endpoint.
rule: version < "0.3.40"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-6092
  - https://gist.github.com/superboy-zjc/96f0d56da584d840ba18355cbea96ac4
  - https://vuldb.com/?ctiid.312559
  - https://vuldb.com/?id.312559
  - https://vuldb.com/?submit.588224