info:
  name: comfyui
  cve: CVE-2024-12882
  summary: Non-blind SSRF vulnerability in comfyui
  details: |
    The non-blind Server-Side Request Forgery (SSRF) vulnerability in comfyanonymous/comfyui version v0.2.4
    can be exploited by combining the REST APIs `POST /internal/models/download` and `GET /view`.
    This allows attackers to abuse the victim server's credentials to access unauthorized web resources.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to comfyui >= v0.2.5
    2. Implement strict input validation for all REST API endpoints
    3. Restrict access to internal APIs to authorized users only
rule: version < "0.2.5"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12882
  - https://huntr.com/bounties/e8768cb1-6a80-40c1-9cdf-bcd21f01f85a