info:
  name: comfyui
  cve: CVE-2025-6107
  summary: Vulnerability in comfyui 0.3.40 allows remote manipulation of object attributes.
  details: |
    A vulnerability was discovered in the `set_attr` function within `/comfy/utils.py` of comfyui version 0.3.40. This issue enables attackers to dynamically determine object attributes remotely. Although the attack complexity is high and exploitability is considered difficult, the vulnerability has been publicly disclosed and may be exploited. The vendor was informed but did not respond.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:N/I:N/A:L
  severity: LOW
  security_advise: |
    1. Upgrade to a newer version of comfyui that addresses this vulnerability.
    2. Review and modify the `set_attr` function in `/comfy/utils.py` to prevent dynamic attribute manipulation.
    3. Implement additional security measures to monitor and restrict remote access to sensitive functions.
rule: version <= "0.3.40"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-6107
  - https://gist.github.com/superboy-zjc/f71b84ed074260a5e459581caa2f1fb2
  - https://gist.github.com/superboy-zjc/f71b84ed074260a5e459581caa2f1fb2#proof-of-concept
  - https://vuldb.com/?ctiid.312576
  - https://vuldb.com/?id.312576
  - https://vuldb.com/?submit.590921