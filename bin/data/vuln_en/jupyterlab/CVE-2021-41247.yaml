info:
  name: jupyterlab
  cve: CVE-2021-41247
  summary: Incomplete JupyterHub logout with simultaneous JupyterLab sessions
  details: |
    Users of JupyterLab with JupyterHub who have multiple JupyterLab tabs open in the same browser session may experience incomplete logout from the single-user server. Fresh credentials for the single-user server (not the Hub) can be reinstated after logout if another active JupyterLab session is open during the logout process.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to JupyterHub version 1.5 or higher.
    2. For distributed deployments, ensure that the _user_ environment's jupyterhub is patched.
    3. As a workaround, ensure only one JupyterLab tab is open during logout.
rule: version >= "1.0.0" && version < "1.5.0"
references:
  - https://github.com/jupyterhub/jupyterhub/security/advisories/GHSA-cw7p-q79f-m2v7
  - https://nvd.nist.gov/vuln/detail/CVE-2021-41247
  - https://github.com/jupyterhub/jupyterhub/commit/5ac9e7f73a6e1020ffddc40321fc53336829fe27
  - https://github.com/jupyterhub/jupyterhub
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyterhub/PYSEC-2021-386.yaml