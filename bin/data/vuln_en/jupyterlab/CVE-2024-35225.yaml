info:
  name: jupyterlab
  cve: CVE-2024-35225
  summary: Jupyter Server Proxy has a reflected XSS issue in host parameter
  details: |
    There is a reflected cross-site scripting (XSS) issue in `jupyter-server-proxy`. The `/proxy` endpoint accepts a `host` path segment in the format `/proxy/<host>`. When this endpoint is called with an invalid `host` value, `jupyter-server-proxy` replies with a response that includes the value of `host`, without sanitization. A third-party actor can leverage this by sending a phishing link with an invalid `host` value containing custom JavaScript to a user. When the user clicks this phishing link, the browser renders the response of `GET /proxy/<host>`, which runs the custom JavaScript contained in `host` set by the actor.
    As any arbitrary JavaScript can be run after the user clicks on a phishing link, this issue permits extensive access to the user's JupyterLab instance for an actor. This issue exists in the latest release of `jupyter-server-proxy`, currently `v4.1.2`.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to jupyter-server-proxy >=4.2.0 or >=3.2.4
    2. If upgrading is not immediately possible, disable the `jupyter-server-proxy` extension with:
       ```
       jupyter server extension disable jupyter-server-proxy
       ```
rule: version >= "3.0.0" && version <= "4.1.2"
references:
  - https://github.com/jupyterhub/jupyter-server-proxy/security/advisories/GHSA-fvcq-4x64-hqxr
  - https://nvd.nist.gov/vuln/detail/CVE-2024-35225
  - https://github.com/jupyterhub/jupyter-server-proxy/commit/7abc9dc5bbb0b4b440548a5375261b8b8192fc22
  - https://github.com/jupyterhub/jupyter-server-proxy/commit/ff78128087e73fb9d0909e1366f8bf051e8ea878
  - https://github.com/jupyterhub/jupyter-server-proxy
  - https://github.com/jupyterhub/jupyter-server-proxy/blob/62a290f08750f7ae55a0c29ca339c9a39a7b2a7b/jupyter_server_proxy/handlers.py#L328
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server-proxy/PYSEC-2024-236.yaml