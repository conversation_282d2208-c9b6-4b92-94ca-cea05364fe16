info:
  name: jupyterlab
  cve: CVE-2024-22420
  summary: <PERSON><PERSON><PERSON><PERSON><PERSON> vulnerable to SXSS in Markdown Preview
  details: |
    The vulnerability depends on user interaction by opening a malicious notebook with Markdown cells, or Markdown file using JupyterLab preview feature.
    
    A malicious user can access any data that the attacked user has access to as well as perform arbitrary requests acting as the attacked user.
    
    JupyterLab v4.0.11 was patched.
    
    Users can either disable the table of contents extension by running:
    
    ```bash
    jupyter labextension disable @jupyterlab/toc-extension:registry
    ```
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to jupyterlab>=4.0.11
    2. Disable the table of contents extension by running:
       ```bash
       jupyter labextension disable @jupyterlab/toc-extension:registry
       ```
rule: version >= "4.0.0" && version < "4.0.11"
references:
  - https://github.com/jupyterlab/jupyterlab/security/advisories/GHSA-4m77-cmpx-vjc4
  - https://nvd.nist.gov/vuln/detail/CVE-2024-22420
  - https://github.com/jupyterlab/jupyterlab/commit/dda0033cd49449572d077bbecd33b18d8d05f48a
  - https://github.com/jupyterlab/jupyterlab/commit/e1b3aabab603878e46add445a3114e838411d2df
  - https://github.com/jupyterlab/jupyterlab
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/UQJKNRDRFMKGVRIYNNN6CKMNJDNYWO2H