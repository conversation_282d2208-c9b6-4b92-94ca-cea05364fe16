info:
  name: jupyterlab
  cve: CVE-2021-32797
  summary: Ju<PERSON>terLab XSS due to lack of sanitization of the action attribute of an html <form>
  details: |
    Untrusted notebook can execute code on load. This is a remote code execution, but requires user action to open a notebook.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to jupyterlab>=3.1.4 or jupyterlab>=3.0.17 or jupyterlab>=2.3.2 or jupyterlab>=2.2.10 or jupyterlab>=1.2.21
    2. Ensure that all notebooks are from trusted sources
    3. Implement Content Security Policy (CSP) to restrict form submissions
rule: version < "1.2.21" || (version >= "2.0.0a0" && version < "2.2.10") || (version >= "2.3.0a0" && version < "2.3.2") || (version >= "3.0.0a0" && version < "3.0.17") || (version >= "3.1.0a0" && version < "3.1.4")
references:
  - https://github.com/google/security-research/security/advisories/GHSA-c469-p3jp-2vhx
  - https://github.com/jupyterlab/jupyterlab/security/advisories/GHSA-4952-p58q-6crx
  - https://nvd.nist.gov/vuln/detail/CVE-2021-32797
  - https://github.com/jupyterlab/jupyterlab/commit/504825938c0abfa2fb8ff8d529308830a5ae42ed
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyterlab/PYSEC-2021-130.yaml