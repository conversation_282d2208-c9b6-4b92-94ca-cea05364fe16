info:
  name: jupyterlab
  cve: CVE-2021-41134
  summary: Stored XSS in Jupyter nbdime
  details: |
    Improper handling of user controlled input caused a stored cross-site scripting (XSS) vulnerability. All previous versions of nbdime are affected.
    Security patches have been released for each of the major versions of the nbdime packages since version 1.x of the nbdime python package.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade nbdime python package to:
       - 1.x: v1.1.1 or higher
       - 2.x: v2.1.1 or higher
       - 3.x: v3.1.1 or higher
    2. Upgrade nbdime npm package to:
       - 5.x: v5.0.2 or higher
       - 6.x: v6.1.2 or higher
    3. Upgrade nbdime-jupyterlab npm package to:
       - 1.x: v1.0.1 or higher
       - 2.x: v2.1.1 or higher
rule: version < "1.1.1" || (version >= "2.0.0" && version < "2.1.1") || (version >= "3.0.0" && version < "3.1.1") || version < "5.0.2" || version >= "6.0.0" && version < "6.1.2" || version < "1.0.1" || version >= "2.0.0" && version < "2.1.1"
references:
  - https://github.com/jupyter/nbdime/security/advisories/GHSA-p6rw-44q7-3fw4
  - https://nvd.nist.gov/vuln/detail/CVE-2021-41134
  - https://github.com/jupyter/nbdime/commit/e44a5cc7677f24b45ebafc756db49058c2f750ea
  - https://github.com/jupyter/nbdime
  - https://github.com/pypa/advisory-database/tree/main/vulns/nbdime/PYSEC-2021-428.yaml