info:
  name: jupyterlab
  cve: CVE-2024-43805
  summary: HTML injection in Jupyter Notebook and JupyterLab leading to DOM Clobbering
  details: |
    The vulnerability depends on user interaction by opening a malicious notebook with Markdown cells, or Markdown file using JupyterLab preview feature.
    
    A malicious user can access any data that the attacked user has access to as well as perform arbitrary requests acting as the attacked user.
    
    JupyterLab v3.6.8, v4.2.5 and Jupyter Notebook v7.2.2 were patched.
    
    There is no workaround for the underlying DOM Clobbering susceptibility. However, select plugins can be disabled on deployments which cannot update in a timely fashion to minimise the risk. These are:
    - `@jupyterlab/mathjax-extension:plugin` - users will loose ability to preview mathematical equations 
    - `@jupyterlab/markdownviewer-extension:plugin` - users will loose ability to open Markdown previews
    - `@jupyterlab/mathjax2-extension:plugin` (if installed with optional `jupyterlab-mathjax2` package) - an older version of the mathjax plugin for JupyterLab 4.x
    
    To disable these extensions run:
    
    ```bash
    jupyter labextension disable @jupyterlab/markdownviewer-extension:plugin
    jupyter labextension disable @jupyterlab/mathjax-extension:plugin
    jupyter labextension disable @jupyterlab/mathjax2-extension:plugin
    ```
    
    To confirm that the plugins were disabled run:
    
    ```bash
    jupyter labextension list
    ```
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to JupyterLab >= 3.6.8 or >= 4.2.5
    2. Upgrade to Jupyter Notebook >= 7.2.2
    3. Disable the following extensions if updating is not immediately feasible:
       - `@jupyterlab/mathjax-extension:plugin`
       - `@jupyterlab/markdownviewer-extension:plugin`
       - `@jupyterlab/mathjax2-extension:plugin`
rule: (version >= "3.6.8" && version < "4.0.0") || (version >= "4.2.5")
references:
  - https://github.com/jupyterlab/jupyterlab/security/advisories/GHSA-9q39-rmj3-p4r2
  - https://nvd.nist.gov/vuln/detail/CVE-2024-43805
  - https://github.com/jupyterlab/jupyterlab/commit/06ad9de836f155add7d3d651ef936cc4c5ea8093
  - https://github.com/jupyterlab/jupyterlab/commit/88e24baac551196f9cb3de16bd060a7ab1597674
  - https://github.com/jupyterlab/jupyterlab