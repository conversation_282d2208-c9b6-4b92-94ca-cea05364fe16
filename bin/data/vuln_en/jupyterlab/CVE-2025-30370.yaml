info:
  name: jupyterlab
  cve: CVE-2025-30370
  summary: jupyterlab-git has a command injection vulnerability in "Open Git Repository in Terminal"
  details: |
    On many platforms, a third party can create a Git repository under a name that includes a shell command substitution string. If a user starts `jupyter-lab` in a parent directory of this inappropriately-named Git repository and clicks "Git > Open Git Repository in Terminal", then the injected command is run in the user's shell without permission. This issue allows for arbitrary code execution via command injection, potentially leading to modifying files, exfiltrating data, halting services, or compromising the server's security rules.
  cvss: CVSS:3.1/AV:L/AC:H/PR:L/UI:R/S:C/C:L/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to jupyterlab-git >= 0.51.1
    2. Disable terminals on `jupyter-server` level:
       ```python
       c.ServerApp.terminals_enabled = False
       ```
    3. Disable the terminals server extension:
       ```bash
       jupyter server extension disable jupyter_server_terminals
       ```
    4. Disable the lab extension:
       ```bash
       jupyter labextension disable @jupyterlab/terminal-extension
       ```
rule: version >= "0" && version < "0.51.1"
references:
  - https://github.com/jupyterlab/jupyterlab-git/security/advisories/GHSA-cj5w-8mjf-r5f8
  - https://nvd.nist.gov/vuln/detail/CVE-2025-30370
  - https://github.com/jupyterlab/jupyterlab-git/pull/1196
  - https://github.com/jupyterlab/jupyterlab-git/commit/b46482993f76d3a546015c6a94ebed8b77fc2376
  - https://github.com/jupyterlab/jupyterlab-git
  - https://github.com/jupyterlab/jupyterlab-git/blob/7eb3b06f0092223bd5494688ec264527bbeb2195/src/commandsAndMenu.tsx#L175-L184