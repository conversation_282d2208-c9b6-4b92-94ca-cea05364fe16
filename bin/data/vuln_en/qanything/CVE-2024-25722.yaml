info:
  name: qanything
  cve: CVE-2024-25722
  summary: SQL Injection vulnerability in qanything.ai before 1.2.0
  details: |
    The qanything_kernel/connector/database/mysql/mysql_client.py in qanything.ai before version 1.2.0 is vulnerable to SQL Injection attacks.
    This allows attackers to execute arbitrary SQL commands, potentially leading to unauthorized data access or modification.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to qanything.ai version 1.2.0 or later.
    2. Review and sanitize all user inputs to prevent SQL Injection attacks.
    3. Implement least privilege access controls for database connections.
rule: version < "1.2.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-25722
  - https://github.com/netease-youdao/QAnything/commit/35753b892c2c4361b318d68dfa3e251c85ce889c
  - https://github.com/netease-youdao/QAnything/compare/v1.1.1...v1.2.0