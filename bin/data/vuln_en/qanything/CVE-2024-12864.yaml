info:
  name: qanything
  cve: CVE-2024-12864
  summary: Denial of Service (DoS) vulnerability in file upload feature
  details: |
    A Denial of Service (DoS) vulnerability was discovered in the file upload feature of netease-youdao/qanything version v2.0.0. The vulnerability is due to improper handling of form-data with a large filename in the file upload request. An attacker can exploit this vulnerability by sending a large filename, causing the server to become overwhelmed and unavailable for legitimate users. This attack does not require authentication, making it highly scalable and increasing the risk of exploitation.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to qanything >= v2.0.1
    2. Implement server-side validation for filename sizes in file upload requests
    3. Monitor server logs for unusual activity related to file uploads
rule: version < "2.0.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12864
  - https://huntr.com/bounties/365c3b9a-180c-4bb5-98d8-dbd78d93fcb7