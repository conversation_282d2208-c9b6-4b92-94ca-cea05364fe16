info:
  name: qanything
  cve: CVE-2024-8024
  summary: CORS misconfiguration vulnerability in netease-youdao/qanything
  details: |
    A CORS misconfiguration vulnerability exists in netease-youdao/qanything version 1.4.1.
    This vulnerability allows an attacker to bypass the Same-Origin Policy,
    potentially leading to sensitive information exposure.
    Properly implementing a restrictive CORS policy is crucial to prevent such security issues.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to qanything >= 1.4.2 or later versions that fix the CORS misconfiguration.
    2. Implement a strict CORS policy that only allows trusted origins.
    3. Regularly review and update CORS configurations to ensure they adhere to security best practices.
rule: version < "1.4.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8024
  - https://huntr.com/bounties/bda53fab-88aa-4e03-8d9d-4cf50a98ffc7