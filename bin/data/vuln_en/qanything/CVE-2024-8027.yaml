info:
  name: qanything
  cve: CVE-2024-8027
  summary: Stored Cross-Site Scripting (XSS) vulnerability in netease-youdao/QAnything
  details: |
    A stored Cross-Site Scripting (XSS) vulnerability exists in netease-youdao/QAnything. 
    Attackers can upload malicious knowledge files to the knowledge base, which can trigger 
    XSS attacks during user chats. This vulnerability affects all versions prior to the fix.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Update to the latest version of QAnything that includes the fix for this vulnerability.
    2. Implement input validation to prevent malicious scripts from being uploaded to the knowledge base.
    3. Regularly monitor and audit the knowledge base for any signs of unauthorized or malicious content.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8027
  - https://huntr.com/bounties/cf75f024-3d64-416d-adfe-c4255d7c3f34