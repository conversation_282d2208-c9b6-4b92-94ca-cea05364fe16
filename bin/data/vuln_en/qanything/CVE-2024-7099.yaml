info:
  name: qanything
  cve: CVE-2024-7099
  summary: SQL injection vulnerability in netease-youdao/qanything
  details: |
    netease-youdao/qanything version 1.4.1 contains a vulnerability where unsafe data obtained from user input is concatenated in SQL queries, leading to SQL injection. 
    The affected functions include `get_knowledge_base_name`, `from_status_to_status`, `delete_files`, and `get_file_by_status`. 
    An attacker can exploit this vulnerability to execute arbitrary SQL queries, potentially stealing information from the database.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to qanything version 1.4.2 or later.
    2. Review and sanitize all user inputs to prevent SQL injection.
    3. Implement parameterized queries or prepared statements for database interactions.
rule: version < "1.4.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7099
  - https://github.com/netease-youdao/qanything/commit/a87354f09d93e95350fb45eb343dc75454387554
  - https://huntr.com/bounties/bc98983e-06cc-4a4b-be01-67e5010cb2c1