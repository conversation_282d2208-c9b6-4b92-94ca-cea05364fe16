info:
  name: qanything
  cve: CVE-2024-12866
  summary: Local file inclusion vulnerability in netease-youdao/qanything
  details: |
    A local file inclusion vulnerability exists in netease-youdao/qanything version v2.0.0. This vulnerability allows an attacker to read arbitrary files on the file system, which can lead to remote code execution by retrieving private SSH keys, reading private files, source code, and configuration files.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to qanything >= v2.0.1 (assuming a fix is available in this version or later)
    2. Implement strict file inclusion checks to prevent arbitrary file reads
    3. Regularly audit and update dependencies to patch known vulnerabilities
rule: version < "2.0.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12866
  - https://huntr.com/bounties/c23da7c7-a226-40a2-83db-6a8ab1b2ef64