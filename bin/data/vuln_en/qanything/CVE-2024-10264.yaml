info:
  name: qanything
  cve: CVE-2024-10264
  summary: HTTP Request Smuggling vulnerability in netease-youdao/qanything
  details: |
    The vulnerability allows attackers to exploit inconsistencies in the interpretation of HTTP requests between a proxy and a server. This can lead to unauthorized access, bypassing security controls, session hijacking, data leakage, and potentially arbitrary code execution.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to netease-youdao/qanything version 1.4.2 or higher.
    2. Implement strict HTTP request validation to prevent smuggling attacks.
    3. Regularly update and patch all software components to mitigate potential vulnerabilities.
rule: version < "1.4.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10264
  - https://huntr.com/bounties/988247d5-fd60-4d85-845a-e867d62c0d02