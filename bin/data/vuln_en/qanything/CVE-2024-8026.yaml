info:
  name: qanything
  cve: CVE-2024-8026
  summary: CSRF vulnerability in netease-youdao/qanything backend API
  details: |
    A Cross-Site Request Forgery (CSRF) vulnerability exists in the backend API of netease-youdao/qanything, as of commit d9ab8bc.
    The backend server has overly permissive CORS headers, allowing all cross-origin calls. This vulnerability affects all backend endpoints,
    enabling actions such as creating, uploading, listing, deleting files, and managing knowledge bases.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Restrict CORS headers to only allow trusted origins.
    2. Implement CSRF tokens for all state-changing requests.
    3. Regularly update and patch the backend API to address security vulnerabilities.
rule: version < "d9ab8bc"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8026
  - https://huntr.com/bounties/e57f1e32-0fe5-4997-926c-587461aa6274