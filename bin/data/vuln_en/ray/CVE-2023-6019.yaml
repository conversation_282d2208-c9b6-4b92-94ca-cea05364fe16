info:
  name: ray
  cve: CVE-2023-6019
  summary: Ray OS Command Injection vulnerability
  details: |
    A command injection exists in <PERSON>'s cpu_profile URL parameter allowing attackers to execute os commands on the system running the ray dashboard remotely without authentication.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to ray>=2.8.1
    2. Review and restrict access to the Ray dashboard to authorized users only
    3. Implement input validation to prevent command injection attacks
rule: version >= "0" && version < "2.8.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6019
  - https://github.com/ray-project/ray
  - https://github.com/ray-project/ray/releases/tag/ray-2.8.1
  - https://huntr.com/bounties/d0290f3c-b302-4161-89f2-c13bb28b4cfe
  - https://www.anyscale.com/blog/update-on-ray-cves-cve-2023-6019-cve-2023-6020-cve-2023-6021-cve-2023-48022-cve-2023-48023