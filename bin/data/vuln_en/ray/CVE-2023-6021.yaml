info:
  name: ray
  cve: CVE-2023-6021
  summary: Ray Path Traversal vulnerability
  details: |
    LFI in <PERSON>'s log API endpoint allows attackers to read any file on the server without authentication.
    The issue is fixed in version 2.8.1+.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
  severity: CRITICAL
  security_advise: |
    1. Upgrade to ray>=2.8.1
    2. Review and restrict access to the log API endpoint
    3. Monitor for any unauthorized file access attempts
rule: version < "2.8.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6021
  - https://github.com/ray-project/ray
  - https://github.com/ray-project/ray/releases/tag/ray-2.8.1
  - https://huntr.com/bounties/5039c045-f986-4cbc-81ac-370fe4b0d3f8
  - https://www.anyscale.com/blog/update-on-ray-cves-cve-2023-6019-cve-2023-6020-cve-2023-6021-cve-2023-48022-cve-2023-48023