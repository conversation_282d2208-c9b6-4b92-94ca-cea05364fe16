info:
  name: ray
  cve: CVE-2023-6020
  summary: Ray Missing Authorization vulnerability
  details: |
    LFI in <PERSON>'s /static/ directory allows attackers to read any file on the server without authentication.
    The issue is fixed in version 2.8.1+.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
  severity: CRITICAL
  security_advise: |
    1. Upgrade to ray>=2.8.1
    2. Review and enforce strict file access controls
    3. Regularly update dependencies to the latest secure versions
rule: version < "2.8.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6020
  - https://github.com/ray-project/ray
  - https://github.com/ray-project/ray/releases/tag/ray-2.8.1
  - https://huntr.com/bounties/83dd8619-6dc3-4c98-8f1b-e620fedcd1f6
  - https://www.anyscale.com/blog/update-on-ray-cves-cve-2023-6019-cve-2023-6020-cve-2023-6021-cve-2023-48022-cve-2023-48023