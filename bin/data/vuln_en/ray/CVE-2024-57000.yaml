info:
  name: ray
  cve: CVE-2024-57000
  summary: Withdrawn Advisory Command injection in Ray (duplicate of CVE-2023-48022)
  details: |
    # Withdrawn Advisory
    This advisory is a duplicate of GHSA-6wgj-66m2-xxp2 / CVE-2023-48022.
    # Original Description
    An issue in Anyscale Inc Ray between v2.9.3 and v2.40.0 allows a remote attacker to execute arbitrary code via a crafted script.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to ray >= 2.40.1
    2. Review and update any custom scripts to prevent injection attacks
    3. Monitor for any further advisories related to Ray security
rule: version >= "2.9.3" && version <= "2.40.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-57000
  - https://github.com/honysyang/Ray.git
  - https://github.com/ray-project/ray