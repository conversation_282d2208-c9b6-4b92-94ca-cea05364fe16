info:
  name: ray
  cve: CVE-2025-1979
  summary: Logging of Redis password in Ray versions before 2.43.0
  details: |
    Versions of the package ray before 2.43.0 are vulnerable to Insertion of Sensitive Information into Log File where the redis password is being logged in the standard logging. If the redis password is passed as an argument, it will be logged and could potentially leak the password.
    This is only exploitable if:
    1) Logging is enabled;
    2) Red<PERSON> is using password authentication;
    3) Those logs are accessible to an attacker, who can reach that redis instance.
    **Note:**
    It is recommended that anyone who is running in this configuration should update to the latest version of Ray, then rotate their redis password.
  cvss: CVSS:3.1/AV:L/AC:H/PR:L/UI:N/S:C/C:H/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Update to the latest version of Ray (2.43.0 or later).
    2. Rotate the Redis password if it has been exposed.
    3. Ensure that logs containing sensitive information are not accessible to unauthorized users.
rule: version < "2.43.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1979
  - https://github.com/ray-project/ray/issues/50266
  - https://github.com/ray-project/ray/pull/50409
  - https://github.com/ray-project/ray/commit/64a2e4010522d60b90c389634f24df77b603d85d
  - https://security.snyk.io/vuln/SNYK-PYTHON-RAY-8745212