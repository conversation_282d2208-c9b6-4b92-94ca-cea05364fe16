info:
  name: anythingllm
  cve: CVE-2024-0759
  summary: Internal network IP scraping vulnerability in AnythingLLM
  details: |
    If AnythingLLM is hosted on an internal network and an attacker is granted manager or admin permissions,
    they could potentially scrape internal IPs of other services on the same network through link scraping.
    This requires the attacker to guess the internal IPs, as range-based scanning (`/*`) is not feasible,
    but could be attempted through brute force.
    There is an inherent risk that other services on the same network might be accessible via simple CURL requests
    without authentication, which could allow for header manipulation or unauthorized access via the link collector.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: |
    1. Restrict manager and admin permissions to trusted personnel only.
    2. Implement network segmentation to isolate Anything<PERSON><PERSON> from other critical services.
    3. Enforce strict authentication for all internal network services.
    4. Regularly audit and monitor network traffic for suspicious activity.
rule: version > "0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0759
  - https://github.com/mintplex-labs/anything-llm/commit/0db6c3b2aa1787a7054ffdaba975474f122c20eb
  - https://huntr.com/bounties/9a978edd-ac94-41fc-8e3e-c35441bdd12b