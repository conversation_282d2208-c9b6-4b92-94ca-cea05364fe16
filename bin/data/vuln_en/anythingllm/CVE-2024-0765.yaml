info:
  name: anythingllm
  cve: CVE-2024-0765
  summary: Data exfiltration via `/export-data` endpoint in AnythingLLM
  details: |
    On a multi-user instance of AnythingLLM, any authenticated user can exploit the `/export-data` endpoint to download and exfiltrate the current state of the system. 
    Although the attacker requires explicit access, they can perform this action regardless of their role. Post-exfiltration, the data is deleted, leaving no evidence of the breach.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: |
    1. Immediately revoke unnecessary export permissions for all users.
    2. Implement strict access controls to limit who can access the `/export-data` endpoint.
    3. Monitor and log all exports to detect and respond to suspicious activities promptly.
    4. Review and update the system to prevent unauthorized data exports.
rule: version > "0" && version < "1.0.0" # Assuming the vulnerability exists in all versions below 1.0.0 based on provided data
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0765
  - https://github.com/mintplex-labs/anything-llm/commit/08d33cfd8fc47c5052b6ea29597c964a9da641e2
  - https://huntr.com/bounties/8978ab27-710c-44ce-bfd8-a2ea416dc786