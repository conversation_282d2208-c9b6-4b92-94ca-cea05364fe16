info:
  name: anythingllm
  cve: CVE-2024-10513
  summary: Path traversal vulnerability in 'document uploads manager' feature
  details: |
    A path traversal vulnerability exists in the 'document uploads manager' feature of mintplex-labs/anything-llm, affecting the latest version prior to 1.2.2. This vulnerability allows users with the 'manager' role to access and manipulate the 'anythingllm.db' database file. By exploiting the vulnerable endpoint '/api/document/move-files', an attacker can move the database file to a publicly accessible directory, download it, and subsequently delete it. This can lead to unauthorized access to sensitive data, privilege escalation, and potential data loss.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to anything-llm version 1.2.2 or later.
    2. Review and strengthen access controls for the 'manager' role.
    3. Implement additional validation for file path inputs to prevent traversal attacks.
rule: version < "1.2.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10513
  - https://github.com/mintplex-labs/anything-llm/commit/47a5c7126c20e2277ee56e2c7ee11990886a40a7
  - https://huntr.com/bounties/ad11cecf-161a-4fb1-986f-6f88272cbb9e