info:
  name: anythingllm
  cve: CVE-2024-3279
  summary: Improper access control in mintplex-labs/anything-llm import endpoint
  details: |
    An improper access control vulnerability exists in the mintplex-labs/anything-llm application's import endpoint.
    This allows anonymous attackers to import their own database file, potentially deleting or spoofing the existing
    `anythingllm.db` file and serving malicious data to users or collecting information about them.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately restrict access to the import functionality to authenticated users only.
    2. Implement strict file validation for all imported database files.
    3. Regularly audit and monitor access logs for any suspicious activity related to database imports.
rule: version > "0" && version < "1.0.0" # Assuming the vulnerability exists in all versions below 1.0.0 based on provided data
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3279
  - https://github.com/mintplex-labs/anything-llm/commit/08d33cfd8fc47c5052b6ea29597c964a9da641e2
  - https://huntr.com/bounties/303c5145-2c14-4945-914a-936be74dd04e