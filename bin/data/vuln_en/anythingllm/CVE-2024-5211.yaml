info:
  name: anythingllm
  cve: CVE-2024-5211
  summary: Path traversal vulnerability in anything-llm
  details: |
    A path traversal vulnerability in mintplex-labs/anything-llm allowed a manager to bypass 
    the `normalizePath()` function, intended to defend against path traversal attacks. 
    This enables the manager to read, delete, or overwrite critical files such as 
    'anythingllm.db' and other files in the 'storage' directory, leading to potential 
    application compromise, denial of service (DoS), and unauthorized admin account takeover.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately apply the patch provided in the GitHub commit: https://github.com/mintplex-labs/anything-llm/commit/e208074ef4c240fe03e4147ab097ec3b52b97619
    2. Review and enhance input validation for all file operations, especially those involving user-supplied data.
    3. Regularly audit and monitor file system access to detect and respond to suspicious activities promptly.
rule: version < "0" # As no specific version is provided, this rule assumes any version could be affected until a fix is applied.
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-5211
  - https://github.com/mintplex-labs/anything-llm/commit/e208074ef4c240fe03e4147ab097ec3b52b97619
  - https://huntr.com/bounties/38f282cb-7226-435e-9832-2d4a102dad4b