info:
  name: anythingllm
  cve: CVE-2024-0549
  summary: Relative path traversal vulnerability in anything-llm
  details: |
    mintplex-labs/anything-llm is vulnerable to a relative path traversal attack, allowing unauthorized attackers with a default role account to delete files and folders within the filesystem, including critical database files such as 'anythingllm.db'. The vulnerability stems from insufficient input validation and normalization in the handling of file and folder deletion requests. Successful exploitation results in the compromise of data integrity and availability.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of anything-llm that addresses this vulnerability.
    2. Implement strict input validation and normalization for file and folder deletion requests.
    3. Regularly audit and monitor file system access to detect and respond to unauthorized changes.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0549
  - https://github.com/mintplex-labs/anything-llm/commit/026849df0224b6a8754f4103530bc015874def62
  - https://huntr.com/bounties/fcb4001e-0290-4b78-a2f0-91ee5d20cc72