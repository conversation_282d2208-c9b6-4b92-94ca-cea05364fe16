info:
  name: anythingllm
  cve: CVE-2024-0455
  summary: Insecure web scraper inclusion in AnythingLLM allows unauthorized access to EC2 instance credentials.
  details: |
    The inclusion of the web scraper for AnythingLLM means that any user with the proper authorization level 
    (manager, admin, and when in single user) could input a special URL that resolves only when the request 
    comes from within an EC2 instance. This would allow the user to see the connection/secret credentials 
    for their specific instance and be able to manage it regardless of who deployed it. The user would have 
    to have pre-existing knowledge of the hosting infra which the target instance is deployed on, but if sent, 
    would resolve if on EC2 and the proper `iptables` or firewall rule is not configured for their setup.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately remove or secure the web scraper functionality in AnythingLLM.
    2. Ensure that proper `iptables` or firewall rules are in place to prevent unauthorized access.
    3. Review and restrict user permissions to limit who can input URLs into the system.
    4. Stay updated with the latest security patches and updates from the AnythingLLM developers.
rule: version > "0" && version < "1.4.0" # Assuming the vulnerability is in version 1.4.0 and below based on the provided data
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0455
  - https://github.com/mintplex-labs/anything-llm/commit/b2b2c2afe15c48952d57b4d01e7108f9515c5f55
  - https://huntr.com/bounties/07d83b49-7ebb-40d2-83fc-78381e3c5c9c