info:
  name: anythingllm
  cve: CVE-2024-13060
  summary: Vulnerability in AnythingLLM Docker allows unauthorized profile picture access.
  details: |
    A vulnerability in AnythingLLM Docker version 1.3.1 allows users with 'Default' permission to access other users' profile pictures by changing the 'id' parameter in the user cookie. This issue is present in versions prior to 1.3.1.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to AnythingLLM Docker version 1.3.1 or later.
    2. Implement strict validation for user cookies to prevent unauthorized access.
rule: version < "1.3.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-13060
  - https://github.com/mintplex-labs/anything-llm/commit/696af19c45473172ad4d3ca749281800a4d1a45a
  - https://huntr.com/bounties/98a49c90-e095-441f-900c-59d463dc8e8f