info:
  name: anythingllm
  cve: CVE-2024-3570
  summary: Stored Cross-Site Scripting (XSS) vulnerability in AnythingLLM chat functionality
  details: |
    A stored XSS vulnerability exists in the chat functionality of the mintplex-labs/anything-llm repository. 
    Attackers can execute arbitrary JavaScript by manipulating ChatBot responses, potentially leading to 
    unauthorized actions such as creating a new admin account or changing user passwords, resulting in 
    complete application takeover. The vulnerability arises from improper sanitization of user and 
    ChatBot input, particularly through the use of `dangerouslySetInnerHTML`. Exploitation requires 
    convincing an admin to add a malicious LocalAI ChatBot.
  cvss: CVSS:3.0/AV:L/AC:H/PR:H/UI:R/S:U/C:N/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Immediately update to the latest version of AnythingLLM.
    2. Review and sanitize all user and ChatBot inputs to prevent XSS attacks.
    3. Avoid using `dangerouslySetInnerHTML` and use safer alternatives for rendering dynamic content.
    4. Implement strict validation for ChatBot additions to the system.
rule: version < "0" # No specific version mentioned, assuming any version could be affected
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3570
  - https://github.com/mintplex-labs/anything-llm/commit/a4ace56a401ffc8ce0082d7444159dfd5dc28834
  - https://huntr.com/bounties/f0eaf552-aaf3-42b6-a5df-cfecd2de15ee