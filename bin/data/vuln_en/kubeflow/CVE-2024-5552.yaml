info:
  name: kubeflow
  cve: CVE-2024-5552
  summary: Kubeflow vulnerable to Regular Expression Denial of Service (ReDoS) due to inefficient email validation regex.
  details: |
    Kubeflow/kubeflow is vulnerable to a Regular Expression Denial of Service (ReDoS) attack due to inefficient regular expression complexity in its email validation mechanism. An attacker can remotely exploit this vulnerability without authentication by providing specially crafted input that causes the application to consume an excessive amount of CPU resources. This vulnerability affects the latest version of kubeflow/kubeflow, specifically within the centraldashboard-angular backend component. The impact of exploiting this vulnerability includes resource exhaustion, and service disruption.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Review and optimize the regular expression used in the email validation mechanism to prevent ReDoS attacks.
    2. Implement input validation to reject or sanitize inputs that could trigger excessive CPU usage.
    3. Monitor CPU usage and set up alerts for unusual spikes that could indicate an attack.
rule: version > "0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-5552
  - https://huntr.com/bounties/0c1d6432-f385-4c54-beea-9f8c677def5b