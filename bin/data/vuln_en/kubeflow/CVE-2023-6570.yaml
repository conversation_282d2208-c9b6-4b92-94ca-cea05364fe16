info:
  name: kubeflow
  cve: CVE-2023-6570
  summary: Server-Side Request Forgery (SSRF) vulnerability in Kubeflow
  details: |
    Kubeflow is affected by a Server-Side Request Forgery (SSRF) vulnerability. This allows attackers to send crafted requests to internal services or systems that are not intended to be accessed from the internet, potentially leading to unauthorized data access or further exploitation.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Review and update all external service calls to ensure they are restricted to trusted sources.
    2. Implement strict input validation for URLs and IP addresses to prevent SSRF attacks.
    3. Regularly update Kubeflow to the latest version to benefit from security patches and improvements.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6570
  - https://huntr.com/bounties/82d6e853-013b-4029-a23f-8b50ec56602a