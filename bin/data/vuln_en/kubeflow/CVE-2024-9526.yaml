info:
  name: kubeflow
  cve: CVE-2024-9526
  summary: Stored XSS Vulnerability in Kubeflow Pipeline View web UI
  details: |
    There exists a stored XSS Vulnerability in Kubeflow Pipeline View web UI. 
    The Kubeflow Web UI allows users to create new pipelines and add descriptions, 
    which permits HTML tags that are not properly filtered, leading to stored XSS.
  cvss: CVSS:4.0/AV:A/AC:L/AT:P/PR:H/UI:P/VC:H/VI:H/VA:L/SC:H/SI:H/SA:L/E:X/CR:X/IR:X/AR:X/MAV:X/MAC:X/MAT:X/MPR:X/MUI:X/MVC:X/MVI:X/MVA:X/MSC:X/MSI:X/MSA:X/S:P/AU:Y/R:U/V:D/RE:L/U:Green
  severity: HIGH
  security_advise: |
    1. Upgrade Kubeflow to a version past commit 930c35f1c543998e60e8d648ce93185c9b5dbe8d
    2. Implement input validation to filter HTML tags in the pipeline description field
    3. Regularly review and patch any newly discovered vulnerabilities in Kubeflow
rule: version < "930c35f1c543998e60e8d648ce93185c9b5dbe8d"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9526
  - https://github.com/kubeflow/pipelines/pull/10315