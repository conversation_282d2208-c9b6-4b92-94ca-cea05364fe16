info:
  name: kubeflow
  cve: CVE-2023-6571
  summary: Cross-site Scripting (XSS) - Reflected in kubeflow/kubeflow
  details: |
    The vulnerability is a reflected Cross-site Scripting (XSS) issue in the kubeflow/kubeflow project. 
    Attackers can exploit this by injecting malicious scripts into web pages viewed by other users, 
    potentially leading to session hijacking, data theft, or other malicious activities.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Review and update the codebase to properly sanitize all user inputs.
    2. Implement Content Security Policy (CSP) headers to mitigate XSS attacks.
    3. Regularly update dependencies to the latest versions that include security patches.
rule: version > "0" && version < "1.0.0" # Placeholder rule, as specific version affected is not provided
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6571
  - https://huntr.com/bounties/f02781e7-2a53-4c66-aa32-babb16434632