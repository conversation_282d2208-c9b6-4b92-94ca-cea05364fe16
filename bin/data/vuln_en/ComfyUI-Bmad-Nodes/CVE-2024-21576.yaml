info:
  name: ComfyUI-Bmad-Nodes
  cve: CVE-2024-21576
  summary: Code Injection vulnerability in ComfyUI-Bmad-Nodes due to validation bypass.
  details: |
    ComfyUI-Bmad-Nodes is vulnerable to Code Injection. The issue arises from a validation bypass 
    in the BuildColorRangeHSVAdvanced, FilterContour, and FindContour custom nodes. An attacker 
    can exploit this by generating a workflow that injects a crafted string into the node, 
    leading to the execution of arbitrary code on the server.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately update to the latest version of ComfyUI-Bmad-Nodes.
    2. Review and patch the BuildColorRangeHSVAdvanced, FilterContour, and FindContour nodes 
       to eliminate the use of eval and implement proper input validation.
    3. Conduct a security audit of all custom nodes to ensure no similar vulnerabilities exist.
rule: version < "3.9.0" # Assuming 3.9.0 is the version where this vulnerability is fixed, adjust as per actual fix version
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-21576
  - https://github.com/bmad4ever/comfyui_bmad_nodes/blob/392af9490cbadf32a1fe92ff820ebabe88c51ee8/cv_nodes.py#L1814