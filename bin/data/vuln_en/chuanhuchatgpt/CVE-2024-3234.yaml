info:
  name: chuanhuchatgpt
  cve: CVE-2024-3234
  summary: Path traversal vulnerability in chuanhuchatgpt due to outdated gradio component
  details: |
    The gaizhenbiao/chuanhuchatgpt application is vulnerable to a path traversal attack 
    due to its use of an outdated gradio component. The application is designed to restrict 
    user access to resources within the `web_assets` folder. However, the outdated version 
    of gradio it employs is susceptible to path traversal, as identified in CVE-2023-51449. 
    This vulnerability allows unauthorized users to bypass the intended restrictions and 
    access sensitive files, such as `config.json`, which contains API keys.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to the latest version of chuanhuchatgpt released on or after 20240305.
    2. Ensure all dependencies, including gradio, are up to date.
    3. Implement additional security measures to prevent path traversal attacks.
rule: version < "20240305"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3234
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/6b8f7db347b390f6f8bd07ea2a4ef01a47382f00
  - https://huntr.com/bounties/277e3ff0-5878-4809-a4b9-73cdbb70dc9f