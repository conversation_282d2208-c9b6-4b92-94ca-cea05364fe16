info:
  name: triton-inference-server
  cve: CVE-2024-0088
  summary: Vulnerability in NVIDIA Triton Inference Server's shared memory APIs can lead to denial of service and data tampering.
  details: |
    NVIDIA Triton Inference Server for Linux contains a vulnerability in shared memory APIs, 
    where a user can cause an improper memory access issue by a network API. 
    A successful exploit of this vulnerability might lead to denial of service and data tampering.
  cvss: CVSS:3.1/AV:N/AC:H/PR:H/UI:N/S:U/C:L/I:L/A:H
  severity: MEDIUM
  security_advise: |
    1. Review and update the server configuration to restrict network API access to shared memory.
    2. Monitor for unusual activity that may indicate an attempt to exploit this vulnerability.
    3. Stay updated with the latest security patches released by NVIDIA for Triton Inference Server.
rule: version > "0" && version < "unknown" # Version information not provided in the input data
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0088
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5535