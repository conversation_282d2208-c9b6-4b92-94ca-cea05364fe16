info:
  name: triton-inference-server
  cve: CVE-2024-0100
  summary: Vulnerability in NVIDIA Triton Inference Server tracing API leading to system file corruption.
  details: |
    NVIDIA Triton Inference Server for Linux contains a vulnerability in the tracing API, where a user can corrupt system files. A successful exploit of this vulnerability might lead to denial of service and data tampering.
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:H/A:H
  severity: MEDIUM
  security_advise: |
    1. Apply the latest security patches provided by NVIDIA for Triton Inference Server.
    2. Monitor system logs for any unusual activity that might indicate an attempt to exploit this vulnerability.
    3. Consider implementing additional security measures such as file integrity monitoring to detect and respond to potential file corruption.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0100
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5535