info:
  name: triton-inference-server
  cve: CVE-2024-0116
  summary: Out-of-bounds read issue in NVIDIA Triton Inference Server
  details: |
    NVIDIA Triton Inference Server contains a vulnerability where a user may cause an out-of-bounds read issue by releasing a shared memory region while it is in use.
    A successful exploit of this vulnerability may lead to denial of service.
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Monitor for any unusual activity related to shared memory usage.
    2. Apply the latest security patches released by NVIDIA for Triton Inference Server.
    3. Consider implementing additional safeguards around shared memory management.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0116
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5565