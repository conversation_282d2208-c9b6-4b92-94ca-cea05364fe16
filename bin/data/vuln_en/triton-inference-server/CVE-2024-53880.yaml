info:
  name: triton-inference-server
  cve: CVE-2024-53880
  summary: Integer overflow in model loading API of NVIDIA Triton Inference Server
  details: |
    The vulnerability in the model loading API allows a user to cause an integer overflow or wraparound error by loading a model with an extra-large file size, potentially leading to denial of service.
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Monitor and restrict the size of models being loaded to prevent overflow.
    2. Apply the latest security patches and updates provided by NVIDIA.
    3. Review and update security configurations to limit exposure to potential attacks.
rule: version > "0" && version < "unknown" # Version information not provided in input data
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-53880
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5612