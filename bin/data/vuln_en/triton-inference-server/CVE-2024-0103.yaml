info:
  name: triton-inference-server
  cve: CVE-2024-0103
  summary: NVIDIA Triton Inference Server may disclose information due to improper resource initialization.
  details: |
    The vulnerability in NVIDIA Triton Inference Server for Linux allows a user to cause incorrect initialization of resources due to network issues.
    A successful exploit may lead to information disclosure.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Monitor network traffic for unusual activity that may indicate an attempt to exploit this vulnerability.
    2. Review and update server configurations to ensure secure network practices.
    3. Stay updated with NVIDIA's security advisories and apply patches as soon as they are available.
rule: version > "0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0103
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5546