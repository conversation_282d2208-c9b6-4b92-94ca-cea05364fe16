info:
  name: triton-inference-server
  cve: CVE-2024-0095
  summary: Vulnerability in NVIDIA Triton Inference Server allows log injection and command execution
  details: |
    NVIDIA Triton Inference Server for Linux and Windows contains a vulnerability where a user can inject forged logs and executable commands by injecting arbitrary data as a new log entry. A successful exploit of this vulnerability might lead to code execution, denial of service, escalation of privileges, information disclosure, and data tampering.
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:C/C:H/I:L/A:H
  severity: CRITICAL
  security_advise: |
    1. Apply the latest security patches provided by NVIDIA for Triton Inference Server.
    2. Review and update server configurations to restrict log entry capabilities to authorized users only.
    3. Implement monitoring and alerting for suspicious log activities.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0095
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5546