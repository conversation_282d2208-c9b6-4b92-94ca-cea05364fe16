info:
  name: triton-inference-server
  cve: CVE-2023-31036
  summary: Relative path traversal vulnerability in NVIDIA Triton Inference Server
  details: |
    NVIDIA Triton Inference Server for Linux and Windows contains a vulnerability where, 
    when launched with the non-default command line option `--model-control explicit`, 
    an attacker may use the model load API to cause a relative path traversal. A successful 
    exploit of this vulnerability may lead to code execution, denial of service, escalation 
    of privileges, information disclosure, and data tampering.
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Avoid using the `--model-control explicit` command line option.
    2. Regularly update to the latest version of Triton Inference Server to benefit from security patches.
    3. Monitor and restrict access to the model load API to authorized users only.
rule: version < "unknown" # Version information not provided in the input data
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-31036
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5509