info:
  name: triton-inference-server
  cve: CVE-2024-0087
  summary: Vulnerability in NVIDIA Triton Inference Server allows arbitrary file logging.
  details: |
    NVIDIA Triton Inference Server for Linux contains a vulnerability where a user can set the logging location to an arbitrary file. If this file exists, logs are appended to the file. A successful exploit of this vulnerability might lead to code execution, denial of service, escalation of privileges, information disclosure, and data tampering.
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:C/C:H/I:L/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately apply the latest security patches provided by NVIDIA.
    2. Review and restrict logging configurations to prevent arbitrary file writes.
    3. Monitor for any suspicious activity related to logging and file system changes.
rule: version < "24.05.14" # Assuming the vulnerability is fixed in a version released on or after May 14, 2024
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0087
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5535