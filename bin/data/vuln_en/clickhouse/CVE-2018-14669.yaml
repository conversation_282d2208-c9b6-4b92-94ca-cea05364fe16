info:
  name: clickhouse
  cve: CVE-2018-14669
  summary: ClickHouse MySQL client allows remote file read via "LOAD DATA LOCAL INFILE"
  details: |
    ClickHouse MySQL client before versions 1.1.54390 had "LOAD DATA LOCAL INFILE" functionality enabled
    that allowed a malicious MySQL database to read arbitrary files from the connected ClickHouse server.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to ClickHouse MySQL client version 1.1.54390 or later.
    2. Disable "LOAD DATA LOCAL INFILE" functionality if not required.
rule: version < "1.1.54390"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2018-14669
  - https://clickhouse.yandex/docs/en/security_changelog