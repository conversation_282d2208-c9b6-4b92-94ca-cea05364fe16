info:
  name: clickhouse
  cve: CVE-2019-15024
  summary: Vulnerability in ClickHouse versions before 19.14.3 allows arbitrary file write via malicious replica registration.
  details: |
    In all versions of ClickHouse before 19.14.3, an attacker having write access to ZooKeeper and who is able to run a custom server available from the network where <PERSON><PERSON><PERSON><PERSON> runs, can create a custom-built malicious server that will act as a ClickHouse replica and register it in ZooKeeper. When another replica will fetch data part from the malicious replica, it can force clickhouse-server to write to arbitrary path on filesystem.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to ClickHouse version 19.14.3 or later.
    2. Ensure that only trusted servers are allowed to register as replicas in ZooKeeper.
    3. Implement strict access controls on ZooKeeper to prevent unauthorized write access.
rule: version < "19.14.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2019-15024
  - https://clickhouse.yandex/docs/en/security_changelog