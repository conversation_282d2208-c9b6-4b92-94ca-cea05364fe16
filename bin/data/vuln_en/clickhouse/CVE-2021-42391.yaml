info:
  name: clickhouse
  cve: CVE-2021-42391
  summary: Divide-by-zero vulnerability in Clickhouse's Gorilla compression codec
  details: |
    The vulnerability arises from a divide-by-zero error in Clickhouse's Gorilla compression codec when parsing a malicious query. 
    The first byte of the compressed buffer is used in a modulo operation without being checked for zero, potentially leading to a crash or arbitrary code execution.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of Clickhouse to mitigate this vulnerability.
    2. Review and validate all incoming queries to ensure they do not contain malicious input.
    3. Implement additional input validation and sanitization measures to prevent similar vulnerabilities.
rule: version < "21.3.4.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-42391
  - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms