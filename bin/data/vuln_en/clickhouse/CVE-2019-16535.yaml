info:
  name: clickhouse
  cve: CVE-2019-16535
  summary: Remote Code Execution and Denial of Service vulnerabilities in ClickHouse
  details: |
    In all versions of ClickHouse before 19.14, an out-of-bounds (OOB) read, OOB write,
    and integer underflow in decompression algorithms can be exploited to achieve remote
    code execution (RCE) or denial of service (DoS) via the native protocol.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ClickHouse version 19.14 or later.
    2. Review and update security configurations to prevent unauthorized access.
    3. Monitor for unusual activity that may indicate exploitation attempts.
rule: version < "19.14.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2019-16535
  - https://clickhouse.yandex/docs/en/security_changelog