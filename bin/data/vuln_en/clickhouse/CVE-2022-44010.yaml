info:
  name: clickhouse
  cve: CVE-2022-44010
  summary: Heap-based buffer overflow in ClickHouse HTTP Endpoint
  details: |
    An issue was discovered in ClickHouse before 22.9.1.2603. An attacker could send a crafted HTTP request to the HTTP Endpoint (usually listening on port 8123 by default), causing a heap-based buffer overflow that crashes the process. This does not require authentication.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ClickHouse version 22.9.1.2603 or later.
    2. Alternatively, upgrade to one of the following fixed versions: 22.8.2.11, 22.7.4.16, 22.6.6.16, or 22.3.12.19.
rule: version < "22.9.1.2603"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2022-44010
  - https://clickhouse.com/docs/en/whats-new/security-changelog