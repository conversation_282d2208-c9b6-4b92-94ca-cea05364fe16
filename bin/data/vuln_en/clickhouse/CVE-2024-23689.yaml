info:
  name: clickhouse
  cve: CVE-2024-23689
  summary: Exposure of sensitive information in ClickHouse
  details: |
    Exposure of sensitive information in exceptions in ClickHouse's clickhouse-r2dbc, com.clickhouse:clickhouse-jdbc, and com.clickhouse:clickhouse-client versions less than 0.4.6 allows unauthorized users to gain access to client certificate passwords via client exception logs. This occurs when 'sslkey' is specified and an exception, such as a ClickHouseException or SQLException, is thrown during database operations; the certificate password is then included in the logged exception message.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to com.clickhouse:clickhouse-r2dbc, com.clickhouse:clickhouse-jdbc, and com.clickhouse:clickhouse-client versions 0.4.6 or higher.
    2. Review and modify exception handling to avoid logging sensitive information.
    3. Implement additional security measures to protect client certificate passwords.
rule: version < "0.4.6"
references:
  - https://github.com/ClickHouse/clickhouse-java/security/advisories/GHSA-g8ph-74m6-8m7r
  - https://nvd.nist.gov/vuln/detail/CVE-2024-23689
  - https://github.com/ClickHouse/clickhouse-java/issues/1331
  - https://github.com/ClickHouse/clickhouse-java/pull/1334
  - https://github.com/ClickHouse/clickhouse-java
  - https://github.com/ClickHouse/clickhouse-java/releases/tag/v0.4.6
  - https://github.com/advisories/GHSA-g8ph-74m6-8m7r
  - https://vulncheck.com/advisories/vc-advisory-GHSA-g8ph-74m6-8m7r