info:
  name: clickhouse
  cve: CVE-2021-4104
  summary: Using JMSAppender in log4j configuration may lead to deserialization of untrusted data
  details: |
    ClickHouse JDBC Bridge uses slf4j-log4j12 1.7.32, which depends on log4j 1.2.17. It allows a remote attacker to execute code on the server if you changed default log4j configuration by adding JMSAppender and an insecure JMS broker.
    
    The patch version `2.0.7` removed log4j dependency by replacing slf4j-log4j12 to slf4j-jdk14. Logging configuration is also changed from log4j.properties to logging.properties.
    
    Workarounds:
    1. Do NOT change log4j configuration to use JMSAppender along with insecure JMS broker
    2. Alternatively, you can issue below command to remove `JMSAppender.class`:
    
    ```bash
    # install zip command if you don't have
    apt-get update && apt-get install -y zip
    # remove the class
    zip -d clickhouse-jdbc-bridge*.jar ru/yandex/clickhouse/jdbcbridge/internal/log4j/net/JMSAppender.class
    ```
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to clickhouse-jdbc-bridge version 2.0.7 or higher
    2. Avoid using JMSAppender in log4j configuration with insecure JMS brokers
    3. If JMSAppender must be used, ensure the JMS broker is secure and properly configured
rule: version < "2.0.7"
references:
  - https://github.com/ClickHouse/clickhouse-jdbc-bridge/security/advisories/GHSA-3w6p-8f82-gw8r
  - https://nvd.nist.gov/vuln/detail/CVE-2021-4104
  - https://access.redhat.com/security/cve/CVE-2021-4104
  - https://github.com/ClickHouse/clickhouse-jdbc-bridge