info:
  name: clickhouse
  cve: CVE-2021-42390
  summary: Divide-by-zero vulnerability in Clickhouse's DeltaDouble compression codec
  details: |
    The vulnerability arises from a divide-by-zero error in Clickhouse's DeltaDouble compression codec when parsing a malicious query. The first byte of the compressed buffer is used in a modulo operation without being checked for zero, potentially leading to a crash or undefined behavior.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest stable version of Clickhouse.
    2. Review and patch any custom implementations that use the DeltaDouble compression codec.
    3. Monitor for any unusual activity that may indicate exploitation attempts.
rule: version < "21.3.4.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-42390
  - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms