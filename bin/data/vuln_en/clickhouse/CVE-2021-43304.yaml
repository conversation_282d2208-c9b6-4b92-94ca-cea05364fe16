info:
  name: clickhouse
  cve: CVE-2021-43304
  summary: Heap buffer overflow in Clickhouse's LZ4 compression codec when parsing a malicious query.
  details: |
    The vulnerability arises from improper handling of copy operations in the LZ4::decompressImpl loop,
    particularly the arbitrary copy operation wildCopy<copy_amount>(op, ip, copy_end), which can exceed
    the destination buffer’s limits when parsing a malicious query.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of Clickhouse where this vulnerability is fixed.
    2. Implement input validation to ensure queries do not contain malicious content.
    3. Monitor for any unusual activity that could indicate exploitation of this vulnerability.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-43304
  - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms
  - https://lists.debian.org/debian-lts-announce/2022/11/msg00002.html