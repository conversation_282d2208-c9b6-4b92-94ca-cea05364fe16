info:
  name: clickhouse
  cve: CVE-2018-14672
  summary: Path traversal vulnerability in CatBoost model loading functions in ClickHouse
  details: |
    In ClickHouse before version 18.12.13, functions for loading CatBoost models allowed path traversal and reading arbitrary files through error messages.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to ClickHouse version 18.12.13 or later.
    2. Review and restrict file access permissions for CatBoost model loading functions.
    3. Implement additional input validation to prevent path traversal attempts.
rule: version < "18.12.13"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2018-14672
  - https://clickhouse.yandex/docs/en/security_changelog