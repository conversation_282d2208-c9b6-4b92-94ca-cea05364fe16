info:
  name: clickhouse
  cve: CVE-2021-43305
  summary: Heap buffer overflow in Clickhouse's LZ4 compression codec when parsing a malicious query.
  details: |
    The vulnerability arises from insufficient verification of copy operations within the LZ4::decompressImpl loop in Clickhouse's LZ4 compression codec. 
    This allows attackers to exploit the system by crafting malicious queries that can lead to heap buffer overflows, potentially resulting in arbitrary code execution or system compromise.
    This issue is similar to CVE-2021-43304 but affects a different wildCopy call.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest stable version of Clickhouse where this vulnerability has been fixed.
    2. Implement input validation to ensure that queries do not contain malicious data.
    3. Monitor Clickhouse logs for any signs of unusual activity that could indicate an attempted exploit.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-43305
  - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms
  - https://lists.debian.org/debian-lts-announce/2022/11/msg00002.html