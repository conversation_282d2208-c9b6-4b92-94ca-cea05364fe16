info:
  name: clickhouse
  cve: CVE-2018-14671
  summary: Remote Code Execution vulnerability in ClickHouse due to insecure unixODBC configuration
  details: |
    In ClickHouse before version 18.10.3, the unixODBC driver allowed loading arbitrary shared objects from the file system. This vulnerability could be exploited to execute arbitrary code remotely.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to ClickHouse version 18.10.3 or later.
    2. Review and restrict unixODBC configuration to prevent loading of unauthorized shared objects.
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2018-14671
  - https://clickhouse.yandex/docs/en/security_changelog
rule: version < "18.10.3"