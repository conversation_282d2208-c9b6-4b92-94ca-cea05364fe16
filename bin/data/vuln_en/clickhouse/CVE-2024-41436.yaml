info:
  name: clickhouse
  cve: CVE-2024-41436
  summary: ClickHouse v24.3.3.102 buffer overflow via DB::evaluateConstantExpressionImpl
  details: |
    ClickHouse v24.3.3.102 was discovered to contain a buffer overflow vulnerability in the component DB::evaluateConstantExpressionImpl.
    This vulnerability could potentially lead to arbitrary code execution if exploited.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ClickHouse version 24.3.3.103 or later, which includes the fix for this vulnerability.
    2. Monitor and review system logs for any suspicious activity that may indicate an attempt to exploit this vulnerability.
    3. Implement additional security measures such as network segmentation and intrusion detection systems to mitigate potential impacts.
rule: version < "24.3.3.103"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-41436
  - https://github.com/ClickHouse/ClickHouse/issues/65520
  - https://gist.github.com/ycybfhb/db127ae9d105a4d20edc9f010a959016