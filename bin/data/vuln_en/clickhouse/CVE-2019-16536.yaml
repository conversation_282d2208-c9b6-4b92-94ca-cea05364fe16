info:
  name: clickhouse
  cve: CVE-2019-16536
  summary: Stack overflow leading to DoS in Clickhouse versions before 19.14.3.3
  details: |
    A stack overflow vulnerability exists in Clickhouse versions prior to 19.14.3.3, which can be triggered by a malicious authenticated client, potentially leading to a denial of service (DoS).
  cvss: CVSS:4.0/AV:N/AC:L/AT:N/PR:H/UI:N/VC:N/VI:N/VA:H/SC:N/SI:N/SA:H/E:X/CR:X/IR:X/AR:X/MAV:X/MAC:X/MAT:X/MPR:X/MUI:X/MVC:X/MVI:X/MVA:X/MSC:X/MSI:X/MSA:X/S:X/AU:X/R:X/V:X/RE:X/U:X
  severity: HIGH
  security_advise: |
    1. Upgrade to Clickhouse version 19.14.3.3 or later.
    2. Monitor for any unusual activity that may indicate an attack.
    3. Implement additional security measures such as rate limiting and input validation.
rule: version < "19.14.3.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2019-16536
  - https://clickhouse.com/docs/whats-new/security-changelog