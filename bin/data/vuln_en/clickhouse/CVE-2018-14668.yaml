info:
  name: clickhouse
  cve: CVE-2018-14668
  summary: In ClickHouse before 1.1.54388, the "remote" table function allowed arbitrary symbols in "user", "password", and "default_database" fields, leading to Cross Protocol Request Forgery Attacks.
  details: |
    The vulnerability in ClickHouse versions prior to 1.1.54388 allowed attackers to exploit the "remote" table function by injecting arbitrary symbols into the "user", "password", and "default_database" fields. This could lead to unauthorized access and potentially allow attackers to perform actions on behalf of legitimate users.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ClickHouse version 1.1.54388 or later.
    2. Review and enforce strict input validation for user inputs in the "remote" table function.
    3. Monitor for any suspicious activities related to database access and authentication.
rule: version < "1.1.54388"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2018-14668
  - https://clickhouse.yandex/docs/en/security_changelog