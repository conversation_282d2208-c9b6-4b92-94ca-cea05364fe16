info:
  name: clickhouse
  cve: CVE-2021-25263
  summary: Clickhouse allows unauthorized file read access prior to specific versions
  details: |
    Clickhouse versions prior to v**********-lts, v*********-stable, v*********-stable, v*********-lts, and v*********-stable allow users to read any file on the host system that the Clickhouse user has access to.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to Clickhouse version v**********-lts, v*********-stable, v*********-stable, v*********-lts, or v*********-stable or later.
    2. Review and restrict file permissions for the Clickhouse user to minimize potential damage.
rule: version < "**********-lts" || (version < "*********-stable" && version < "*********-stable") || (version < "*********-lts" && version < "*********-stable")
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-25263
  - https://clickhouse.tech/docs/en/whats-new/security-changelog
  - https://yandex.com/bugbounty/i/hall-of-fame-browser