info:
  name: clickhouse
  cve: CVE-2021-42387
  summary: Heap out-of-bounds read in Clickhouse's LZ4 compression codec
  details: |
    The vulnerability arises from a heap out-of-bounds read in Clickhouse's LZ4 compression codec when parsing a malicious query. During the LZ4::decompressImpl() loop, a 16-bit unsigned user-supplied value ('offset') is read from the compressed data and used in the length of a copy operation without checking the upper bounds of the source of the copy operation.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of Clickhouse that addresses this vulnerability.
    2. Implement input validation to ensure queries do not contain malicious data.
    3. Monitor for any unusual activity that could indicate exploitation attempts.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-42387
  - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms
  - https://lists.debian.org/debian-lts-announce/2022/11/msg00002.html