info:
  name: clickhouse
  cve: CVE-2022-44011
  summary: Heap buffer overflow in ClickHouse leading to server crash
  details: |
    An authenticated user with the ability to load data could cause a heap buffer overflow
    and crash the ClickHouse server by inserting a malformed CapnProto object before version
    22.9.1.2603.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to ClickHouse version 22.9.1.2603 or later.
    2. Apply any available security patches for versions 22.8.2.11, 22.7.4.16, 22.6.6.16,
       and 22.3.12.19.
rule: version < "22.9.1.2603"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2022-44011
  - https://clickhouse.com