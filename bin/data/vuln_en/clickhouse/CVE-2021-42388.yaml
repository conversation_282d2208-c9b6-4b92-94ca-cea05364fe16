info:
  name: clickhouse
  cve: CVE-2021-42388
  summary: Heap out-of-bounds read in Clickhouse's LZ4 compression codec
  details: |
    The vulnerability arises from a heap out-of-bounds read in Clickhouse's LZ4 compression codec when parsing a malicious query. During the LZ4::decompressImpl() loop, a 16-bit unsigned user-supplied value ('offset') is read from the compressed data and used in the length of a copy operation without proper lower bound checking, potentially leading to arbitrary memory access.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest stable version of Clickhouse.
    2. Review and adjust query handling to prevent malicious input.
    3. Implement additional input validation for compressed data.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2021-42388
  - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms
  - https://lists.debian.org/debian-lts-announce/2022/11/msg00002.html