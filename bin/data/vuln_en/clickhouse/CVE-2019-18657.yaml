info:
  name: clickhouse
  cve: CVE-2019-18657
  summary: <PERSON><PERSON><PERSON><PERSON> before ********** allows HTTP header injection via the url table function.
  details: |
    The vulnerability allows attackers to inject malicious HTTP headers via the url table function in ClickHouse versions prior to **********. This could lead to various types of attacks, including information leakage and session manipulation.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade ClickHouse to version ********** or later.
    2. Review and restrict the use of the url table function to trusted inputs only.
    3. Monitor for any suspicious activity related to HTTP headers.
rule: version < "**********"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2019-18657
  - https://github.com/ClickHouse/ClickHouse/pull/6466
  - https://github.com/ClickHouse/ClickHouse/pull/7526/files
  - https://github.com/ClickHouse/ClickHouse/blob/master/CHANGELOG.md