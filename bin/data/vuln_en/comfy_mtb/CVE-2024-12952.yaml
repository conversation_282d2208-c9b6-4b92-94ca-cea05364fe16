info:
  name: comfy_mtb
  cve: CVE-2024-12952
  summary: Critical code injection vulnerability in melMass comfy_mtb up to 0.1.4
  details: |
    A critical vulnerability was discovered in the `run_command` function of the `comfy_mtb/endpoint.py` file, part of the Dependency Handler component. This vulnerability allows for remote code injection, potentially leading to system compromise.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: CRITICAL
  security_advise: |
    1. Immediately apply the patch named d6e004cce2c32f8e48b868e66b89f82da4887dc3.
    2. Upgrade to a version of comfy_mtb greater than 0.1.4 once it is available.
    3. Monitor for any further security advisories or updates from the maintainers.
rule: version <= "0.1.4"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12952
  - https://github.com/melMass/comfy_mtb/issues/224
  - https://github.com/melMass/comfy_mtb/commit/d6e004cce2c32f8e48b868e66b89f82da4887dc3
  - https://vuldb.com/?ctiid.289315