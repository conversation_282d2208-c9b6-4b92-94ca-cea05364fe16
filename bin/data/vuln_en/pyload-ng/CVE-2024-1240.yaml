info:
  name: pyload-ng
  cve: CVE-2024-1240
  summary: Open redirection vulnerability in pyload/pyload
  details: |
    An open redirection vulnerability exists in pyload/pyload version 0.5.0 due to improper handling of the 'next' parameter in the login functionality.
    An attacker can exploit this vulnerability to redirect users to malicious sites, which can be used for phishing or other malicious activities.
    The issue is fixed in pyload-ng 0.5.0b3.dev79.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev79
    2. Review and enforce strict validation of URL parameters to prevent open redirection
rule: version = "0.5.0" || (version < "0.5.0b3.dev79" && version > "0")
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1240
  - https://github.com/pyload/pyload/commit/fe94451dcc2be90b3889e2fd9d07b483c8a6dccd
  - https://huntr.com/bounties/eef9513d-ccc3-4030-b574-374c5e7b887e