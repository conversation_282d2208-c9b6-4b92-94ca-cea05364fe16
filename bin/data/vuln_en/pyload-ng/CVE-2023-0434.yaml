info:
  name: pyload-ng
  cve: CVE-2023-0434
  summary: Improper Input Validation in pyload-ng
  details: |
    The vulnerability arises from improper input validation in the GitHub repository pyload/pyload prior to version 0.5.0b3.dev40.
    This could allow attackers to exploit the system, potentially leading to unauthorized access and data manipulation.
  cvss: CVSS:3.0/AV:P/AC:L/PR:H/UI:R/S:U/C:N/I:H/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to pyload-ng version 0.5.0b3.dev40 or later.
    2. Implement strict input validation checks in the application.
    3. Regularly review and update dependencies to mitigate potential vulnerabilities.
rule: version < "0.5.0b3.dev40"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-0434
  - https://github.com/pyload/pyload/commit/a2b1eb1028f45ac58dea5f58593c1d3db2b4a104
  - https://github.com/pyload/pyload
  - https://huntr.dev/bounties/7d9332d8-6997-483b-9fb9-bcf2ae01dad4