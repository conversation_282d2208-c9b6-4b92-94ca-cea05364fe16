info:
  name: pyload-ng
  cve: CVE-2024-22416
  summary: Cross-Site Request Forgery on any API call in pyLoad may lead to admin privilege escalation
  details: |
    The `pyload` API allows any API call to be made using GET requests. Since the session cookie is not set to `SameSite: strict`, this opens the library up to severe attack possibilities via a Cross-Site Request Forgery (CSRF) attack. This proof of concept shows how an unauthenticated user could trick the administrator's browser into creating a new admin user.
    If we now trick an administrator into visiting a malicious page, their browser will make a request to add a new administrator to the `pyload` application. The attacker can then authenticate as this newly created administrator user.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev78
    2. Implement CSRF protection mechanisms for all API endpoints
    3. Ensure session cookies are set with `SameSite: strict` attribute
rule: version < "0.5.0b3.dev78"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-pgpj-v85q-h5fm
  - https://nvd.nist.gov/vuln/detail/CVE-2024-22416
  - https://github.com/pyload/pyload/commit/1374c824271cb7e927740664d06d2e577624ca3e
  - https://github.com/pyload/pyload/commit/c7cdc18ad9134a75222974b39e8b427c4af845fc
  - https://github.com/pyload/pyload
  - https://github.com/pypa/advisory-database/tree/main/vulns/pyload-ng/PYSEC-2024-17.yaml