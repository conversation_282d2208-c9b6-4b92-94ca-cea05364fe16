info:
  name: pyload-ng
  cve: CVE-2023-0055
  summary: Pyload contains Sensitive Cookie in HTTPS Session Without 'Secure' Attribute
  details: |
    Sensitive Cookie in HTTPS Session Without 'Secure' Attribute in GitHub repository pyload/pyload prior to 0.5.0b3.dev32.
    The Secure attribute for sensitive cookies in HTTPS sessions is not set, which could cause the user agent to send those cookies in plaintext over an HTTP session.
    This issue is patched in version 0.5.0b3.dev32.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev32
    2. Ensure that sensitive cookies are always set with the 'Secure' attribute in HTTPS sessions.
rule: version < "0.5.0b3.dev32"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-0055
  - https://github.com/pyload/pyload/commit/7b53b8d43c2c072b457dcd19c8a09bcfc3721703
  - https://github.com/pyload/pyload
  - https://huntr.dev/bounties/ed88e240-99ff-48a1-bf32-8e1ef5f13cce