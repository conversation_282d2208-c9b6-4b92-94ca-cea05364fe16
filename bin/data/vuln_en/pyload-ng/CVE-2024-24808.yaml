info:
  name: pyload-ng
  cve: CVE-2024-24808
  summary: Open redirect vulnerability due to improper validation of the is_safe_url function
  details: |
    ### Summary
    Open redirect vulnerability due to incorrect validation of input values when redirecting users after login.
    ### Details
    pyload is validating URLs via the `get_redirect_url` function when redirecting users at login.
    The URL entered in the `next` variable goes through the `is_safe_url` function, where a lack of validation can redirect the user to an arbitrary domain.
    The documentation in the urllib library shows that improper URLs are recognized as relative paths when using the `urlparse` function.
    For example, When an unusual URL like `https:///example.com` is entered, `urlparse` interprets it as a relative path, but in the actual request it is converted to `https://example.com` due to url normalization.
    ### PoC
    1. In the next variable, insert the URL to which you want to redirect the user.
    2. Check that it is possible to bypass url validation and redirect users to an arbitrary url.
    ### Impact
    An attacker can use this vulnerability to redirect users to malicious websites, which can be used for phishing and similar attacks.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev79
    2. Implement strict validation for URLs in the `is_safe_url` function
    3. Regularly review and update URL validation logic to prevent similar vulnerabilities
rule: version < "0.5.0b3.dev79"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-g3cm-qg2v-2hj5
  - https://nvd.nist.gov/vuln/detail/CVE-2024-24808
  - https://github.com/pyload/pyload/commit/fe94451dcc2be90b3889e2fd9d07b483c8a6dccd
  - https://github.com/pyload/pyload