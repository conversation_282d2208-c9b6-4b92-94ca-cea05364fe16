info:
  name: pyload-ng
  cve: CVE-2024-32880
  summary: pyLoad allows upload to arbitrary folder leading to RCE
  details: |
    An authenticated user can change the download folder and upload a crafted template to the specified folder, leading to remote code execution. The vulnerability affects all versions, with earlier versions having different trigger conditions.
    
    Example version: 0.5
    File: src/pyload/webui/app/blueprints/app_blueprint.py
    
    ```python
    @bp.route("/render/<path:filename>", endpoint="render")
    def render(filename):
        mimetype = mimetypes.guess_type(filename)[0] or "text/html"
        data = render_template(filename)
        return flask.Response(data, mimetype=mimetype)
    ```
    
    Impact:
    It is a RCE vulnerability and affects all versions. In earlier version 0.4.20, the trigger difference is the pyload installation folder path difference and the upload file must have the extension ".js".
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.1
    2. Implement strict file upload validation to prevent uploading of malicious templates
    3. Restrict access to the /settings page to authorized personnel only
rule: version < "0.5.1"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-3f7w-p8vr-4v5f
  - https://nvd.nist.gov/vuln/detail/CVE-2024-32880
  - https://github.com/pyload/pyload