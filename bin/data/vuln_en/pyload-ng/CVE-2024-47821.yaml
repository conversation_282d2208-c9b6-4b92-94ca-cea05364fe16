info:
  name: pyload-ng
  cve: CVE-2024-47821
  summary: py<PERSON>oad vulnerable to remote code execution by download to /.pyload/scripts using /flashgot API
  details: |
    The folder `/.pyload/scripts` has scripts which are run when certain actions are completed, for e.g. a download is finished. By downloading an executable file to a folder in /scripts and performing the respective action, remote code execution can be achieved. A file can be downloaded to such a folder by changing the download folder to a folder in `/scripts` path and using the `/flashgot` API to download the file.
    The `flashgot` API provides functionality to download files from a provided URL. Although pyload tries to prevent non-local requests from being able to reach this API, it relies on checking the Host header and the Referer header of the incoming request. Both of these can be set by an attacker to arbitrary values, thereby bypassing these checks.
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev87
    2. Ensure that the download folder is not set to a directory within `/.pyload/scripts`
    3. Implement stricter checks for the Host and Referer headers in the `/flashgot` API
rule: version < "0.5.0b3.dev87"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-w7hq-f2pj-c53g
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47821
  - https://github.com/pyload/pyload/commit/48f59567393a19263c8a0285256a7537dc9ce109
  - https://github.com/pyload/pyload