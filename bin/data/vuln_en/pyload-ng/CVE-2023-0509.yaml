info:
  name: pyload-ng
  cve: CVE-2023-0509
  summary: Improper Certificate Validation in pyload-ng
  details: |
    The vulnerability arises from improper certificate validation in the GitHub repository pyload/pyload prior to version 0.5.0b3.dev44.
    This issue could allow attackers to intercept or manipulate encrypted communications.
  cvss: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev44
    2. Review and update certificate validation mechanisms
    3. Monitor for any suspicious activity post-upgrade
rule: version < "0.5.0b3.dev44"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-0509
  - https://github.com/pyload/pyload/commit/a9098bdf7406e6faf9df3da6ff2d584e90c13bbb
  - https://github.com/pyload/pyload
  - https://huntr.dev/bounties/a370e0c2-a41c-4871-ad91-bc6f31a8e839