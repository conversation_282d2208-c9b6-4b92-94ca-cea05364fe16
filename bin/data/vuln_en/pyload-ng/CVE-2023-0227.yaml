info:
  name: pyload-ng
  cve: CVE-2023-0227
  summary: Pyload Insufficient Session Expiration vulnerability
  details: |
    Pyload 0.5.0b3.dev35 has an Insufficient Session Expiration vulnerability. A patch is available and anticipated to be part of version 0.5.0b3.dev36.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev36
    2. Monitor for any further updates or patches released for pyload-ng
rule: version <= "0.5.0b3.dev35"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-0227
  - https://github.com/pyload/pyload/commit/c035714c0596b704b11af0f8a669352f128ad2d9
  - https://github.com/pyload/pyload
  - https://huntr.dev/bounties/af3101d7-fea6-463a-b7e4-a48be219e31b