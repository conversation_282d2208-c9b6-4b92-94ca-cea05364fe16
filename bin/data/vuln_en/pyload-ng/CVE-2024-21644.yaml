info:
  name: pyload-ng
  cve: CVE-2024-21644
  summary: pyload Unauthenticated Flask Configuration Leakage vulnerability
  details: |
    Any unauthenticated user can browse to a specific URL to expose the Flask config, including the `SECRET_KEY` variable.
    ### Details
    Any unauthenticated user can browse to a specific URL to expose the Flask config, including the `SECRET_KEY` variable.
    ### PoC
    Run `pyload` in the default configuration by running the following command
    ```
    pyload
    ```
    Now browse to `http://localhost:8000/render/info.html`. Notice how the Flask configuration gets displayed.
    ![PoC](https://user-images.githubusercontent.com/44903767/294522246-4cc19c49-b315-4926-8fd6-ec3c3fdb7c1f.png)
    The vulnerability arises due to the ability to load predefined templates without context, exposing the Flask configuration.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev77
    2. Review and restrict access to Flask configuration endpoints
    3. Ensure that sensitive information is not exposed in templates
rule: version < "0.5.0b3.dev77"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-mqpq-2p68-46fv
  - https://nvd.nist.gov/vuln/detail/CVE-2024-21644
  - https://github.com/pyload/pyload/commit/bb22063a875ffeca357aaf6e2edcd09705688c40
  - https://github.com/pyload/pyload