info:
  name: pyload-ng
  cve: CVE-2023-0435
  summary: Excessive Attack Surface in pyload-ng
  details: |
    The vulnerability arises from an excessive attack surface in the GitHub repository pyload/pyload prior to version 0.5.0b3.dev41.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev41
    2. Review and reduce the attack surface of the application
    3. Monitor for any suspicious activity post-upgrade
rule: version < "0.5.0b3.dev41"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-0435
  - https://github.com/pyload/pyload/commit/431ea6f0371d748df66b344a05ca1a8e0310cff3
  - https://github.com/pyload/pyload
  - https://huntr.dev/bounties/a3e32ad5-caee-4f43-b10a-4a876d4e3f1d