info:
  name: pyload-ng
  cve: CVE-2024-21645
  summary: pyload Log Injection vulnerability
  details: |
    A log injection vulnerability was identified in `pyload`. This vulnerability allows any unauthenticated actor to inject arbitrary messages into the logs gathered by `pyload`. When supplied with a username containing a newline, this newline is not properly escaped, allowing the attacker to inject new log entries into the log file.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev77
    2. Implement input validation to escape or reject newline characters in usernames
    3. Regularly monitor logs for any signs of tampering or injection attempts
rule: version < "0.5.0b3.dev77"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-ghmw-rwh8-6qmr
  - https://nvd.nist.gov/vuln/detail/CVE-2024-21645
  - https://github.com/pyload/pyload/commit/4159a1191ec4fe6d927e57a9c4bb8f54e16c381d
  - https://github.com/pyload/pyload