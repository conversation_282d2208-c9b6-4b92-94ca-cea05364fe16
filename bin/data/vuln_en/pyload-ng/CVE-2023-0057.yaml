info:
  name: pyload-ng
  cve: CVE-2023-0057
  summary: pyLoad vulnerable to Improper Restriction of Rendered UI Layers or Frames
  details: |
    Improper Restriction of Rendered UI Layers or Frames in GitHub repository pyload/pyload prior to 0.5.0b3.dev33.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev33
    2. Review and update UI rendering logic to enforce proper restrictions
    3. Monitor for any further updates or patches related to this vulnerability
rule: version < "0.5.0b3.dev33"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-0057
  - https://github.com/pyload/pyload/commit/bd2a31b7de54570b919aa1581d486e6ee18c0f64
  - https://github.com/pyload/pyload
  - https://huntr.dev/bounties/12b64f91-d048-490c-94b0-37514b6d694d