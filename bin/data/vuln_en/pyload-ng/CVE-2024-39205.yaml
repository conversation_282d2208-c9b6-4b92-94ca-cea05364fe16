info:
  name: pyload-ng
  cve: CVE-2024-39205
  summary: pyload-ng vulnerable to RCE with js2py sandbox escape
  details: |
    Any pyload-ng running under python3.11 or below are vulnerable to Remote Code Execution (RCE). 
    Attackers can send a request containing any shell command and the victim server will execute it immediately.
    The vulnerability stems from a sandbox escape in js2py, which is used by the `/flash/addcrypted2` API endpoint of pyload-ng.
    Although this endpoint is designed to only accept localhost connections, it can be bypassed using HTTP headers.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to pyload-ng >=0.5.0b3.dev86 or later.
    2. If upgrading is not immediately feasible, consider running pyload-ng under Python 3.12 or above, as js2py is not used in these versions.
    3. Implement network-level restrictions to prevent unauthorized access to the `/flash/addcrypted2` endpoint.
rule: version <= "0.5.0b3.dev85"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-r9pp-r4xf-597r
  - https://nvd.nist.gov/vuln/detail/CVE-2024-39205
  - https://github.com/Marven11/CVE-2024-28397-js2py-Sandbox-Escape
  - https://github.com/advisories/GHSA-h95x-26f3-88hr
  - https://github.com/pyload/pyload