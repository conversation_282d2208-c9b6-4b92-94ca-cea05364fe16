info:
  name: pyload-ng
  cve: CVE-2023-0488
  summary: Cross-site Scripting in pyload-ng
  details: |
    Cross-site Scripting (XSS) vulnerability was found in the GitHub repository pyload/pyload prior to version 0.5.0b3.dev42.
    This vulnerability allows attackers to inject malicious scripts into web pages viewed by other users.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to pyload-ng >= 0.5.0b3.dev42
    2. Review and sanitize all user input to prevent XSS attacks
    3. Regularly update and patch the software to mitigate future vulnerabilities
rule: version < "0.5.0b3.dev42"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-0488
  - https://github.com/pyload/pyload/commit/46d75a3087f3237d06530d55998938e2e2bda6bd
  - https://github.com/pyload/pyload
  - https://huntr.dev/bounties/4311d8d7-682c-4f2a-b92c-3f9f1a36255a