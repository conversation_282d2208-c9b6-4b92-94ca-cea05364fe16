info:
  name: jupyter-server
  cve: CVE-2022-24757
  summary: Insertion of Sensitive Information into Log File in Jupyter notebook
  details: |
    Anytime a 5xx error is triggered, the auth cookie and other header values are recorded in Jupyter Server logs by default. Considering these logs do not require root access, an attacker can monitor these logs, steal sensitive auth/cookie information, and gain access to the Jupyter server.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to Jupyter Server version 1.15.4 or later.
    2. Monitor and restrict access to Jupyter Server logs to prevent unauthorized information leakage.
rule: version >= "0" && version < "1.15.4"
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-p737-p57g-4cpr
  - https://nvd.nist.gov/vuln/detail/CVE-2022-24757
  - https://github.com/jupyter-server/jupyter_server/commit/a5683aca0b0e412672ac6218d09f74d44ca0de5a
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2022-179.yaml