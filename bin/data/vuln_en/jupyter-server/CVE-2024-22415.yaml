info:
  name: jupyter-server
  cve: CVE-2024-22415
  summary: Unsecured endpoints in the jupyter-lsp server extension
  details: |
    Installations of jupyter-lsp running in environments without configured file system access control (on the operating system level), and with jupyter-server instances exposed to non-trusted network are vulnerable to unauthorized access and modification of file system beyond the jupyter root directory.
    Version 2.2.2 has been patched.
    Users of jupyterlab who do not use jupyterlab-lsp can uninstall jupyter-lsp.
    Credits to <PERSON><PERSON>, researcher of pillar.security research team, for the discovery and responsible disclosure of this vulnerability.
    Based on advice from pillar.security, the Confidentiality/Integrity/Availability were increased to High to reflect potential for critical impact on publicly hosted jupyter-server instances lacking isolation of user privileges on operating system level.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to jupyter-lsp version 2.2.2 or higher.
    2. If jupyterlab-lsp is not in use, uninstall jupyter-lsp.
    3. Ensure file system access control is configured on the operating system level.
    4. Follow best practices for user privilege isolation as outlined in https://jupyterhub.readthedocs.io/en/stable/explanation/websecurity.html#protect-users-from-each-other
rule: version <= "2.2.1"
references:
  - https://github.com/jupyter-lsp/jupyterlab-lsp/security/advisories/GHSA-4qhp-652w-c22x
  - https://nvd.nist.gov/vuln/detail/CVE-2024-22415
  - https://github.com/jupyter-lsp/jupyterlab-lsp/commit/4ad12f204ad0b85580fc32137c647baaff044e95
  - https://github.com/jupyter-lsp/jupyterlab-lsp
  - https://github.com/jupyter-lsp/jupyterlab-lsp/releases/tag/v5.0.2