info:
  name: jupyter-server
  cve: CVE-2023-49080
  summary: jupyter-server errors include tracebacks with path information
  details: |
    Unhandled errors in API requests include traceback information, which can include path information. 
    There is no known mechanism by which to trigger these errors without authentication, so the paths revealed 
    are not considered particularly sensitive, given that the requesting user has arbitrary execution permissions 
    already in the same environment.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to jupyter-server >= 2.11.2
    2. Monitor for any further updates or patches related to this vulnerability
rule: version >= "0" && version < "2.11.2"
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-h56g-gq9v-vc8r
  - https://nvd.nist.gov/vuln/detail/CVE-2023-49080
  - https://github.com/jupyter-server/jupyter_server/commit/0056c3aa52cbb28b263a7a609ae5f17618b36652
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2023-272.yaml
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/62LO7PPIAMLIDEKUOORXLHKLGA6QPL77
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/FG2JWZI5KPUYMDPS53AIFTZJWZD3IT6I