info:
  name: jupyter-server
  cve: CVE-2023-39968
  summary: Open Redirect Vulnerability in jupyter-server
  details: |
    Open Redirect Vulnerability. Maliciously crafted login links to known Jupyter Servers can cause successful login or an already logged-in session to be redirected to arbitrary sites, which should be restricted to Jupyter Server-served URLs.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to Jupyter Server 2.7.2 or later.
    2. Monitor for any suspicious login attempts that may indicate an open redirect attack.
rule: version >= "0" && version < "2.7.2"
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-r726-vmfq-j9j3
  - https://nvd.nist.gov/vuln/detail/CVE-2023-39968
  - https://blog.xss.am/2023/08/cve-2023-39968-jupyter-token-leak
  - https://github.com/jupyter-server/jupyter_server/commit/290362593b2ffb23c59f8114d76f77875de4b925
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2023-155.yaml
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/NRP7DNZYVOIA4ZB3U3ZWKTFZEPYWNGCQ
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/XDKQAWQN6SQTOVACZNXYKEHWQXGG4DOF