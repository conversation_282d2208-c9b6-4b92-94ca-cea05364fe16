info:
  name: jupyter-server
  cve: CVE-2020-26275
  summary: Jupyter Server open redirect vulnerability
  details: |
    Open redirect vulnerability - a maliciously crafted link to a jupyter server could redirect the browser to a different website.
    All jupyter servers running without a base_url prefix are technically affected, however, these maliciously crafted links can only be reasonably made for known jupyter server hosts. A link to your jupyter server may *appear* safe, but ultimately redirect to a spoofed server on the public internet. This same vulnerability was patched in upstream notebook v5.7.8.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to jupyter-server >= 1.1.1
    2. If upgrade is not available, run your server on a URL prefix:
       ```
       jupyter server --ServerApp.base_url=/jupyter/
       ```
rule: version < "1.1.1"
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-9f66-54xg-pc2c
  - https://nvd.nist.gov/vuln/detail/CVE-2020-26275
  - https://github.com/jupyter-server/jupyter_server/commit/85e4abccf6ea9321d29153f73b0bd72ccb3a6bca
  - https://advisory.checkmarx.net/advisory/CX-2020-4291
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2020-50.yaml
  - https://pypi.org/project/jupyter-server
  - https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html