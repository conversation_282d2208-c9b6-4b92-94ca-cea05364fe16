info:
  name: jupyter-server
  cve: CVE-2024-28179
  summary: Jupyter Server Proxy's Websocket Proxying does not require authentication
  details: |
    `jupyter-server-proxy` is used to expose ports local to a Jupyter server listening to web traffic to the Jupyter server's authenticated users by proxying web requests and websockets. This feature is commonly used in hosted environments to expose non-Jupyter interactive frontends or APIs to the user. The vulnerability arises because `jupyter-server-proxy` did not check user authentication appropriately when proxying websockets, allowing unauthenticated access to anyone who had network access to the Jupyter server endpoint.
    This vulnerability can allow unauthenticated remote access to any websocket endpoint set up to be accessible via `jupyter-server-proxy`, leading to remote unauthenticated arbitrary code execution in many cases.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade `jupyter-server-proxy` to a patched version:
       - For general users: `pip install "jupyter-server-proxy>=3.2.3,!=4.0.0,!=4.1.0"`
       - For TLJH admins: Follow the instructions provided in the advisory for checking and patching vulnerabilities.
       - For Z2JH admins: Consider updating docker images and using the provided script to dynamically check and patch the vulnerability before starting user servers.
    2. Restart any running Jupyter server after upgrading.
    3. Consider terminating currently running user servers that were started before the patch was applied to ensure they are no longer vulnerable.
rule: version < "3.2.3" || (version >= "4.0.0" && version < "4.1.1")
references:
  - https://github.com/jupyterhub/jupyter-server-proxy/security/advisories/GHSA-w3vc-fx9p-wp4v
  - https://nvd.nist.gov/vuln/detail/CVE-2024-28179
  - https://github.com/jupyterhub/jupyter-server-proxy/commit/764e499f61a87641916a7a427d4c4b1ac3f321a9
  - https://github.com/jupyterhub/jupyter-server-proxy/commit/bead903b7c0354b6efd8b4cde94b89afab653e03
  - https://github.com/jupyterhub/jupyter-server-proxy
  - https://github.com/jupyterhub/jupyter-server-proxy/blob/9b624c4d9507176334b46a85d94a4aa3bcd29bed/jupyter_server_proxy/handlers.py#L433
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server-proxy/PYSEC-2024-234.yaml