info:
  name: jupyter-server
  cve: CVE-2025-30167
  summary: Jupyter Core on Windows Has Uncontrolled Search Path Element Local Privilege Escalation Vulnerability
  details: |
    On Windows, the shared `%PROGRAMDATA%` directory is searched for configuration files (`SYSTEM_CONFIG_PATH` and `SYSTEM_JUPYTER_PATH`), which may allow users to create configuration files affecting other users.
    
    Only shared Windows systems with multiple users and unprotected `%PROGRAMDATA%` are affected.
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to `jupyter_core>=5.8.1` (5.8.0 is patched but breaks `jupyter-server`)
    2. As administrator, modify the permissions on the `%PROGRAMDATA%` directory so it is not writable by unauthorized users
    3. As administrator, create the `%PROGRAMDATA%\\jupyter` directory with appropriately restrictive permissions
    4. As user or administrator, set the `%PROGRAMDATA%` environment variable to a directory with appropriately restrictive permissions (e.g. controlled by administrators or the current user)
rule: version < "5.8.1"
references:
  - https://github.com/jupyter/jupyter_core/security/advisories/GHSA-33p9-3p43-82vq
  - https://nvd.nist.gov/vuln/detail/CVE-2025-30167
  - https://github.com/jupyter/jupyter_core