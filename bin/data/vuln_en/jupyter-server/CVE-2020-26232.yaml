info:
  name: jupyter-server
  cve: CVE-2020-26232
  summary: Open redirect vulnerability in Jupyter Server
  details: |
    The open redirect vulnerability in Jupyter Server allows a maliciously crafted link to redirect the browser to a different website. While all Jupyter servers are technically affected, such links can only be reasonably crafted for known Jupyter server hosts, potentially leading users to spoofed servers on the public internet.
    This vulnerability originated in the jupyter/notebook project and was later addressed in the jupyter-server package.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:N/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to jupyter-server version 1.0.6 or later.
    2. Review and validate all external links to ensure they do not point to untrusted domains.
    3. Implement additional security measures such as Content Security Policies (CSP) to mitigate the risk of open redirects.
rule: version >= "0" && version < "1.0.6"
references:
  - https://github.com/jupyter/jupyter_server/security/advisories/GHSA-grfj-wjv9-4f9v
  - https://nvd.nist.gov/vuln/detail/CVE-2020-26232
  - https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html
  - https://github.com/jupyter-server/jupyter_server/commit/3d83e49090289c431da253e2bdb8dc479cbcb157
  - https://github.com/jupyter-server/jupyter_server/blob/master/CHANGELOG.md#106---2020-11-18
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2020-234.yaml