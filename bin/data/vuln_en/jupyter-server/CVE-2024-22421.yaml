info:
  name: jupyter-server
  cve: CVE-2024-22421
  summary: <PERSON><PERSON><PERSON><PERSON><PERSON> vulnerable to potential authentication and CSRF tokens leak
  details: |
    Users of JupyterLab who click on a malicious link may get their `Authorization` and `XSRFToken` tokens exposed to a third party when running an older `jupyter-server` version.
    
    JupyterLab 4.1.0b2, 4.0.11, and 3.6.7 were patched.
    
    No workaround has been identified, however users should ensure to upgrade `jupyter-server` to version 2.7.2 or newer which includes a redirect vulnerability fix.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade `jupyter-server` to version 2.7.2 or newer.
    2. Ensure all JupyterLab instances are updated to the latest patched version (4.1.0b2, 4.0.11, or 3.6.7).
rule: version < "2.7.2"
references:
  - https://github.com/jupyterlab/jupyterlab/security/advisories/GHSA-44cc-43rp-5947
  - https://nvd.nist.gov/vuln/detail/CVE-2024-22421
  - https://github.com/jupyterlab/jupyterlab/commit/19bd9b96cb2e77170a67e43121637d0b5619e8c6
  - https://github.com/jupyterlab/jupyterlab/commit/1ef7a4fa0202ebdf663e1cc0b45c8813a34a0b96
  - https://github.com/jupyterlab/jupyterlab/commit/fccd83dc4441da0384ee3fd1322c3b2d9ad4caaa
  - https://github.com/jupyterlab/jupyterlab
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/UQJKNRDRFMKGVRIYNNN6CKMNJDNYWO2H