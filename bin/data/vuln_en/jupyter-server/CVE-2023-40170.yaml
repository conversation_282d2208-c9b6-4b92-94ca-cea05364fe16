info:
  name: jupyter-server
  cve: CVE-2023-40170
  summary: Cross-site inclusion (XSSI) of files in jupyter-server
  details: |
    Improper cross-site credential checks on `/files/` URLs could allow exposure of certain file contents, or accessing files when opening untrusted files via "Open image in new tab".
    
    Upstream patch for CVE-2019-9644 was not applied completely, leaving part of the vulnerability open.
    
    Vulnerability reported by <PERSON> via the [bug bounty program](https://app.intigriti.com/programs/jupyter/jupyter/detail) [sponsored by the European Commission](https://commission.europa.eu/news/european-commissions-open-source-programme-office-starts-bug-bounties-2022-01-19_en) and hosted on the [Intigriti platform](https://www.intigriti.com/).
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to jupyter-server >= 2.7.2
    2. As a workaround, use the lower performance `--ContentsManager.files_handler_class=jupyter_server.files.handlers.FilesHandler`, which implements the correct checks.
rule: version >= "0" && version < "2.7.2"
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-64x5-55rw-9974
  - https://nvd.nist.gov/vuln/detail/CVE-2023-40170
  - https://github.com/jupyter-server/jupyter_server/commit/87a4927272819f0b1cae1afa4c8c86ee2da002fd
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2023-157.yaml
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/NRP7DNZYVOIA4ZB3U3ZWKTFZEPYWNGCQ
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/XDKQAWQN6SQTOVACZNXYKEHWQXGG4DOF