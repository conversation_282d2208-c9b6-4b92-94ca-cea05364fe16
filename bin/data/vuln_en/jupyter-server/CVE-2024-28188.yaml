info:
  name: jupyter-server
  cve: CVE-2024-28188
  summary: jupyter-scheduler's endpoint is missing authentication
  details: |
    `jupyter_scheduler` is missing an authentication check in Jupyter Server on an API endpoint (`GET /scheduler/runtime_environments`) which lists the names of the Conda environments on the server. In affected versions, `jupyter_scheduler` allows an unauthenticated user to obtain the list of Conda environment names on the server. This reveals any information that may be present in a Conda environment name.
    
    This issue does **not** allow an unauthenticated third party to read, modify, or enter the Conda environments present on the server where `jupyter_scheduler` is running. This issue only reveals the list of Conda environment names.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to jupyter-scheduler>=1.1.6, >=1.2.1, >=1.8.2, or >=2.5.2
    2. If upgrading is not immediately possible, disable the `jupyter-scheduler` extension with:
       ```
       jupyter server extension disable jupyter-scheduler
       ```
rule: (version >= "1.0.0" && version <= "1.1.5") || version == "1.2.0" || (version >= "1.3.0" && version <= "1.8.1") || (version >= "2.0.0" && version <= "2.5.1")
references:
  - https://github.com/jupyter-server/jupyter-scheduler/security/advisories/GHSA-v9g2-g7j4-4jxc
  - https://nvd.nist.gov/vuln/detail/CVE-2024-28188
  - https://github.com/jupyter-server/jupyter_server/pull/1392
  - https://github.com/jupyter-server/jupyter-scheduler/commit/06435a2277bb2b8f441ec9cedafa474572b92c5d
  - https://github.com/jupyter-server/jupyter-scheduler/commit/f4137a779fdf0cc4a9688a42dd8c6e7ade60f044
  - https://github.com/jupyter-server/jupyter-scheduler