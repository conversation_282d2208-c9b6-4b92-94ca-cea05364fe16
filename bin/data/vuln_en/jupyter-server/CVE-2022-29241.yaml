info:
  name: jupyter-server
  cve: CVE-2022-29241
  summary: Jupyter server Token bruteforcing
  details: |
    Affects: Notebook and Lab between 6.4.0 (potentially earlier) and 6.4.11 (currently latest). Jupyter Server <=1.16.0. If the responsible code is as described, it will also affect Jupyter-Server 1.17.0 and 2.0.0a0.
    
    Description: If notebook server is started with a value of `root_dir` that contains the starting user's home directory, then the underlying REST API can be used to leak the access token assigned at start time by guessing/brute forcing the PID of the jupyter server. While this requires an authenticated user session, this url can be used from an XSS payload (as in CVE-2021-32798) or from a hooked or otherwise compromised browser to leak this access token to a malicious third party. This token can be used along with the REST API to interact with Jupyter services/notebooks such as modifying or overwriting critical files, such as .bashrc or .ssh/authorized_keys, allowing a malicious user to read potentially sensitive data and possibly gain control of the impacted system.
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to jupyter-server >=1.17.1
    2. Upgrade to jupyter-server >=2.0.0a1 if using version 2.0.0a0
    3. Ensure `root_dir` does not contain the user's home directory to mitigate the risk of token leakage
rule: (version >= "0" && version < "1.17.1") || (version == "2.0.0a0")
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-q874-g24w-4q9g
  - https://nvd.nist.gov/vuln/detail/CVE-2022-29241
  - https://github.com/jupyter-server/jupyter_server/commit/3485007abbb459585357212dcaa20521989272e8
  - https://github.com/jupyter-server/jupyter_server/commit/877da10cd0d7ae45f8b1e385fa1f5a335e7adf1f
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2022-211.yaml