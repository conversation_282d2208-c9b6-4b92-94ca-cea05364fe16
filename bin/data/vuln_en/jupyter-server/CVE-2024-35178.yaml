info:
  name: jupyter-server
  cve: CVE-2024-35178
  summary: <PERSON>pyter server on Windows discloses Windows user password hash
  details: |
    ### Summary
    Jupyter Server on Windows has a vulnerability that lets unauthenticated attackers leak the NTLMv2 password hash of the Windows user running the Jupyter server. An attacker can crack this password to gain access to the Windows machine hosting the Jupyter server, or access other network-accessible machines or 3rd party services using that credential. Or an attacker perform an NTLM relay attack without cracking the credential to gain access to other network-accessible machines.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to jupyter_server >= 2.14.1
    2. Ensure that Jupyter Server is not running with elevated privileges
    3. Implement network segmentation to limit access to Jupyter Server instances
rule: version <= "2.14.0"
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-hrw6-wg82-cm62
  - https://nvd.nist.gov/vuln/detail/CVE-2024-35178
  - https://github.com/jupyter-server/jupyter_server/commit/79fbf801c5908f4d1d9bc90004b74cfaaeeed2df
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2024-165.yaml