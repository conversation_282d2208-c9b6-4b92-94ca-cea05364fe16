info:
  name: gradio
  cve: CVE-2024-10624
  summary: Gradio Vulnerable to Denial of Service (DoS) via Crafted HTTP Request
  details: |
    A Regular Expression Denial of Service (ReDoS) vulnerability exists in the gradio-app/gradio repository, affecting the gr.Datetime component. The affected version is git commit 98cbcae. The vulnerability arises from the use of a regular expression to process user input, which can take polynomial time to match certain crafted inputs, potentially leading to a Denial of Service (DoS) condition on the server.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio >= 5.0.0-beta.2
    2. Apply the latest security patches released by the gradio maintainers
    3. Monitor server performance for unusual activity that may indicate a DoS attack
rule: version >= "4.38.0" && version < "5.0.0-beta.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10624
  - https://github.com/gradio-app/gradio
  - https://github.com/gradio-app/gradio/blob/98cbcaef827de7267462ccba180c7b2ffb1e825d/gradio/components/datetime.py#L133-L136
  - https://huntr.com/bounties/e8d0b248-8feb-4c23-9ef9-be4d1e868374