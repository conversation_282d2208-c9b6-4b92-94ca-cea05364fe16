info:
  name: gradio
  cve: CVE-2024-47868
  summary: Gradio has several components with post-process steps allowing arbitrary file leaks
  details: |
    This is a data validation vulnerability affecting several Gradio components, which allows arbitrary file leaks through the post-processing step. Attackers can exploit these components by crafting requests that bypass expected input constraints. This issue could lead to sensitive files being exposed to unauthorized users, especially when combined with other vulnerabilities.
    
    Vulnerable Components:
    - String to FileData: DownloadButton, Audio, ImageEditor, Video, Model3D, File, UploadButton.
    - Complex data to FileData: Chatbot, MultimodalTextbox.
    - Direct file read in preprocess: Code.
    - Dictionary converted to FileData: ParamViewer, Dataset.
    
    Exploit Scenarios:
    1. A developer creates a Dropdown list that passes values to a DownloadButton. An attacker bypasses the allowed inputs, sends an arbitrary file path (like `/etc/passwd`), and downloads sensitive files.
    2. An attacker crafts a malicious payload in a ParamViewer component, leaking sensitive files from a server through the arbitrary file leak.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=5.0.0
    2. Review and update all Gradio components to ensure they handle file data securely
    3. Implement additional input validation to prevent bypassing of expected constraints
rule: version < "5.0.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-4q3c-cj7g-jcwf
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47868
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-217.yaml