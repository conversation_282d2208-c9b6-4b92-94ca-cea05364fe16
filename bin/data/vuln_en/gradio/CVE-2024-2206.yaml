info:
  name: gradio
  cve: CVE-2024-2206
  summary: gradio Server-Side Request Forgery vulnerability
  details: |
    An SSRF vulnerability exists in the gradio-app/gradio due to insufficient validation of user-supplied URLs in the `/proxy` route. 
    Attackers can exploit this vulnerability by manipulating the `self.replica_urls` set through the `X-Direct-Url` header in requests to the `/` and `/config` routes, allowing the addition of arbitrary URLs for proxying. 
    This flaw enables unauthorized proxying of requests and potential access to internal endpoints within the Hugging Face space.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.18.0
    2. Implement strict validation for URLs supplied via the `X-Direct-Url` header
    3. Review and enhance the `build_proxy_request` function to ensure safe URL handling
rule: version >= "0" && version < "4.18.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-2206
  - https://github.com/gradio-app/gradio/commit/49d9c48537aa706bf72628e3640389470138bdc6
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/2286c1ed-b889-45d6-adda-7014ea06d98e