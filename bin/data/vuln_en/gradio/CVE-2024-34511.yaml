info:
  name: gradio
  cve: CVE-2024-34511
  summary: Gradio's Component Server does not properly consider `_is_server_fn` for functions
  details: |
    Component Server in Gradio before 4.13 does not properly consider `_is_server_fn` for functions.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=4.13.0
    2. Review and ensure that all server functions are properly decorated with `_is_server_fn`
rule: version >= "0" && version < "4.13.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-34511
  - https://github.com/gradio-app/gradio/commit/24a583688046867ca8b8b02959c441818bdb34a2
  - https://github.com/gradio-app/gradio
  - https://www.gradio.app/changelog#4-13-0