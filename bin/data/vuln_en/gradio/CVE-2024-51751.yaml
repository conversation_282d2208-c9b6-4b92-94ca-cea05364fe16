info:
  name: gradio
  cve: CVE-2024-51751
  summary: Gradio vulnerable to arbitrary file read with File and UploadButton components
  details: |
    If File or UploadButton components are used in a Gradio application to preview file content, an attacker with access to the application might abuse these components to read arbitrary files from the application server. This occurs due to insufficient validation of file paths when the `meta` key is missing or incorrect, allowing attackers to manipulate file paths and access sensitive data.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=5.5.0
    2. Review and modify file handling logic to ensure all file paths are properly sanitized regardless of the presence of the `meta` key
    3. Implement additional security measures to restrict file access based on user permissions
rule: version >= "5.0.0" && version < "5.5.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-rhm9-gp5p-5248
  - https://nvd.nist.gov/vuln/detail/CVE-2024-51751
  - https://github.com/gradio-app/gradio