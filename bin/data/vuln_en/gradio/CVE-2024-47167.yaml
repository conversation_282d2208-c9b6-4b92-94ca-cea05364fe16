info:
  name: gradio
  cve: CVE-2024-47167
  summary: Gradio vulnerable to SSRF in the path parameter of /queue/join
  details: |
    This vulnerability relates to **Server-Side Request Forgery (SSRF)** in the `/queue/join` endpoint. Gradio’s `async_save_url_to_cache` function allows attackers to force the Gradio server to send HTTP requests to user-controlled URLs. This could enable attackers to target internal servers or services within a local network and possibly exfiltrate data or cause unwanted internal requests. Additionally, the content from these URLs is stored locally, making it easier for attackers to upload potentially malicious files to the server. This impacts users deploying Gradio servers that use components like the Video component which involve URL fetching.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=5.0.0 to address this issue.
    2. As a workaround, disable or heavily restrict URL-based inputs in your Gradio applications to trusted domains only.
    3. Implement stricter URL validation (such as allowlist-based validation) and ensure that local or internal network addresses cannot be requested via the `/queue/join` endpoint.
rule: version < "5.0.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-576c-3j53-r9jj
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47167
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-215.yaml