info:
  name: gradio
  cve: CVE-2024-1540
  summary: Gradio's CI vulnerable to Command Injection
  details: |
    A command injection vulnerability exists in the deploy+test-visual.yml workflow of the gradio-app/gradio repository,
    due to improper neutralization of special elements used in a command. This vulnerability allows attackers to execute
    unauthorized commands, potentially leading to unauthorized modification of the base repository or secrets exfiltration.
    The issue arises from the unsafe handling of GitHub context information within an action definition which is evaluated
    and substituted before script execution.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.18.0
    2. Review and update the deploy+test-visual.yml workflow to safely handle GitHub context information
    3. Set untrusted input values to intermediate environment variables to prevent direct influence on script generation
rule: version >= "0" && version < "4.18.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1540
  - https://github.com/gradio-app/gradio/commit/d56bb28df80d8db1f33e4acf4f6b2c4f87cb8b28
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/0e39e974-9a66-476f-91f5-3f37abb03d77