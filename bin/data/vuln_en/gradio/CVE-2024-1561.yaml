info:
  name: gradio
  cve: CVE-2024-1561
  summary: gradio vulnerable to Path Traversal
  details: |
    An issue was discovered in gradio-app/gradio, where the `/component_server` endpoint improperly allows the invocation of any method on a `Component` class with attacker-controlled arguments. Specifically, by exploiting the `move_resource_to_block_cache()` method of the `Block` class, an attacker can copy any file on the filesystem to a temporary directory and subsequently retrieve it. This vulnerability enables unauthorized local file read access, posing a significant risk especially when the application is exposed to the internet via `launch(share=True)`, thereby allowing remote attackers to read files on the host machine. Furthermore, gradio apps hosted on `huggingface.co` are also affected, potentially leading to the exposure of sensitive information such as API keys and credentials stored in environment variables.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.13.0
    2. Review and restrict the use of the `/component_server` endpoint to prevent unauthorized method invocation
    3. Monitor and audit file access logs for any suspicious activity
rule: version >= "0" && version < "4.13.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1561
  - https://github.com/gradio-app/gradio/commit/24a583688046867ca8b8b02959c441818bdb34a2
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/4acf584e-2fe8-490e-878d-2d9bf2698338
  - https://www.gradio.app/changelog#4-13-0