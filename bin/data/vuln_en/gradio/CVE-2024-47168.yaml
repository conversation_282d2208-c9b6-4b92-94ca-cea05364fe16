info:
  name: gradio
  cve: CVE-2024-47168
  summary: In Gradio, the `enable_monitoring` flag set to `False` does not disable monitoring
  details: |
    ### Impact
    What kind of vulnerability is it? Who is impacted?
    This vulnerability involves data exposure due to the enable_monitoring flag not properly disabling monitoring when set to False. Even when monitoring is supposedly disabled, an attacker or unauthorized user can still access the monitoring dashboard by directly requesting the /monitoring endpoint. This means that sensitive application analytics may still be exposed, particularly in environments where monitoring is expected to be disabled. Users who set enable_monitoring=False to prevent unauthorized access to monitoring data are impacted.
    ### Patches
    Yes, please upgrade to gradio>=4.44 to address this issue.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: LOW
  security_advise: |
    1. Upgrade to gradio>=4.44
rule: version >= "0" && version < "4.44.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-hm3c-93pg-4cxw
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47168
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-198.yaml