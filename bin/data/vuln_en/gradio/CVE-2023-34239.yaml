info:
  name: gradio
  cve: CVE-2023-34239
  summary: Gradio vulnerable to arbitrary file read and proxying of arbitrary URLs
  details: |
    There are two separate security vulnerabilities here:
    1. A security vulnerability that allows users to read arbitrary files on the machines that are running shared Gradio apps.
    2. The ability of users to use machines that are sharing Gradio apps to proxy arbitrary URLs.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=3.34.0
    2. Take down any shared Gradio apps until the upgrade is complete
rule: version >= "0" && version < "3.34.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-3qqg-pgqq-3695
  - https://nvd.nist.gov/vuln/detail/CVE-2023-34239
  - https://github.com/gradio-app/gradio/pull/4406
  - https://github.com/gradio-app/gradio/pull/4370
  - https://github.com/gradio-app/gradio/commit/37967617bd97615fb6f3b44e7750c0e0be58479a
  - https://github.com/gradio-app/gradio/commit/cd64130d54e678525774bbb200ef9c7166fa1543
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2023-90.yaml