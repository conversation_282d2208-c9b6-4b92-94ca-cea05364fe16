info:
  name: gradio
  cve: CVE-2022-24770
  summary: Improper Neutralization of Formula Elements in a CSV File in Gradio Flagging
  details: |
    The `gradio` library has a flagging functionality which saves input/output data into a CSV file on the developer's computer. This can allow a user to save arbitrary text into the CSV file, such as commands. If a program like MS Excel opens such a file, then it automatically runs these commands, which could lead to arbitrary commands running on the user's computer.
    
    The problem has been patched as of `2.8.11`, which escapes the data saved to the csv with single quotes.
    
    If you are using an older version of `gradio`, don't open csv files generated by `gradio` with Excel or similar spreadsheet programs.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=2.8.11
    2. Avoid opening CSV files generated by gradio with Excel or similar spreadsheet programs if using an older version of gradio
rule: version > "0" && version < "2.8.11"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-f8xq-q7px-wg8c
  - https://nvd.nist.gov/vuln/detail/CVE-2022-24770
  - https://github.com/gradio-app/gradio/pull/817
  - https://github.com/gradio-app/gradio/commit/80fea89117358ee105973453fdc402398ae20239
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2022-229.yaml