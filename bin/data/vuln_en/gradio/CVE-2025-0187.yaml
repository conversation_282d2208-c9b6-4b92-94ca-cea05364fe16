info:
  name: gradio
  cve: CVE-2025-0187
  summary: Denial of Service (DoS) vulnerability in file upload feature
  details: |
    A Denial of Service (DoS) vulnerability was discovered in the file upload feature of gradio-app/gradio version 0.39.1.
    The vulnerability is due to improper handling of form-data with a large filename in the file upload request.
    By sending a payload with an excessively large filename, the server becomes overwhelmed and unresponsive,
    leading to unavailability for legitimate users.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=0.39.2
    2. Implement server-side validation to limit filename sizes
    3. Monitor server logs for unusual activity that may indicate an attack
rule: version < "0.39.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0187
  - https://huntr.com/bounties/77f3ed54-9e1c-4d9f-948f-ee6f82e2fe24