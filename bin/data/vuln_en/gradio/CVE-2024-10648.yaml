info:
  name: gradio
  cve: CVE-2024-10648
  summary: Gradio Vulnerable to Arbitrary File Deletion
  details: |
    A path traversal vulnerability exists in the Gradio Audio component of gradio-app/gradio, as of version git 98cbcae.
    This vulnerability allows an attacker to control the format of the audio file, leading to arbitrary file content deletion.
    By manipulating the output format, an attacker can reset any file to an empty file, causing a denial of service (DOS) on the server.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio >= 5.0.0b3
    2. Review and patch the path traversal vulnerability in the Gradio Audio component
    3. Implement strict file input validation to prevent unauthorized file manipulation
rule: version >= "4.0.0" && version < "5.0.0b3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10648
  - https://github.com/gradio-app/gradio
  - https://github.com/gradio-app/gradio/blame/98cbcaef827de7267462ccba180c7b2ffb1e825d/gradio/processing_utils.py#L234
  - https://huntr.com/bounties/667d664d-8189-458c-8ed7-483fe8f33c76