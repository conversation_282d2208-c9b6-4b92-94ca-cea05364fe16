info:
  name: gradio
  cve: CVE-2024-47166
  summary: Gradio has a one-level read path traversal in `/custom_component`
  details: |
    This vulnerability involves a **one-level read path traversal** in the `/custom_component` endpoint. Attackers can exploit this flaw to access and leak source code from custom Gradio components by manipulating the file path in the request. Although the traversal is limited to a single directory level, it could expose proprietary or sensitive code that developers intended to keep private. This impacts users who have developed custom Gradio components and are hosting them on publicly accessible servers.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=4.44 to address this issue.
    2. As a workaround, developers can sanitize the file paths and ensure that components are not stored in publicly accessible directories.
rule: version > "0" && version < "4.44.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-37qc-qgx6-9xjv
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47166
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-197.yaml