info:
  name: gradio
  cve: CVE-2024-34510
  summary: Gradio before 4.20 allows credential leakage on Windows.
  details: |
    The vulnerability affects Gradio versions prior to 4.20, enabling attackers to potentially
    access sensitive credentials on Windows systems through improper handling of certain
    operations.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.20
    2. Review and enhance credential handling mechanisms
    3. Monitor for any suspicious activity related to credential access
rule: version < "4.20.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-34510
  - https://github.com/gradio-app/gradio
  - https://www.gradio.app/changelog#4-20-0