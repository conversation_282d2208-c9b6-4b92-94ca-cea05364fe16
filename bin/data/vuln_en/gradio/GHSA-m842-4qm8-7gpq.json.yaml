info:
  name: gradio
  cve: CVE-2024-1728
  summary: Gradio allows users to access arbitrary files
  details: |
    ### Impact
    This vulnerability allows users of Gradio applications that have a public link (such as on Hugging Face Spaces) to access files on the machine hosting the Gradio application. This involves intercepting and modifying the network requests made by the Gradio app to the server.
    ### Patches
    Yes, the problem has been patched in Gradio version 4.19.2 or higher. We have no knowledge of this exploit being used against users of Gradio applications, but we encourage all users to upgrade to Gradio 4.19.2 or higher.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to gradio>=4.19.2
    2. Monitor and restrict access to Gradio applications with public links
    3. Regularly check for updates and security advisories from the Gradio project
rule: version > "0" && version < "4.19.2"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-m842-4qm8-7gpq
  - https://github.com/gradio-app/gradio/commit/16fbe9cd0cffa9f2a824a0165beb43446114eec7
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1728