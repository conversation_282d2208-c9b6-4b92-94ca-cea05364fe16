info:
  name: gradio
  cve: CVE-2024-8966
  summary: Denial of Service (DoS) vulnerability in Gradio file upload process
  details: |
    A vulnerability in the file upload process of gradio-app/gradio version @gradio/video@0.10.2 allows for a Denial of Service (DoS) attack. An attacker can append a large number of characters to the end of a multipart boundary, causing the system to continuously process each character and issue warnings. This can render Gradio inaccessible for extended periods, disrupting services and causing significant downtime.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio-video@0.10.3 or later
    2. Implement input validation to limit the size of multipart boundaries
    3. Monitor and log unusual activity to detect potential DoS attacks
rule: version = "@gradio/video@0.10.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8966
  - https://huntr.com/bounties/7b5932bb-58d1-4e71-b85c-43dc40522ff2