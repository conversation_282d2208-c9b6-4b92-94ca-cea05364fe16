info:
  name: gradio
  cve: CVE-2024-4253
  summary: Command injection vulnerability in Gradio workflow
  details: |
    A command injection vulnerability exists in the gradio-app/gradio repository, specifically within the 'test-functional.yml' workflow. The vulnerability arises due to improper neutralization of special elements used in a command, allowing for unauthorized modification of the base repository or secrets exfiltration. The issue affects versions up to and including '@gradio/video@0.6.12'. The flaw is present in the workflow's handling of GitHub context information, where it echoes the full name of the head repository, the head branch, and the workflow reference without adequate sanitization. This could potentially lead to the exfiltration of sensitive secrets such as 'GITHUB_TOKEN', 'COMMENT_TOKEN', and 'CHROMATIC_PROJECT_TOKEN'.
  cvss: CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio-video@0.6.13 or later
    2. Review and sanitize all inputs in GitHub workflows
    3. Implement strict access controls for sensitive tokens
rule: version <= "0.6.12"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4253
  - https://github.com/gradio-app/gradio/commit/a0e70366a8a406fdd80abb21e8c88a3c8e682a2b
  - https://huntr.com/bounties/23cb3749-8ae9-4e1a-9023-4a20ca6b675e