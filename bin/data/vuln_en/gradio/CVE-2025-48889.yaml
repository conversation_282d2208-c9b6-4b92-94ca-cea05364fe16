info:
  name: gradio
  cve: CVE-2025-48889
  summary: Gradio Allows Unauthorized File Copy via Path Manipulation
  details: |
    An arbitrary file copy vulnerability in Gradio's flagging feature allows unauthenticated attackers to copy any readable file from the server's filesystem. While attackers can't read these copied files, they can cause DoS by copying large files (like /dev/urandom) to fill disk space.
    The flagging component doesn't properly validate file paths before copying files. Attackers can send specially crafted requests to the `/gradio_api/run/predict` endpoint to trigger these file copies.
    **Source**: User-controlled `path` parameter in the flagging functionality JSON payload  
    **Sink**: `shutil.copy` operation in `FileData._copy_to_dir()` method
    The vulnerable code flow:
    1. A JSON payload is sent to the `/gradio_api/run/predict` endpoint
    2. The `path` field within `FileData` object can reference any file on the system
    3. When processing this request, the `Component.flag()` method creates a `GradioDataModel` object
    4. The `FileData._copy_to_dir()` method uses this path without proper validation:
    ```python
    def _copy_to_dir(self, dir: str) -> FileData:
        pathlib.Path(dir).mkdir(exist_ok=True)
        new_obj = dict(self)
        if not self.path:
            raise ValueError("Source file path is not set")
        new_name = shutil.copy(self.path, dir)  # vulnerable sink
        new_obj["path"] = new_name
        return self.__class__(**new_obj)
    ```
    5. The lack of validation allows copying any file the Gradio process can read
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=5.31.0
    2. Implement strict path validation for file operations in the flagging feature
    3. Monitor and limit the size of files that can be copied to prevent DoS attacks
rule: version >= "0" && version < "5.31.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-8jw3-6x8j-v96g
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48889
  - https://github.com/gradio-app/gradio