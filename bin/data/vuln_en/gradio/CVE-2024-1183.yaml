info:
  name: gradio
  cve: CVE-2024-1183
  summary: gradio Server-Side Request Forgery vulnerability
  details: |
    An SSRF (Server-Side Request Forgery) vulnerability exists in the gradio-app/gradio repository, allowing attackers to scan and identify open ports within an internal network. By manipulating the 'file' parameter in a GET request, an attacker can discern the status of internal ports based on the presence of a 'Location' header or a 'File not allowed' error in the response.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=4.10.0
    2. Implement strict input validation for all request parameters
    3. Monitor and restrict access to internal network services from external interfaces
rule: version >= "0" && version < "4.10.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1183
  - https://github.com/gradio-app/gradio/commit/2ad3d9e7ec6c8eeea59774265b44f11df7394bb4
  - https://github.com/gradio-app/gradio/commit/7ba8c5da45b004edd12c0460be9222f5b5f5f055
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/103434f9-87d2-42ea-9907-194a3c25007c