info:
  name: gradio
  cve: CVE-2024-1727
  summary: Cross-Site Request Forgery in Gradio
  details: |
    A Cross-Site Request Forgery vulnerability in Gradio allows attackers to trick users into uploading many large files to their local Gradio instances. This issue arises due to insufficient CORS configurations. A pull request has been submitted to tighten the CORS rules, ensuring that requests are only accepted from localhost or its aliases when appropriate headers are present.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:N/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio >= 4.19.2 to apply the fix.
    2. Review and enforce strict CORS policies in your Gradio applications.
    3. Monitor for any suspicious file upload activities post-upgrade.
rule: version >= "0" && version < "4.19.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1727
  - https://github.com/gradio-app/gradio/pull/7503
  - https://github.com/gradio-app/gradio/commit/84802ee6a4806c25287344dce581f9548a99834a
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/a94d55fb-0770-4cbe-9b20-97a978a2ffff