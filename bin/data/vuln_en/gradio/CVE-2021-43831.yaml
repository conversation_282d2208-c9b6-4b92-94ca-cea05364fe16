info:
  name: gradio
  cve: CVE-2021-43831
  summary: Files on the host computer can be accessed from the Gradio interface
  details: |
    This is a vulnerability that affects anyone who creates and publicly shares Gradio interfaces using `gradio<2.4.8`. Because of the way that static files were being served, someone who generated a public Gradio link and shared it with others would potentially be exposing the files on the computer that generated the link, while the link was active. An attacker would be able to view the contents of a file on the computer if they knew the exact relative filepath.
  cvss: CVSS:3.1/AV:A/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:L
  severity: CRITICAL
  security_advise: |
    1. Upgrade to gradio>=2.5.0
    2. Ensure that no Gradio interfaces are publicly accessible with versions below 2.5.0
    3. Regularly check for updates and security advisories from the Gradio team
rule: version < "2.5.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-rhq2-3vr9-6mcr
  - https://nvd.nist.gov/vuln/detail/CVE-2021-43831
  - https://github.com/gradio-app/gradio/commit/41bd3645bdb616e1248b2167ca83636a2653f781
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2021-873.yaml