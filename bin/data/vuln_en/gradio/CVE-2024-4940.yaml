info:
  name: gradio
  cve: CVE-2024-4940
  summary: Open redirect in gradio
  details: |
    An open redirect vulnerability exists in the gradio-app/gradio, affecting the latest version. The vulnerability allows an attacker to redirect users to arbitrary websites, which can be exploited for phishing attacks, Cross-site Scripting (XSS), Server-Side Request Forgery (SSRF), amongst others. This issue is due to improper validation of user-supplied input in the handling of URLs. Attackers can exploit this vulnerability by crafting a malicious URL that, when processed by the application, redirects the user to an attacker-controlled web page.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=4.36.2
    2. Implement strict validation for user-supplied URLs
    3. Monitor for suspicious redirect patterns
rule: version > "0" && version < "4.36.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4940
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/35aaea93-6895-4f03-9c1b-cd992665aa60