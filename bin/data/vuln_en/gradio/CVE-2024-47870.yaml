info:
  name: gradio
  cve: CVE-2024-47870
  summary: Grad<PERSON> has a race condition in update_root_in_config may redirect user traffic
  details: |
    ### Impact
    **What kind of vulnerability is it? Who is impacted?**
    This vulnerability involves a **race condition** in the `update_root_in_config` function, allowing an attacker to modify the `root` URL used by the Gradio frontend to communicate with the backend. By exploiting this flaw, an attacker can redirect user traffic to a malicious server. This could lead to the interception of sensitive data such as authentication credentials or uploaded files. This impacts all users who connect to a Gradio server, especially those exposed to the internet, where malicious actors could exploit this race condition.
    ### Patches
    Yes, please upgrade to `gradio>=5` to address this issue.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=5.0.0
    2. Monitor server logs for unusual traffic redirections
    3. Implement additional security measures to detect and prevent URL redirection attacks
rule: version >= "0" && version < "5.0.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-xh2x-3mrm-fwqm
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47870
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-218.yaml