info:
  name: gradio
  cve: CVE-2025-23042
  summary: Gradio Blocked Path ACL Bypass Vulnerability
  details: |
    Gradio's Access Control List (ACL) for file paths can be bypassed by altering the letter case of a blocked file or directory path due to the lack of case normalization in the file path validation logic. This enables attackers to circumvent security restrictions and access sensitive files on case-insensitive file systems, potentially leading to unauthorized data access and broader application or system compromise.
  cvss: CVSS:4.0/AV:N/AC:L/AT:P/PR:N/UI:N/VC:H/VI:H/VA:N/SC:N/SI:N/SA:N
  severity: CRITICAL
  security_advise: |
    1. Normalize Path Case:
       - Before evaluating paths against the ACL, normalize the case of both the requested path and the blocked paths (e.g., convert all paths to lowercase).
       - Example:
         ```python
         normalized_path = str(path).lower()
         normalized_blocked_paths = [str(p).lower() for p in blocked_paths]
         ```
    2. Update Documentation:
       - Warn developers about potential risks when deploying Gradio on case-insensitive file systems.
    3. Release Security Patches:
       - Notify users of the vulnerability and release an updated version of <PERSON>rad<PERSON> with the fixed logic.
rule: version <= "5.6.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-j2jg-fq62-7c3h
  - https://nvd.nist.gov/vuln/detail/CVE-2025-23042
  - https://github.com/gradio-app/gradio/commit/6b63fdec441b5c9bf910f910a2505d8defbb6bf8
  - https://github.com/gradio-app/gradio/releases/tag/gradio%405.11.0