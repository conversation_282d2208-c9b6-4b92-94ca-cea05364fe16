info:
  name: gradio
  cve: CVE-2023-51449
  summary: Gradio fixes `/file` route against file traversal and SSRF attacks
  details: |
    Older versions of `gradio` had a vulnerability in the `/file` route that allowed file traversal and server-side request forgery (SSRF) attacks.
    Attackers could access arbitrary files on a machine running a Gradio app with a public URL if they knew the file paths.
    These vulnerabilities were exploitable through programmatic tools like `curl` with specific flags, not regular browser URLs.
    Both issues have been addressed in `gradio==4.11.0`.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.11.0 to mitigate the vulnerabilities.
    2. Regularly update Gradio to the latest version to protect against newly discovered vulnerabilities.
rule: version < "4.11.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-6qm2-wpxq-7qh2
  - https://nvd.nist.gov/vuln/detail/CVE-2023-51449
  - https://github.com/gradio-app/gradio/commit/1b9d4234d6c25ef250d882c7b90e1f4039ed2d76
  - https://github.com/gradio-app/gradio/commit/7ba8c5da45b004edd12c0460be9222f5b5f5f055
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2023-249.yaml