info:
  name: gradio
  cve: CVE-2023-6572
  summary: Exposure of Sensitive Information to an Unauthorized Actor in Gradio
  details: |
    The vulnerability involves the exposure of sensitive information to unauthorized actors 
    due to improper handling in the GitHub repository gradio-app/gradio prior to the main 
    branch being active.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: |
    1. Upgrade to gradio>=4.14.0
    2. Review and adjust repository access controls to prevent unauthorized access
    3. Regularly audit the repository for any potential sensitive information leaks
rule: version >= "0" && version < "4.14.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6572
  - https://github.com/gradio-app/gradio/commit/5b5af1899dd98d63e1f9b48a93601c2db1f56520
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2023-255.yaml
  - https://huntr.com/bounties/21d2ff0c-d43a-4afd-bb4d-049ee8da5b5c