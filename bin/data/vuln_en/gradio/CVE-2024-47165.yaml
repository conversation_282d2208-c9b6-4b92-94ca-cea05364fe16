info:
  name: gradio
  cve: CVE-2024-47165
  summary: Gradio's CORS origin validation accepts the null origin
  details: |
    This vulnerability relates to **CORS origin validation accepting a null origin**. When a Gradio server is deployed locally, the `localhost_aliases` variable includes "null" as a valid origin. This allows attackers to make unauthorized requests from sandboxed iframes or other sources with a null origin, potentially leading to data theft, such as user authentication tokens or uploaded files. This impacts users running Gradio locally, especially those using basic authentication.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=5.0.0 to address this issue.
    2. As a workaround, manually modify the `localhost_aliases` list in your local Gradio deployment to exclude "null" as a valid origin.
rule: version >= "0" && version < "5.0.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-89v2-pqfv-c5r9
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47165
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-214.yaml