info:
  name: gradio
  cve: CVE-2024-48052
  summary: gradio Server Side Request Forgery vulnerability
  details: |
    In gradio <=4.42.0, the gr.DownloadButton function has a hidden server-side request forgery (SSRF) vulnerability. 
    The reason is that within the save_url_to_cache function, there are no restrictions on the URL, which allows access to local target resources. 
    This can lead to the download of local resources and sensitive information.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio >=4.43.0
    2. Implement URL validation in the save_url_to_cache function to restrict access to local resources
    3. Regularly update and patch gradio to mitigate future vulnerabilities
rule: version <= "4.42.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-48052
  - https://gist.github.com/AfterSnows/45ffc23797f9127e00755376cc610e12
  - https://github.com/gradio-app/gradio
  - https://rumbling-slice-eb0.notion.site/FULL-SSRF-in-gr-DownloadButton-in-gradio-app-gradio-870b21e0908b48cbafd914719ac1a4e6?pvs=4