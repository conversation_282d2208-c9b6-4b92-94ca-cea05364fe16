info:
  name: gradio
  cve: CVE-2024-8021
  summary: Open redirect vulnerability in Gradio allows malicious redirection
  details: |
    An open redirect vulnerability exists in the latest version of gradio-app/gradio.
    The vulnerability allows an attacker to redirect users to a malicious website by URL encoding.
    This can be exploited by sending a crafted request to the application, which results in a 302 redirect to an attacker-controlled site.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Monitor and filter incoming requests to prevent malicious URL encoding.
    2. Implement strict validation for redirection URLs.
    3. Regularly update Gradio to the latest version to benefit from security patches.
rule: version > "0" && version < "4.44.0" # Assuming 4.44.0 is the version where this vulnerability is fixed
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8021
  - https://huntr.com/bounties/adc23067-ec04-47ef-9265-afd452071888