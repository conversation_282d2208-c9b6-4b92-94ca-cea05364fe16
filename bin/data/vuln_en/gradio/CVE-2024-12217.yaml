info:
  name: gradio
  cve: CVE-2024-12217
  summary: Path traversal vulnerability in Gradio on Windows OS
  details: |
    A vulnerability exists in the gradio-app/gradio repository, version git 67e4044, that allows for path traversal on Windows OS.
    The blocked_path functionality, which is intended to disallow users from reading certain files, is flawed.
    Specifically, while the application correctly blocks access to paths like 'C:/tmp/secret.txt', it fails to block access when using NTFS Alternate Data Streams (ADS) syntax, such as 'C:/tmp/secret.txt::$DATA'.
    This flaw can lead to unauthorized reading of blocked file paths.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of gradio that addresses this vulnerability.
    2. Implement additional validation checks to ensure that all forms of path traversal attempts are blocked, including those using NTFS Alternate Data Streams (ADS) syntax.
    3. Regularly review and update the blocked_path functionality to cover any new or emerging path traversal techniques.
rule: version < "67e4044" # Assuming that the vulnerability is fixed in a commit after git 67e4044
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12217
  - https://huntr.com/bounties/0439bf3d-cb38-43a5-8314-0fadf85cc5a0