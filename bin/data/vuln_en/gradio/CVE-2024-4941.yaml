info:
  name: gradio
  cve: CVE-2024-4941
  summary: Local file inclusion in Gradio
  details: |
    A local file inclusion vulnerability exists in the JSON component of gradio-app/gradio, 
    specifically in version 4.25. The vulnerability arises from improper input validation 
    in the `postprocess()` function within `gradio/components/json_component.py`, where 
    a user-controlled string is parsed as JSON. If the parsed JSON object contains a 
    `path` key, the specified file is moved to a temporary directory, making it possible 
    to retrieve it later via the `/file=..` endpoint. This issue allows an attacker to 
    read files on the remote system, posing a significant security risk.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.31.3
    2. Review and enhance input validation in the `postprocess()` function
    3. Implement strict file path validation to prevent unauthorized file access
rule: version >= "0" && version < "4.31.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4941
  - https://github.com/gradio-app/gradio/commit/ee1e2942e0a1ae84a08a05464e41c8108a03fa9c
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-184.yaml
  - https://huntr.com/bounties/39889ce1-298d-4568-aecd-7ae40c2ca58e