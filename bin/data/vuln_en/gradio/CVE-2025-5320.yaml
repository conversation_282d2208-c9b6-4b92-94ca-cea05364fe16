info:
  name: gradio
  cve: CVE-2025-5320
  summary: Gradio CORS Origin Validation Bypass Vulnerability
  details: |
    A vulnerability classified as problematic has been found in gradio-app gradio up to 5.29.1.
    This affects the function is_valid_origin of the component CORS Handler. The manipulation
    of the argument localhost_aliases leads to origin validation error. It is possible to
    initiate the attack remotely. The complexity of an attack is rather high. The exploitability
    is told to be difficult.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:L/A:N
  severity: LOW
  security_advise: |
    1. Upgrade to gradio>=5.29.2
    2. Review and enforce strict origin validation in CORS Handler
    3. Monitor for any suspicious activity related to CORS
rule: version >= "5.0.0" && version <= "5.29.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-5320
  - https://gist.github.com/superboy-zjc/aa3dfa161d7b19d8a53ab4605792f2fe
  - https://gist.github.com/superboy-zjc/aa3dfa161d7b19d8a53ab4605792f2fe#proof-of-concept-poc
  - https://github.com/gradio-app/gradio
  - https://vuldb.com/?ctiid.310491
  - https://vuldb.com/?id.310491
  - https://vuldb.com/?submit.580250