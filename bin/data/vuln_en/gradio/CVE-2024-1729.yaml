info:
  name: gradio
  cve: CVE-2024-1729
  summary: Gradio apps vulnerable to timing attacks to guess password
  details: |
    This security issue relates to a timing attack that allows users of Gradio apps to potentially guess the password of password-protected Gradio apps. This relies on the fact that string comparisons in Python terminate early, as soon as there is a string mismatch. Because Gradio apps are, by default, not rate-limited, a user could brute-force millions of guesses to figure out the correct username and password.
  cvss: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=4.19.2
    2. Implement rate limiting to mitigate brute force attempts
    3. Monitor for unusual activity that may indicate a brute force attack
rule: version < "4.19.2"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-hmx6-r76c-85g9
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1729
  - https://github.com/gradio-app/gradio/commit/e329f1fd38935213fe0e73962e8cbd5d3af6e87b
  - https://github.com/gradio-app/gradio/releases/tag/gradio%404.19.2
  - https://huntr.com/bounties/f6a10a8d-f538-4cb7-9bb2-85d9f5708124