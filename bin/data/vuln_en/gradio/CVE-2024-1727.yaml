info:
  name: gradio
  cve: CVE-2024-1727
  summary: Gradio applications running locally vulnerable to 3rd party websites accessing routes and uploading files
  details: |
    This CVE covers the ability of 3rd party websites to access routes and upload files to users running Gradio applications locally. For example, a malicious website could include a script that uploads a large file to a locally running Gradio instance, resulting in unintended file uploads on the user's computer.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:N/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio version 4.19.2 or higher.
    2. Monitor for any suspicious activity related to file uploads.
    3. Regularly update Gradio to benefit from the latest security patches.
rule: version >= "0" && version < "4.19.2"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-48cq-79qq-6f7x
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1727
  - https://github.com/gradio-app/gradio/pull/7503
  - https://github.com/gradio-app/gradio/commit/84802ee6a4806c25287344dce581f9548a99834a
  - https://huntr.com/bounties/a94d55fb-0770-4cbe-9b20-97a978a2ffff