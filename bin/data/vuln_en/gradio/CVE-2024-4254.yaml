info:
  name: gradio
  cve: CVE-2024-4254
  summary: Secrets exfiltration vulnerability in gradio workflow
  details: |
    The 'deploy-website.yml' workflow in the gradio-app/gradio repository, specifically in the 'main' branch, is vulnerable to secrets exfiltration due to improper authorization. The vulnerability arises from the workflow's explicit checkout and execution of code from a fork, which is unsafe as it allows the running of untrusted code in an environment with access to push to the base repository and access secrets. This flaw could lead to the exfiltration of sensitive secrets such as GITHUB_TOKEN, HF_TOKEN, VERCEL_ORG_ID, VERCEL_PROJECT_ID, COMMENT_TOKEN, AWSACCESSKEYID, AWSSECRETKEY, and VERCEL_TOKEN.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:L/A:N
  severity: HIGH
  security_advise: |
    1. Review and update the 'deploy-website.yml' workflow to ensure it does not execute code from untrusted forks.
    2. Implement strict authorization checks for any workflow that handles sensitive information.
    3. Regularly audit and update dependencies to the latest secure versions.
rule: version > "0" && version < "4.44.0" # Placeholder rule as specific version affected is not provided
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4254
  - https://huntr.com/bounties/59873fbd-5698-4ec3-87f9-5d70c6055d01