info:
  name: gradio
  cve: CVE-2024-0964
  summary: Gradio Path Traversal vulnerability
  details: |
    A local file include could be remotely triggered in Gradio due to a vulnerable user-supplied JSON value in an API request.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.9.0
    2. Review and sanitize all user-supplied input to prevent path traversal attacks
    3. Implement additional security measures such as input validation and output encoding
rule: version >= "0" && version < "4.9.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0964
  - https://github.com/gradio-app/gradio/commit/d76bcaaaf0734aaf49a680f94ea9d4d22a602e70
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/25e25501-5918-429c-8541-88832dfd3741