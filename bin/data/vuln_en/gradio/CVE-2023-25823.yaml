info:
  name: gradio
  cve: CVE-2023-25823
  summary: Update share links to use FRP instead of SSH tunneling
  details: |
    This vulnerability affects anyone using Gradio's share links with versions older than 3.13.1. In these older versions, a private SSH key is sent to any user that connects to the Gradio machine, potentially allowing access to other users' shared Gradio demos and enabling further exploits based on the level of access/exposure provided by the Gradio app.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>=3.19.1 where the FRP solution has been properly tested.
    2. Ensure that any shared Gradio apps are updated to the latest version to mitigate the risk of unauthorized access.
rule: version > "0" && version < "3.13.1"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-3x5j-9vwr-8rr5
  - https://nvd.nist.gov/vuln/detail/CVE-2023-25823
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2023-16.yaml