info:
  name: gradio
  cve: CVE-2024-4325
  summary: Server-Side Request Forgery in gradio
  details: |
    A Server-Side Request Forgery (SSRF) vulnerability exists in the gradio-app/gradio and was discovered in version 4.21.0, specifically within the `/queue/join` endpoint and the `save_url_to_cache` function. The vulnerability arises when the `path` value, obtained from the user and expected to be a URL, is used to make an HTTP request without sufficient validation checks. This flaw allows an attacker to send crafted requests that could lead to unauthorized access to the local network or the AWS metadata endpoint, thereby compromising the security of internal servers.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.36.1
    2. Review and enhance input validation for URLs within the application
    3. Implement network restrictions to limit outbound requests from the application
rule: version >= "0" && version < "4.36.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4325
  - https://github.com/gradio-app/gradio/pull/8301
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/b34f084b-7d14-4f00-bc10-048a3a5aaf88