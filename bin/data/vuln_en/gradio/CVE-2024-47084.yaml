info:
  name: gradio
  cve: CVE-2024-47084
  summary: Gradio's CORS origin validation is not performed when the request has a cookie
  details: |
    This vulnerability is related to **CORS origin validation**, where the Gradio server fails to validate the request origin when a cookie is present. This allows an attacker’s website to make unauthorized requests to a local Gradio server. Potentially, attackers can upload files, steal authentication tokens, and access user data if the victim visits a malicious website while logged into Gradio. This impacts users who have deployed Gradio locally and use basic authentication.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.44 to address this issue.
    2. As a workaround, manually enforce stricter CORS origin validation by modifying the `CustomCORSMiddleware` class in your local Gradio server code. Specifically, bypass the condition that skips CORS validation for requests containing cookies to prevent potential exploitation.
rule: version > "0" && version < "4.44.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-3c67-5hwx-f6wx
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47084
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-196.yaml