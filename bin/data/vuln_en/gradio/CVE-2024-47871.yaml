info:
  name: gradio
  cve: CVE-2024-47871
  summary: Gradio uses insecure communication between the FRP client and server
  details: |
    This vulnerability involves **insecure communication** between the FRP (Fast Reverse Proxy) client and server when Gradio's `share=True` option is used. HTTPS is not enforced on the connection, allowing attackers to intercept and read files uploaded to the Gradio server, as well as modify responses or data sent between the client and server. This impacts users who are sharing Gradio demos publicly over the internet using `share=True` without proper encryption, exposing sensitive data to potential eavesdroppers.
  cvss: CVSS:3.1/AV:A/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=5.0.0 to address this issue.
    2. As a workaround, avoid using `share=True` in production environments.
    3. Host Gradio applications on servers with HTTPS enabled to ensure secure communication.
rule: version < "5.0.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-279j-x4gx-hfrh
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47871
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-219.yaml