info:
  name: gradio
  cve: CVE-2024-47867
  summary: Gradio lacks integrity checking on the downloaded FRP client
  details: |
    ### Impact
    This vulnerability is a **lack of integrity check** on the downloaded FRP client, which could potentially allow attackers to introduce malicious code. If an attacker gains access to the remote URL from which the FRP client is downloaded, they could modify the binary without detection, as the Gradio server does not verify the file's checksum or signature.
    **Who is impacted?**  
    Any users utilizing the Gradio server's sharing mechanism that downloads the FRP client could be affected by this vulnerability, especially those relying on the executable binary for secure data tunneling.
    ### Patches
    Yes, please upgrade to `gradio>=5.0`, which includes a fix to verify the integrity of the downloaded binary.
    ### Workarounds
    There is no direct workaround for this issue without upgrading. However, users can manually validate the integrity of the downloaded FRP client by implementing checksum or signature verification in their own environment to ensure the binary hasn't been tampered with.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=5.0
    2. Implement checksum or signature verification for the downloaded FRP client if upgrading is not immediately feasible
rule: version >= "0" && version < "5.0.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-8c87-gvhj-xm8m
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47867
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-216.yaml