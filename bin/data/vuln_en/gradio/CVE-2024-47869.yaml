info:
  name: gradio
  cve: CVE-2024-47869
  summary: Gradio performs a non-constant-time comparison when comparing hashes
  details: |
    ### Impact
    **What kind of vulnerability is it? Who is impacted?**
    This vulnerability involves a **timing attack** in the way Gradio compares hashes for the `analytics_dashboard` function. Since the comparison is not done in constant time, an attacker could exploit this by measuring the response time of different requests to infer the correct hash byte-by-byte. This can lead to unauthorized access to the analytics dashboard, especially if the attacker can repeatedly query the system with different keys.
    ### Patches
    Yes, please upgrade to `gradio>4.44` to mitigate this issue.
    ### Workarounds
    **Is there a way for users to fix or remediate the vulnerability without upgrading?**
    To mitigate the risk before applying the patch, developers can manually patch the `analytics_dashboard` dashboard to use a **constant-time comparison** function for comparing sensitive values, such as hashes. Alternatively, access to the analytics dashboard can be disabled.
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gradio>4.44
    2. Manually patch the `analytics_dashboard` to use a constant-time comparison function for hashes
    3. Disable access to the analytics dashboard
rule: version > "0" && version < "4.44.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-j757-pf57-f8r4
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47869
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2024-199.yaml