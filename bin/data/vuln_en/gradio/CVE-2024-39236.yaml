info:
  name: gradio
  cve: CVE-2024-39236
  summary: Gradio contains a code injection vulnerability via /gradio/component_meta.py
  details: |
    Gradio v4.36.1 was discovered to contain a code injection vulnerability via the component /gradio/component_meta.py. This vulnerability is triggered via a crafted input.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to gradio>=4.36.2
    2. Review and sanitize all user inputs to prevent code injection
    3. Monitor for any suspicious activity post-upgrade
rule: version == "4.36.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-39236
  - https://github.com/gradio-app/gradio/issues/8853
  - https://github.com/Aaron911/PoC/blob/main/Gradio.md
  - https://github.com/advisories/GHSA-9v2f-6vcg-3hgv