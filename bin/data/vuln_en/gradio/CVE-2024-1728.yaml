info:
  name: gradio
  cve: CVE-2024-1728
  summary: Gradio Local File Inclusion vulnerability
  details: |
    gradio-app/gradio is vulnerable to a local file inclusion vulnerability due to improper validation of user-supplied input in the UploadButton component. Attackers can exploit this vulnerability to read arbitrary files on the filesystem, such as private SSH keys, by manipulating the file path in the request to the `/queue/join` endpoint. This issue could potentially lead to remote code execution. The vulnerability is present in the handling of file upload paths, allowing attackers to redirect file uploads to unintended locations on the server.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to gradio>=4.19.2
    2. Validate and sanitize all user-supplied input in the UploadButton component
    3. Implement strict file path validation to prevent redirection to unintended locations
rule: version > "0" && version < "4.19.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1728
  - https://github.com/gradio-app/gradio/commit/16fbe9cd0cffa9f2a824a0165beb43446114eec7
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/9bb33b71-7995-425d-91cc-2c2a2f2a068a