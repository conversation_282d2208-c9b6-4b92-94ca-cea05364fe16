info:
  name: ollama
  cve: CVE-2024-8063
  summary: Divide by zero vulnerability in ollama/ollama version v0.3.3
  details: |
    A divide by zero vulnerability exists in ollama/ollama version v0.3.3. 
    The vulnerability occurs when importing GGUF models with a crafted type for `block_count` 
    in the Modelfile. This can lead to a denial of service (DoS) condition when the server 
    processes the model, causing it to crash.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama/ollama version v0.3.4 or higher
    2. Implement input validation for `block_count` in Modelfile to prevent crafted types
    3. Monitor server logs for unusual activity that may indicate an attack attempt
rule: version == "0.3.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8063
  - https://huntr.com/bounties/fd8e1ed6-21d2-4c9e-8395-2098f11b7db9