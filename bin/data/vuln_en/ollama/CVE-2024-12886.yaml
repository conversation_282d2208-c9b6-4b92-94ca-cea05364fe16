info:
  name: ollama
  cve: CVE-2024-12886
  summary: Out-Of-Memory (OOM) vulnerability in `ollama` server
  details: |
    An Out-Of-Memory (OOM) vulnerability exists in the `ollama` server version 0.3.14. 
    This vulnerability can be triggered when a malicious API server responds with a gzip bomb HTTP response, 
    leading to the `ollama` server crashing. The vulnerability is present in the `makeRequestWithRetry` 
    and `getAuthorizationToken` functions, which use `io.ReadAll` to read the response body. 
    This can result in excessive memory usage and a Denial of Service (DoS) condition.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama >= 0.3.15
    2. Implement input validation to prevent gzip bomb responses
    3. Monitor server memory usage for abnormal spikes
rule: version < "0.3.15"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12886
  - https://huntr.com/bounties/f115fe52-58af-4844-ad29-b1c25f7245df