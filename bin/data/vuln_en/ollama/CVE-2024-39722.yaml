info:
  name: ollama
  cve: CVE-2024-39722
  summary: Path traversal vulnerability in Ollama before 0.1.46
  details: |
    An issue was discovered in Ollama before 0.1.46. It exposes which files exist on the server on which it is deployed via path traversal in the api/push route.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to Ollama version 0.1.46 or later.
    2. Implement strict input validation to prevent path traversal attacks.
    3. Regularly update and patch the Ollama application to mitigate future vulnerabilities.
rule: version < "0.1.46"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-39722
  - https://oligosecurity.webflow.io/blog/more-models-more-probllms
  - https://www.oligo.security/blog/more-models-more-probllms