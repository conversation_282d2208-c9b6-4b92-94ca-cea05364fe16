info:
  name: ollama
  cve: CVE-2024-39720
  summary: Ollama Out-of-bounds Read
  details: |
    An issue was discovered in Ollama before 0.1.46. An attacker can use two HTTP requests to upload a malformed GGUF file containing just 4 bytes starting with the GGUF custom magic header. By leveraging a custom Modelfile that includes a FROM statement pointing to the attacker-controlled blob file, the attacker can crash the application through the CreateModel route, leading to a segmentation fault (signal SIGSEGV: segmentation violation).
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama>=0.1.46
    2. Implement input validation for GGUF files to prevent malformed file uploads
    3. Monitor for any unusual activity related to model creation routes
rule: version < "0.1.46"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-39720
  - https://github.com/ollama/ollama
  - https://github.com/ollama/ollama/compare/v0.1.45...v0.1.46#diff-782c2737eecfa83b7cb46a77c8bdaf40023e7067baccd4f806ac5517b4563131L417
  - https://oligo.security/blog/more-models-more-probllms
  - https://oligosecurity.webflow.io/blog/more-models-more-probllms
  - https://pkg.go.dev/vuln/GO-2024-3245