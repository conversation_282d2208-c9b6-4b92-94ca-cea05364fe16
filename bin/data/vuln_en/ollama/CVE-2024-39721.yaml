info:
  name: ollama
  cve: CVE-2024-39721
  summary: Infinite goroutine due to uncontrolled file path in CreateModelHandler
  details: |
    An issue was discovered in Ollama before 0.1.34. The CreateModelHandler function uses os.Open to read a file until completion. 
    The req.Path parameter is user-controlled and can be set to /dev/random, which is blocking, causing the goroutine to run infinitely 
    (even after the HTTP request is aborted by the client).
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama>=0.1.34
    2. Implement input validation for the req.Path parameter to prevent usage of blocking file paths like /dev/random
rule: version < "0.1.34"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-39721
  - https://github.com/ollama/ollama/blob/9164b0161bcb24e543cba835a8863b80af2c0c21/server/routes.go#L557
  - https://github.com/ollama/ollama/blob/adeb40eaf29039b8964425f69a9315f9f1694ba8/server/routes.go#L536
  - https://oligosecurity.webflow.io/blog/more-models-more-probllms
  - https://www.oligo.security/blog/more-models-more-probllms