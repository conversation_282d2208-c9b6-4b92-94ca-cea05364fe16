info:
  name: ollama
  cve: CVE-2025-0315
  summary: Vulnerability in ollama allows DoS via customized GGUF model file upload
  details: |
    A vulnerability in ollama/ollama <=0.3.14 allows a malicious user to create a customized GGUF model file, upload it to the Ollama server, and create it. This can cause the server to allocate unlimited memory, leading to a Denial of Service (DoS) attack.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama >=0.3.15
    2. Implement server-side validation for GGUF model files to prevent unlimited memory allocation
    3. Monitor server resource usage for unusual spikes that could indicate an attack
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0315
  - https://huntr.com/bounties/da414d29-b55a-496f-b135-17e0fcec67bc