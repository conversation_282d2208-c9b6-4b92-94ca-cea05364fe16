info:
  name: ollama
  cve: CVE-2024-45436
  summary: Ollama can extract members of a ZIP archive outside of the parent directory
  details: |
    The vulnerability in `extractFromZipFile` function in `model.go` of Ollama versions before 0.1.47 allows extraction of ZIP archive members outside of the intended parent directory.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama version 0.1.47 or later.
    2. Review and test ZIP extraction functionality to ensure it operates within expected directory boundaries.
rule: version < "0.1.47"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-45436
  - https://github.com/ollama/ollama/pull/5314
  - https://github.com/ollama/ollama/commit/123a722a6f541e300bc8e34297ac378ebe23f527
  - https://github.com/ollama/ollama
  - https://github.com/ollama/ollama/compare/v0.1.46...v0.1.47