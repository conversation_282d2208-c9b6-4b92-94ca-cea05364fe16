info:
  name: ollama
  cve: CVE-2024-39719
  summary: File existence disclosure vulnerability in Ollama
  details: |
    An issue was discovered in Ollama through 0.3.14. File existence disclosure can occur via api/create.
    When calling the CreateModel route with a path parameter that does not exist, it reflects the 
    \"File does not exist\" error message to the attacker, providing a primitive for file existence 
    on the server.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to Ollama version 0.3.15 or higher.
    2. Implement custom error handling to avoid leaking file existence information.
    3. Regularly review and update the application to patch known vulnerabilities.
rule: version < "0.3.15"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-39719
  - https://oligosecurity.webflow.io/blog/more-models-more-probllms
  - https://www.oligo.security/blog/more-models-more-probllms