info:
  name: ollama
  cve: CVE-2024-12055
  summary: Vulnerability in Ollama versions <=0.3.14 allows DoS via malicious gguf model file upload
  details: |
    A vulnerability in Ollama versions <=0.3.14 allows a malicious user to create a customized gguf model file that can be uploaded to the public Ollama server.
    When the server processes this malicious model, it crashes, leading to a Denial of Service (DoS) attack. The root cause of the issue is an out-of-bounds read in the gguf.go file.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to Ollama version >0.3.14
    2. Implement server-side validation for uploaded model files to prevent malicious content
    3. Monitor server logs for unusual activity that may indicate an attack
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12055
  - https://huntr.com/bounties/7b111d55-8215-4727-8807-c5ed4cf1bfbe