info:
  name: ollama
  cve: CVE-2024-7773
  summary: Remote Code Execution (RCE) in ollama due to ZipSlip vulnerability
  details: |
    A vulnerability in ollama/ollama version 0.1.37 allows for remote code execution (RCE) due to improper input validation in the handling of zip files. 
    The vulnerability, known as ZipSlip, occurs in the parseFromZipFile function in server/model.go. 
    The code does not check for directory traversal sequences (../) in file names within the zip archive, allowing an attacker to write arbitrary files to the file system.
    This can be exploited to create files such as /etc/ld.so.preload and a malicious shared library, leading to RCE.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to ollama >= 0.1.38
    2. Implement strict input validation for file names within zip archives
    3. Regularly update and patch all software dependencies
rule: version < "0.1.38"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7773
  - https://github.com/ollama/ollama/commit/123a722a6f541e300bc8e34297ac378ebe23f527
  - https://huntr.com/bounties/aeb82e05-484f-4431-9ede-25a3478d8dbb