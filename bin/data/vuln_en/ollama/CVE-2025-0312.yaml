info:
  name: ollama
  cve: CVE-2025-0312
  summary: Vulnerability in ollama allows DoS via null pointer dereference in GGUF model files
  details: |
    A vulnerability in ollama/ollama versions <=0.3.14 allows a malicious user to create a customized GGUF model file that, when uploaded and created on the Ollama server, can cause a crash due to an unchecked null pointer dereference. This can lead to a Denial of Service (DoS) attack via remote network.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama >=0.3.15
    2. Implement input validation for GGUF model files to prevent null pointer dereferences
    3. Monitor server logs for unusual activity that may indicate an attack
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0312
  - https://huntr.com/bounties/522c87b6-a7ac-41b2-84f3-62fd58921f21