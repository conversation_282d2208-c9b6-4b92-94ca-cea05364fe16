info:
  name: ollama
  cve: CVE-2025-0313
  summary: Vulnerability in ollama allows DoS attack via GGUF model
  details: |
    A vulnerability in ollama/ollama versions <=0.3.14 allows a malicious user to create a GGUF model that can cause a denial of service (DoS) attack. 
    The vulnerability is due to improper validation of array index bounds in the GGUF model handling code, which can be exploited via a remote network.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama >=0.3.15
    2. Implement proper array index bounds validation in GGUF model handling code
    3. Monitor for unusual network activity that may indicate an attack
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0313
  - https://huntr.com/bounties/450c90f9-bc02-4560-afd4-d0aa057ac82c