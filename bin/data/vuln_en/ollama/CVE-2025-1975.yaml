info:
  name: ollama
  cve: CVE-2025-1975
  summary: Ollama Server Vulnerable to Denial of Service (DoS) Attack
  details: |
    A vulnerability in the Ollama server version 0.5.11 allows a malicious user to cause a Denial of Service (DoS) attack by customizing the manifest content and spoofing a service. This is due to improper validation of array index access when downloading a model via the /api/pull endpoint, which can lead to a server crash.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama >= 0.5.12
    2. Implement proper validation for array index access in the /api/pull endpoint
    3. Monitor server logs for unusual activity that may indicate a DoS attempt
rule: version >= "0" && version < "0.5.12"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1975
  - https://github.com/ollama/ollama
  - https://huntr.com/bounties/921ba5d4-f1d0-4c66-9764-4f72dffe7acd