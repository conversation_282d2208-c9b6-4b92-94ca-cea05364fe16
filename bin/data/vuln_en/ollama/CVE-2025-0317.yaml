info:
  name: ollama
  cve: CVE-2025-0317
  summary: Vulnerability in ollama allows DoS via malicious GGUF model upload
  details: |
    A vulnerability in ollama/ollama versions <=0.3.14 allows a malicious user to upload and create a customized GGUF model file on the Ollama server. This can lead to a division by zero error in the ggufPadding function, causing the server to crash and resulting in a Denial of Service (DoS) attack.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama >=0.3.15
    2. Implement server-side validation for GGUF model files to prevent malicious uploads
    3. Monitor server logs for unusual activity that may indicate an attack
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0317
  - https://huntr.com/bounties/a9951bca-9bd8-49b2-b143-4cd4219f9fa0