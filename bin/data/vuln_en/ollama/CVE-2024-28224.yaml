info:
  name: ollama
  cve: CVE-2024-28224
  summary: Ollama DNS rebinding vulnerability
  details: |
    Ollama before 0.1.29 has a DNS rebinding vulnerability that can inadvertently allow remote access to the full API, thereby letting an unauthorized user chat with a large language model, delete a model, or cause a denial of service (resource exhaustion).
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ollama>=0.1.29
    2. Implement additional security measures to prevent DNS rebinding attacks
    3. Monitor for unauthorized API access and resource exhaustion
rule: version < "0.1.29"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-28224
  - https://github.com/ollama/ollama
  - https://github.com/ollama/ollama/releases
  - https://pkg.go.dev/vuln/GO-2024-2699
  - https://research.nccgroup.com/2024/04/08/technical-advisory-ollama-dns-rebinding-attack-cve-2024-28224
  - https://www.nccgroup.trust/us/our-research/?research=Technical+advisories