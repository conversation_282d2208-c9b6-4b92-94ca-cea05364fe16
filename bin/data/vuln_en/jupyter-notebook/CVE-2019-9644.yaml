info:
  name: jupyter-notebook
  cve: CVE-2019-9644
  summary: Improper Neutralization of Input During Web Page Generation in Jupyter Notebook
  details: |
    An XSSI (cross-site inclusion) vulnerability in Jupyter Notebook before 5.7.6 allows inclusion of resources on malicious pages when visited by users who are authenticated with a Jupyter server. Access to the content of resources has been demonstrated with Internet Explorer through capturing of error messages, though not reproduced with other browsers. This occurs because Internet Explorer's error messages can include the content of any invalid JavaScript that was encountered.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to jupyter-notebook>=5.7.6
    2. Ensure that all users are using browsers that do not include error messages in JavaScript execution contexts
    3. Regularly update and patch Jupyter Notebook to the latest version
rule: version < "5.7.6"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2019-9644
  - https://github.com/jupyter/notebook
  - https://github.com/jupyter/notebook/compare/f3f00df...05aa4b2
  - https://github.com/pypa/advisory-database/tree/main/vulns/notebook/PYSEC-2019-159.yaml
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/UP5RLEES2JBBNSNLBR65XM6PCD4EMF7D
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/VMDPJBVXOVO6LYGAT46VZNHH6JKSCURO