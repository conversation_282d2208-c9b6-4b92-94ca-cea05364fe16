info:
  name: langchain
  cve: CVE-2024-28088
  summary: LangChain directory traversal vulnerability
  details: |
    LangChain through 0.1.10 allows ../ directory traversal by an actor who is able to control the final part of the path parameter in a load_chain call. This bypasses the intended behavior of loading configurations only from the hwchase17/langchain-hub GitHub repository. The outcome can be disclosure of an API key for a large language model online service, or remote code execution.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: LOW
  security_advise: |
    1. Upgrade to langchain>=0.0.339 or langchain-core>=0.1.30
    2. Review and enforce strict path validation in load_chain calls
    3. Monitor for unauthorized access attempts to API keys and sensitive data
rule: version < "0.0.339" || version < "0.1.30"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-28088
  - https://github.com/langchain-ai/langchain/pull/18600
  - https://github.com/langchain-ai/langchain/commit/e1924b3e93d513ca950c72f8e80e1c133749fba5
  - https://github.com/PinkDraconian/PoC-Langchain-RCE/blob/main/README.md
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/blob/f96dd57501131840b713ed7c2e86cbf1ddc2761f/libs/core/langchain_core/utils/loading.py
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain-core/PYSEC-2024-45.yaml
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-43.yaml