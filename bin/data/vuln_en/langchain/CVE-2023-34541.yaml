info:
  name: langchain
  cve: CVE-2023-34541
  summary: Langchain vulnerable to arbitrary code execution
  details: |
    Langchain 0.0.171 is vulnerable to Arbitrary code execution in `load_prompt`.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain>=0.0.247
    2. Review and patch the `load_prompt` function to prevent arbitrary code execution
rule: version >= "0" && version < "0.0.247"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-34541
  - https://github.com/langchain-ai/langchain/issues/4849
  - https://github.com/langchain-ai/langchain/issues/4849#issuecomment-1697896569
  - https://github.com/langchain-ai/langchain/pull/8425
  - https://github.com/langchain-ai/langchain/commit/fab24457bcf8ede882abd11419769c92bc4e7751
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-92.yaml