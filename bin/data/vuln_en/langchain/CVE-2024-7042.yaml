info:
  name: langchain
  cve: CVE-2024-7042
  summary: "@langchain/community SQL Injection vulnerability"
  details: |
    A vulnerability in the GraphCypherQAChain class of langchain-ai/langchainjs versions 0.2.5 and all versions with this class allows for prompt injection, leading to SQL injection. This vulnerability permits unauthorized data manipulation, data exfiltration, denial of service (DoS) by deleting all data, breaches in multi-tenant security environments, and data integrity issues. Attackers can create, update, or delete nodes and relationships without proper authorization, extract sensitive data, disrupt services, access data across different tenants, and compromise the integrity of the database.
  cvss: CVSS:3.0/AV:L/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to @langchain/community version 0.3.3 or higher.
    2. Review and patch any custom implementations of the GraphCypherQAChain class to prevent SQL injection.
    3. Implement input validation and sanitization to ensure that only authorized and expected data is processed.
rule: version >= "0" && version < "0.3.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7042
  - https://github.com/langchain-ai/langchainjs/commit/615b9d9ab30a2d23a2f95fb8d7acfdf4b41ad7a6
  - https://github.com/langchain-ai/langchainjs
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-114.yaml
  - https://huntr.com/bounties/b612defb-1104-4fff-9fef-001ab07c7b2d