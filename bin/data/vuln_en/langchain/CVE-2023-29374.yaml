info:
  name: langchain
  cve: CVE-2023-29374
  summary: <PERSON><PERSON><PERSON><PERSON> vulnerable to code injection
  details: |
    In LangChain through 0.0.131, the `LLMMathChain` chain allows prompt injection attacks 
    that can execute arbitrary code via the Python `exec()` method.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain>=0.0.132
    2. Review and patch any custom chain implementations to prevent prompt injection
    3. Implement input validation to sanitize user inputs
rule: version < "0.0.132"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-29374
  - https://github.com/hwchase17/langchain/issues/1026
  - https://github.com/hwchase17/langchain/issues/814
  - https://github.com/hwchase17/langchain/pull/1119
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-18.yaml
  - https://twitter.com/rharang/status/1641899743608463365/photo/1