info:
  name: langchain
  cve: CVE-2024-0243
  summary: langchain Server-Side Request Forgery vulnerability
  details: |
    With the following crawler configuration:
    ```python
    from bs4 import BeautifulSoup as Soup
    url = "https://example.com"
    loader = RecursiveUrlLoader(
        url=url, max_depth=2, extractor=lambda x: Soup(x, "html.parser").text 
    )
    docs = loader.load()
    ```
    An attacker in control of the contents of `https://example.com` could place a malicious HTML file in there with links like "https://example.completely.different/my_file.html" and the crawler would proceed to download that file as well even though `prevent_outside=True`.
  cvss: CVSS:3.0/AV:L/AC:H/PR:H/UI:R/S:C/C:L/I:L/A:N
  severity: LOW
  security_advise: |
    1. Upgrade to langchain >= 0.1.0
    2. Review and adjust crawler configurations to prevent outside requests when `prevent_outside` is set to True
    3. Monitor and validate the sources of data being crawled to ensure they are trusted
rule: version < "0.1.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0243
  - https://github.com/langchain-ai/langchain/pull/15559
  - https://github.com/langchain-ai/langchain/commit/bf0b3cc0b5ade1fb95a5b1b6fa260e99064c2e22
  - https://github.com/langchain-ai/langchain/blob/bf0b3cc0b5ade1fb95a5b1b6fa260e99064c2e22/libs/community/langchain_community/document_loaders/recursive_url_loader.py#L51-L51
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain-exa/PYSEC-2024-235.yaml
  - https://huntr.com/bounties/370904e7-10ac-40a4-a8d4-e2d16e1ca861