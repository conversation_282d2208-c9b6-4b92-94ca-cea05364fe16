info:
  name: langchain
  cve: CVE-2023-38896
  summary: <PERSON><PERSON><PERSON><PERSON> vulnerable to arbitrary code execution
  details: |
    An issue in Harrison Chase langchain before version 0.0.236 allows a remote attacker to execute arbitrary code via the `from_math_prompt` and `from_colored_object_prompt` functions.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain>=0.0.236
    2. Review and update any usage of `from_math_prompt` and `from_colored_object_prompt` functions to ensure they are not vulnerable to code injection
    3. Monitor for any further updates or patches released by the maintainers
rule: version < "0.0.236"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-38896
  - https://github.com/hwchase17/langchain/issues/5872
  - https://github.com/hwchase17/langchain/pull/6003
  - https://github.com/langchain-ai/langchain/commit/8ba9835b925473655914f63822775679e03ea137
  - https://github.com/langchain-ai/langchain/commit/e294ba475a355feb95003ed8f1a2b99942509a9e
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-146.yaml
  - https://twitter.com/llm_sec/status/1668711587287375876