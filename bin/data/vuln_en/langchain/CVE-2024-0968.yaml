info:
  name: langchain
  cve: CVE-2024-0968
  summary: Cross-site Scripting (XSS) - DOM in GitHub repository langchain-ai/chat-langchain prior to 0.0.0.
  details: |
    The vulnerability is a Cross-site Scripting (XSS) issue affecting the DOM in the GitHub repository langchain-ai/chat-langchain versions prior to 0.0.0. This allows attackers to inject malicious scripts into web pages viewed by other users.
  cvss: CVSS:3.0/AV:N/AC:H/PR:L/UI:R/S:U/C:L/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Update to the latest version of langchain-ai/chat-langchain.
    2. Review and patch any known XSS vulnerabilities in the codebase.
    3. Implement strict input validation to prevent malicious script injection.
rule: version < "0.0.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0968
  - https://github.com/langchain-ai/chat-langchain/commit/e13db53cba2a48e4e26d103fd51598856f6bdd33
  - https://huntr.com/bounties/566033b9-df20-4928-b4aa-5cd4c3ca1561