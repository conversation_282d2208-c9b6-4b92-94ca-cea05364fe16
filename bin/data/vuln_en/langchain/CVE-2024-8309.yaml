info:
  name: langchain
  cve: CVE-2024-8309
  summary: Langchain SQL Injection vulnerability
  details: |
    A vulnerability in the GraphCypherQAChain class of langchain-ai/langchain version 0.2.5 allows for SQL injection through prompt injection. 
    This can lead to unauthorized data manipulation, data exfiltration, denial of service (DoS), breaches in multi-tenant security environments, and data integrity issues.
  cvss: CVSS:3.0/AV:L/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to langchain-community>=0.2.19 or langchain>=0.2.0
    2. Review and sanitize all user inputs to prevent SQL injection
    3. Implement least privilege access controls for database operations
rule: (version >= "0.2.0" && version < "0.2.19") || (version >= "0" && version < "0.2.0")
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8309
  - https://github.com/langchain-ai/langchain/commit/64c317eba05fbac0c6a6fc5aa192bc0d7130972e
  - https://github.com/langchain-ai/langchain/commit/c2a3021bb0c5f54649d380b42a0684ca5778c255
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-115.yaml
  - https://huntr.com/bounties/8f4ad910-7fdc-4089-8f0a-b5df5f32e7c5