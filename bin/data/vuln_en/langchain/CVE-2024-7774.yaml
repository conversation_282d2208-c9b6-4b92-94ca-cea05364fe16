info:
  name: langchain
  cve: CVE-2024-7774
  summary: Langchain Path Traversal vulnerability
  details: |
    A path traversal vulnerability exists in the `getFullPath` method of langchain-ai/langchainjs version 0.2.5. 
    This vulnerability allows attackers to save files anywhere in the filesystem, overwrite existing text files, 
    read `.txt` files, and delete files. The vulnerability is exploited through the `setFileContent`, 
    `getParsedFile`, and `mdelete` methods, which do not properly sanitize user input.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to langchainjs version 0.2.19 or higher.
    2. Review and sanitize all user inputs to prevent path traversal attacks.
    3. Implement strict file access controls to limit file operations.
rule: version >= "0" && version < "0.2.19"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7774
  - https://github.com/langchain-ai/langchainjs/commit/a0fad77d6b569e5872bd4a9d33be0c0785e538a9
  - https://github.com/langchain-ai/langchainjs
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-111.yaml
  - https://huntr.com/bounties/8fe40685-b714-4191-af7a-3de5e5628cee