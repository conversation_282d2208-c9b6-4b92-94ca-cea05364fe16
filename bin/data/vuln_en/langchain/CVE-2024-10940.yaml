info:
  name: langchain
  cve: CVE-2024-10940
  summary: Unauthorized file read vulnerability in langchain-core
  details: |
    A vulnerability in langchain-core versions >=0.1.17,<0.1.53, >=0.2.0,<0.2.43, and >=0.3.0,<0.3.15 allows unauthorized users to read arbitrary files from the host file system. The issue arises from the ability to create langchain_core.prompts.ImagePromptTemplate's (and by extension langchain_core.prompts.ChatPromptTemplate's) with input variables that can read any user-specified path from the server file system. If the outputs of these prompt templates are exposed to the user, either directly or through downstream model outputs, it can lead to the exposure of sensitive information.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to langchain-core >=0.1.53, >=0.2.43, or >=0.3.15
    2. Review and restrict the use of ImagePromptTemplate and ChatPromptTemplate to prevent exposure of file system paths
    3. Implement input validation to ensure that user-specified paths cannot be used to read arbitrary files
rule: (version >= "0.1.17" && version < "0.1.53") || (version >= "0.2.0" && version < "0.2.43") || (version >= "0.3.0" && version < "0.3.15")
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10940
  - https://github.com/langchain-ai/langchain/commit/c1e742347f9701aadba8920e4d1f79a636e50b68
  - https://huntr.com/bounties/be1ee1cb-2147-4ff4-a57b-b6045271cf27