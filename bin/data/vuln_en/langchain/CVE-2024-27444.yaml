info:
  name: langchain
  cve: CVE-2024-27444
  summary: LangChain Experimental vulnerable to arbitrary code execution
  details: |
    langchain_experimental (aka LangChain Experimental) before 0.0.52, part of LangChain before 0.1.8,
    allows an attacker to bypass the CVE-2023-44467 fix and execute arbitrary code via the 
    `__import__`, `__subclasses__`, `__builtins__`, `__globals__`, `__getattribute__`, 
    `__bases__`, `__mro__`, or `__base__` attribute in Python code. These are not prohibited by 
    `pal_chain/base.py`.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain-experimental>=0.0.52 and Lang<PERSON><PERSON><PERSON>>=0.1.8
    2. Review and patch any custom code that may utilize the vulnerable attributes
    3. Monitor for any suspicious activity post-upgrade
rule: version < "0.0.52" || version < "0.1.8"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-27444
  - https://github.com/langchain-ai/langchain/commit/de9a6cdf163ed00adaf2e559203ed0a9ca2f1de7
  - https://github.com/langchain-ai/langchain