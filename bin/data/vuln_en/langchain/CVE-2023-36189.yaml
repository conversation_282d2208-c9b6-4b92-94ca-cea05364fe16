info:
  name: langchain
  cve: CVE-2023-36189
  summary: langchain SQL Injection vulnerability
  details: |
    SQL injection vulnerability in langchain allows a remote attacker to obtain sensitive information via the SQLDatabaseChain component.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to langchain >= 0.0.247
    2. Review and sanitize all user inputs to prevent SQL injection attacks
    3. Implement least privilege access controls for database operations
rule: version < "0.0.247"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-36189
  - https://github.com/hwchase17/langchain/issues/5923
  - https://github.com/langchain-ai/langchain/issues/5923
  - https://github.com/langchain-ai/langchain/issues/5923#issuecomment-1696053841
  - https://github.com/hwchase17/langchain/pull/6051
  - https://github.com/langchain-ai/langchain/pull/8425
  - https://github.com/langchain-ai/langchain/commit/fab24457bcf8ede882abd11419769c92bc4e7751
  - https://gist.github.com/rharang/9c58d39db8c01db5b7c888e467c0533f
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-110.yaml