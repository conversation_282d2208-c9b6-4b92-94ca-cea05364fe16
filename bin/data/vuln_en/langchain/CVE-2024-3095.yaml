info:
  name: langchain
  cve: CVE-2024-3095
  summary: Server-Side Request Forgery in langchain-community.retrievers.web_research.WebResearchRetriever
  details: |
    A Server-Side Request Forgery (SSRF) vulnerability exists in the Web Research Retriever component in langchain-community. 
    The vulnerability arises because the Web Research Retriever does not restrict requests to remote internet addresses, 
    allowing it to reach local addresses. This flaw enables attackers to execute port scans, access local services, 
    and in some scenarios, read instance metadata from cloud environments.
  cvss: CVSS:3.0/AV:P/AC:H/PR:L/UI:N/S:C/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to langchain-community >= 0.2.9
    2. Implement a proxy to prevent requests to local addresses
    3. Ensure users opt-in to the feature that may trigger SSRF
rule: version >= "0" && version < "0.2.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3095
  - https://github.com/langchain-ai/langchain/pull/24451
  - https://github.com/langchain-ai/langchain/commit/604dfe2d99246b0c09f047c604f0c63eafba31e7
  - https://github.com/langchain-ai/langchain/releases/tag/langchain-community%3D%3D0.2.9
  - https://huntr.com/bounties/e62d4895-2901-405b-9559-38276b6a5273