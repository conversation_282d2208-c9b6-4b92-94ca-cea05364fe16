info:
  name: langchain
  cve: CVE-2023-46229
  summary: LangChain Server Side Request Forgery vulnerability
  details: |
    LangChain before 0.0.317 allows SSRF via `document_loaders/recursive_url_loader.py` because crawling can proceed from an external server to an internal server.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to langchain>=0.0.317
    2. Review and restrict the URLs that can be accessed by `recursive_url_loader.py` to prevent external to internal server requests.
    3. Implement additional input validation and sanitization for URL inputs to mitigate SSRF risks.
rule: version < "0.0.317"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-46229
  - https://github.com/langchain-ai/langchain/pull/11925
  - https://github.com/langchain-ai/langchain/commit/9ecb7240a480720ec9d739b3877a52f76098a2b8
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-205.yaml