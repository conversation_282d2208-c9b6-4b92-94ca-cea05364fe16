info:
  name: langchain
  cve: CVE-2023-39659
  summary: <PERSON><PERSON><PERSON><PERSON> vulnerable to arbitrary code execution
  details: |
    An issue in langchain langchain-ai before version 0.0.325 allows a remote attacker to execute arbitrary code via a crafted script to the PythonAstREPLTool._run component.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain>=0.0.325
    2. Review and patch any custom implementations of PythonAstREPLTool._run
    3. Monitor for any suspicious activity related to code execution
rule: version < "0.0.325"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-39659
  - https://github.com/langchain-ai/langchain/issues/7700
  - https://github.com/langchain-ai/langchain/pull/12427
  - https://github.com/langchain-ai/langchain/pull/5640
  - https://github.com/langchain-ai/langchain/commit/cadfce295f8a33828fc635c2e5ea28b883e5c992
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/releases/tag/v0.0.325
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-147.yaml