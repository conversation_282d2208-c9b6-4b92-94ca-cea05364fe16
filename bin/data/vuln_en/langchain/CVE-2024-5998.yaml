info:
  name: langchain
  cve: CVE-2024-5998
  summary: <PERSON><PERSON><PERSON><PERSON> pickle deserialization of untrusted data
  details: |
    A vulnerability in the `FAISS.deserialize_from_bytes` function of langchain-ai/langchain allows for pickle deserialization of untrusted data. This can lead to the execution of arbitrary commands via the `os.system` function.
  cvss: CVSS:3.1/AV:P/AC:L/PR:L/UI:R/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to langchain-community>=0.2.4
    2. Avoid deserializing untrusted data
    3. Implement input validation to ensure data integrity
rule: version < "0.2.4"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-5998
  - https://github.com/langchain-ai/langchain/commit/77209f315efd13442ec51c67719ba37dfaa44511
  - https://github.com/langchain-ai/langchain
  - https://huntr.com/bounties/fa3a2753-57c3-4e08-a176-d7a3ffda28fe