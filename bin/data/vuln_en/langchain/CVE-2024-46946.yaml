info:
  name: langchain
  cve: CVE-2024-46946
  summary: LangChain Experimental Eval Injection vulnerability
  details: |
    langchain_experimental (aka LangChain Experimental) versions 0.1.17 through 0.3.0 allow attackers to execute arbitrary code through sympy.sympify (which uses eval) in LLMSymbolicMathChain. LLMSymbolicMathChain was introduced in commit fcccde406dd9e9b05fc9babcbeb9ff527b0ec0c6 on 2023-10-05.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain-experimental >= 0.3.1
    2. Review and patch the use of sympy.sympify to avoid code execution
    3. Monitor for any further updates or patches from the maintainers
rule: version >= "0.1.17" && version <= "0.3.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-46946
  - https://docs.sympy.org/latest/modules/codegen.html
  - https://gist.github.com/12end/68c0c58d2564ef4141bccd4651480820#file-cve-2024-46946-txt
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/releases/tag/langchain-experimental%3D%3D0.3.0