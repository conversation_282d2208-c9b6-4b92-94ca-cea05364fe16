info:
  name: langchain
  cve: CVE-2024-38459
  summary: langchain_experimental Code Execution via Python REPL access
  details: |
    langchain_experimental (formerly LangChain Experimental) versions prior to 0.0.61 allow unauthenticated Python REPL access without explicit user consent. This vulnerability arises from an incomplete fix for CVE-2024-27444.
  cvss: CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Immediately upgrade to langchain-experimental version 0.0.61 or later.
    2. Review and implement additional security measures to restrict unauthorized access.
    3. Monitor for any further updates or patches related to this vulnerability.
rule: version >= "0" && version < "0.0.61"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-38459
  - https://github.com/langchain-ai/langchain/pull/22860
  - https://github.com/langchain-ai/langchain/commit/ce0b0f22a175139df8f41cdcfb4d2af411112009
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/compare/langchain-experimental==0.0.60...langchain-experimental==0.0.61
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain-experimental/PYSEC-2024-53.yaml