info:
  name: langchain
  cve: CVE-2024-1455
  summary: LangChain's XMLOutputParser vulnerable to XML Entity Expansion
  details: |
    The XMLOutputParser in LangChain uses the etree module from the XML parser in the standard python library, which has some XML vulnerabilities.
    This primarily affects users that combine an LLM (or agent) with the `XMLOutputParser` and expose the component via an endpoint on a web-service. A successful attack is predicated on:
    1. Usage of XMLOutputParser
    2. Passing of malicious input into the XMLOutputParser either directly or by trying to manipulate an LLM to do so on the users behalf
    3. Exposing the component via a web-service
  cvss: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to langchain-core>=0.1.35
    2. Avoid exposing the XMLOutputParser component via web-services
    3. Implement input validation to prevent malicious XML input
rule: version >= "0" && version < "0.1.35"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1455
  - https://github.com/langchain-ai/langchain/pull/17250
  - https://github.com/langchain-ai/langchain/pull/19653
  - https://github.com/langchain-ai/langchain/pull/19660
  - https://github.com/langchain-ai/langchain/commit/727d5023ce88e18e3074ef620a98137d26ff92a3
  - https://github.com/langchain-ai/langchain
  - https://huntr.com/bounties/4353571f-c70d-4bfd-ac08-3a89cecb45b6