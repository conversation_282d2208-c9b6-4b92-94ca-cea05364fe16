info:
  name: langchain
  cve: CVE-2023-44467
  summary: langchain_experimental vulnerable to arbitrary code execution via PALChain in the python exec method
  details: |
    langchain_experimental (aka LangChain Experimental) in LangChain before 0.0.306 allows an attacker to bypass the CVE-2023-36258 fix and execute arbitrary code via __import__ in Python code, which is not prohibited by pal_chain/base.py.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain-experimental>=0.0.306
    2. Review and update code to prevent arbitrary code execution via __import__
    3. Monitor for any further updates or patches related to this vulnerability
rule: version < "0.0.306"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-44467
  - https://github.com/langchain-ai/langchain/pull/11233
  - https://github.com/langchain-ai/langchain/commit/4c97a10bd0d9385cfee234a63b5bd826a295e483
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain-experimental/PYSEC-2023-194.yaml
  - https://pypi.org/project/langchain-experimental/0.0.14