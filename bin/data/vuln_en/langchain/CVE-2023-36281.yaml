info:
  name: langchain
  cve: CVE-2023-36281
  summary: langchain vulnerable to arbitrary code execution
  details: |
    An issue in langchain v.0.0.171 allows a remote attacker to execute arbitrary code via the `load_prompt` parameter by submitting a specially crafted JSON file. This vulnerability is related to the misuse of `__subclasses__` or a template.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain >= 0.0.312 immediately.
    2. Review and update your application's input validation to prevent the exploitation of similar vulnerabilities.
    3. Monitor for any further security advisories related to langchain and apply updates promptly.
rule: version >= "0" && version < "0.0.312"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-36281
  - https://github.com/hwchase17/langchain/issues/4394
  - https://github.com/langchain-ai/langchain/pull/10252
  - https://github.com/langchain-ai/langchain/commit/22abeb9f6cc555591bf8e92b5e328e43aa07ff6c
  - https://aisec.today/LangChain-2e6244a313dd46139c5ef28cbcab9e55
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/releases/tag/v0.0.312
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-151.yaml