info:
  name: langchain
  cve: CVE-2023-32785
  summary: Langchain SQL Injection vulnerability
  details: |
    In Langchain before 0.0.247, prompt injection allows execution of arbitrary code against the SQL service provided by the chain.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain>=0.0.247
    2. Review and sanitize all user inputs to prevent SQL injection
    3. Implement least privilege access controls for the SQL service
rule: version < "0.0.247"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-32785
  - https://github.com/langchain-ai/langchain/issues/5923#issuecomment-1696053841
  - https://gist.github.com/rharang/9c58d39db8c01db5b7c888e467c0533f
  - https://github.com/langchain-ai/langchain