info:
  name: langchain
  cve: CVE-2024-2057
  summary: Critical SSRF vulnerability in LangC<PERSON>n's load_local function
  details: |
    A vulnerability classified as critical was found in Harrison Chase LangChain version 0.1.9.
    The affected function is `load_local` in `libs/community/langchain_community/retrievers/tfidf.py`.
    The manipulation of this function leads to server-side request forgery (SSRF), allowing remote attackers
    to make unauthorized requests. The exploit has been disclosed to the public and may be used.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: CRITICAL
  security_advise: |
    1. Upgrade to LangChain version 0.1.10 or later.
    2. Review and restrict the URLs that can be accessed by the `load_local` function.
    3. Implement additional input validation to prevent SSRF attacks.
rule: version < "0.1.10"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-2057
  - https://github.com/langchain-ai/langchain/pull/18695
  - https://github.com/bayuncao/vul-cve-16
  - https://github.com/bayuncao/vul-cve-16/tree/main/PoC.pkl
  - https://vuldb.com/?ctiid.255372
  - https://vuldb.com/?id.255372