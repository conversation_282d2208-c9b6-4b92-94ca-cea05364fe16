info:
  name: langchain
  cve: CVE-2024-2965
  summary: Denial of service in langchain-community
  details: |
    Denial of service in `SitemapLoader` Document Loader in the `langchain-community` package, affecting versions below 0.2.5. The `parse_sitemap` method, responsible for parsing sitemaps and extracting URLs, lacks a mechanism to prevent infinite recursion when a sitemap URL refers to the current sitemap itself. This oversight allows for the possibility of an infinite loop, leading to a crash by exceeding the maximum recursion depth in Python. This vulnerability can be exploited to occupy server socket/port resources and crash the Python process, impacting the availability of services relying on this functionality.
  cvss: CVSS:3.0/AV:P/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to langchain-community>=0.2.5
    2. Review and update the `parse_sitemap` method to prevent infinite recursion
    3. Monitor server logs for unusual activity that may indicate a DoS attempt
rule: version < "0.2.5"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-2965
  - https://github.com/langchain-ai/langchain/pull/22903
  - https://github.com/langchain-ai/langchain/commit/73c42306745b0831aa6fe7fe4eeb70d2c2d87a82
  - https://github.com/langchain-ai/langchain/commit/9a877c7adbd06f90a2518152f65b562bd90487cc
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-118.yaml
  - https://huntr.com/bounties/90b0776d-9fa6-4841-aac4-09fde5918cae