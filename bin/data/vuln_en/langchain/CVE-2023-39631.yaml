info:
  name: langchain
  cve: CVE-2023-39631
  summary: Langchain vulnerable to arbitrary code execution via the evaluate function in the numexpr library
  details: |
    An issue in LanChain-ai Langchain v.0.0.245 allows a remote attacker to execute arbitrary code via the evaluate function in the numexpr library.
    Patches: Released in v.0.0.308. numexpr dependency is optional for langchain.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain>=0.0.308
    2. Ensure numexpr is updated to version 2.8.5 or higher if used
rule: version < "0.0.308"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-39631
  - https://github.com/langchain-ai/langchain/issues/8363
  - https://github.com/pydata/numexpr/issues/442
  - https://github.com/langchain-ai/langchain/pull/11302
  - https://github.com/pydata/numexpr/commit/4b2d89cf14e75030d27629925b9998e1e91d23c7
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/releases/tag/v0.0.308
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-162.yaml
  - https://github.com/pypa/advisory-database/tree/main/vulns/numexpr/PYSEC-2023-163.yaml