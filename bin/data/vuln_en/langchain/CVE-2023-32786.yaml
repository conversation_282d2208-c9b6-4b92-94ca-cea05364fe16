info:
  name: langchain
  cve: CVE-2023-32786
  summary: Langchain Server-Side Request Forgery vulnerability
  details: |
    In Langchain before 0.0.329, prompt injection allows an attacker to force the service to retrieve data from an arbitrary URL, essentially providing SSRF and potentially injecting content into downstream tasks.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to langchain>=0.0.329
    2. Implement strict input validation to prevent prompt injection
    3. Monitor and restrict outbound network requests from the application
rule: version < "0.0.329"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-32786
  - https://github.com/langchain-ai/langchain/pull/12747
  - https://gist.github.com/rharang/d265f46fc3161b31ac2e81db44d662e1
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/releases/tag/v0.0.329