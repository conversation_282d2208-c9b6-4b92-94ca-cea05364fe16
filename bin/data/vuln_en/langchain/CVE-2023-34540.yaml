info:
  name: langchain
  cve: CVE-2023-34540
  summary: Langchain OS Command Injection vulnerability
  details: |
    Langchain before v0.0.225 was discovered to contain a remote code execution (RCE) vulnerability in the component JiraAPIWrapper (aka the JIRA API wrapper). This vulnerability allows attackers to execute arbitrary code via crafted input.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain>=0.0.225
    2. Review and update all dependencies to ensure they are up-to-date and free from vulnerabilities
    3. Implement input validation to prevent arbitrary code execution
rule: version < "0.0.225"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-34540
  - https://github.com/hwchase17/langchain/issues/4833
  - https://github.com/langchain-ai/langchain/issues/4833
  - https://github.com/langchain-ai/langchain/pull/6992
  - https://github.com/langchain-ai/langchain/commit/a2f191a32229256dd41deadf97786fe41ce04cbb
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/releases/tag/v0.0.225
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-91.yaml