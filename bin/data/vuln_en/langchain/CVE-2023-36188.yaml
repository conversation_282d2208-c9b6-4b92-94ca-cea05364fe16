info:
  name: langchain
  cve: CVE-2023-36188
  summary: langchain vulnerable to arbitrary code execution
  details: |
    An issue in langchain allows a remote attacker to execute arbitrary code via the PALChain parameter in the Python exec method.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to langchain >= 0.0.236
    2. Review and restrict the use of the PALChain parameter in the Python exec method to prevent unauthorized code execution
rule: version < "0.0.236"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-36188
  - https://github.com/langchain-ai/langchain/issues/5872
  - https://github.com/langchain-ai/langchain/pull/6003
  - https://github.com/langchain-ai/langchain/pull/8425
  - https://github.com/langchain-ai/langchain/commit/e294ba475a355feb95003ed8f1a2b99942509a9e
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-109.yaml