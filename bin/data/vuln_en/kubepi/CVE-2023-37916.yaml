info:
  name: kubepi
  cve: CVE-2023-37916
  summary: <PERSON>beP<PERSON> may leak password hash of any user
  details: |
    http://kube.pi/kubepi/api/v1/users/search?pageNum=1&&pageSize=10 leaks password of any user (including admin). This leads to password crack attacks.
    ### PoC
    https://drive.google.com/file/d/1ksdawJ1vShRJyT3wAgpqVmz-Ls6hMA7M/preview
    ### Impact
    - Leaking confidential information.
    - Can lead to password cracking attacks
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to kubepi version 1.6.5 or higher.
    2. Review and enforce stronger access controls around user data endpoints.
    3. Implement monitoring to detect unauthorized access attempts.
rule: version >= "0" && version < "1.6.5"
references:
  - https://github.com/1Panel-dev/KubePi/security/advisories/GHSA-87f6-8gr7-pc6h
  - https://nvd.nist.gov/vuln/detail/CVE-2023-37916
  - https://drive.google.com/file/d/1ksdawJ1vShRJyT3wAgpqVmz-Ls6hMA7M/preview
  - https://github.com/1Panel-dev/KubePi
  - https://github.com/1Panel-dev/KubePi/releases/tag/v1.6.5