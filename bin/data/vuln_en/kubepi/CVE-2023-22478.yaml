info:
  name: kube<PERSON>
  cve: CVE-2023-22478
  summary: KubePi may allow unauthorized access to system API
  details: |
    Unauthorized access refers to the ability to bypass the system's preset permission settings to access some API interfaces. The attack exploits a flaw in how online applications handle routing permissions.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to KubePi version v1.6.4 or later.
rule: version <= "1.6.3"
references:
  - https://github.com/1Panel-dev/KubePi/security/advisories/GHSA-gqx8-hxmv-c4v4
  - https://github.com/KubeOperator/KubePi/security/advisories/GHSA-gqx8-hxmv-c4v4
  - https://nvd.nist.gov/vuln/detail/CVE-2023-22478
  - https://github.com/KubeOperator/KubePi/commit/0c6774bf5d9003ae4d60257a3f207c131ff4a6d6
  - https://github.com/KubeOperator/KubePi/releases/tag/v1.6.4