info:
  name: kubepi
  cve: CVE-2023-37917
  summary: KubePi Privilege Escalation vulnerability
  details: |
    A normal user has permission to create/update users and can become an admin by editing the `isadmin` value in the request.
    **PoC:**
    Change the value of the `isadmin` field in the request to true:
    [PoC Link](https://drive.google.com/file/d/1e8XJbIFIDXaFiL-dqn0a0b6u7o3CwqSG/preview)
    **Impact:**
    Elevate user privileges
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:L/I:H/A:L
  severity: CRITICAL
  security_advise: |
    1. Upgrade to kubepi version 1.6.5 or higher.
    2. Review and enforce strict access controls for user management.
    3. Monitor for unauthorized administrative access attempts.
rule: version < "1.6.5"
references:
  - https://github.com/1Panel-dev/KubePi/security/advisories/GHSA-757p-vx43-fp9r
  - https://nvd.nist.gov/vuln/detail/CVE-2023-37917
  - https://drive.google.com/file/d/1e8XJbIFIDXaFiL-dqn0a0b6u7o3CwqSG/preview
  - https://github.com/1Panel-dev/KubePi
  - https://github.com/1Panel-dev/KubePi/releases/tag/v1.6.5