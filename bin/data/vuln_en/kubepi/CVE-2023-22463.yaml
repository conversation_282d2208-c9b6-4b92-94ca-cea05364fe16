info:
  name: kube<PERSON>
  cve: CVE-2023-22463
  summary: <PERSON>beP<PERSON> allows malicious actor to login with a forged JWT token via Hardcoded Jwtsigkeys
  details: |
    The JWT authentication function of KubePi <= v1.6.2 uses hard-coded Jwtsigkeys, resulting in the same Jwtsigkeys for all online projects. This means that an attacker can forge any JWT token to take over the administrator account of any online project.
    The use of hard-coded JwtSigKey allows an attacker to use this value to forge JWT tokens arbitrarily. The JwtSigKey is confidential and should not be hard-coded in the code.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to KubePi >= v1.6.3
    2. Ensure that JWT keys are not hard-coded and are securely managed.
rule: version <= "1.6.2"
references:
  - https://github.com/KubeOperator/KubePi/security/advisories/GHSA-vjhf-8vqx-vqpq
  - https://nvd.nist.gov/vuln/detail/CVE-2023-22463
  - https://github.com/KubeOperator/KubePi/commit/3be58b8df5bc05d2343c30371dd5fcf6a9fbbf8b
  - https://github.com/KubeOperator/KubePi/releases/tag/v1.6.3
  - https://github.com/KubeOperator/KubePi/blob/da784f5532ea2495b92708cacb32703bff3a45a3/internal/api/v1/session/session.go#L35