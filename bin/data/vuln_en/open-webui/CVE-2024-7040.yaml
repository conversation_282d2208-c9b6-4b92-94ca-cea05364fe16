info:
  name: open-webui
  cve: CVE-2024-7040
  summary: Improper access control vulnerability in open-webui
  details: |
    In version v0.3.8 of open-webui/open-webui, there is an improper access control vulnerability.
    On the frontend admin page, administrators are intended to view only the chats of non-admin members.
    However, by modifying the user_id parameter, it is possible to view the chats of any administrator,
    including those of other admin (owner) accounts.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui >= v0.3.9
    2. Implement strict access control checks on the admin page
    3. Validate and sanitize all user input parameters
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7040
  - https://huntr.com/bounties/bd182309-4aa4-4747-941e-bbc1741955c1