info:
  name: open-webui
  cve: CVE-2024-7045
  summary: Improper access control vulnerabilities in open-webui allow unauthorized prompt viewing.
  details: |
    In version v0.3.8 of open-webui/open-webui, improper access control vulnerabilities allow an attacker to view any prompts. 
    The application does not verify whether the attacker is an administrator, allowing the attacker to directly call the /api/v1/prompts/ interface 
    to retrieve all prompt information created by the admin, which includes the ID values. Subsequently, the attacker can exploit the 
    /api/v1/prompts/command/{command_id} interface to obtain arbitrary prompt information.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui >= v0.3.9
    2. Implement strict access control checks for the /api/v1/prompts/ interface
    3. Validate user roles and permissions before allowing access to sensitive data
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7045
  - https://huntr.com/bounties/03ea0826-af7b-4717-b63e-90fd19675ab2