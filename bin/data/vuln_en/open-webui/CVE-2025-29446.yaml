info:
  name: open-webui
  cve: CVE-2025-29446
  summary: SSRF vulnerability in open-webui v0.5.16
  details: |
    open-webui v0.5.16 contains a Server-Side Request Forgery (SSRF) vulnerability 
    in the `verify_connection` function within `routers/ollama.py`. This allows 
    attackers to make unauthorized requests to internal or external services 
    by manipulating the input to this function.
  cvss: CVSS:3.x/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H  # Placeholder, actual score needed
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >=0.5.17 (assuming this version addresses the issue)
    2. Implement strict input validation for all inputs to the `verify_connection` function
    3. Consider implementing network-level restrictions to limit the destinations 
       that can be accessed by the `verify_connection` function
rule: version == "0.5.16"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-29446
  - https://github.com/jcxj/jcxj/blob/master/source/_posts/open-webui-ssrf%E6%BC%8F%E6%B4%9E.md
  - https://github.com/l1uyi/cve-list/blob/main/cve-list/open-webui-ssrf.md