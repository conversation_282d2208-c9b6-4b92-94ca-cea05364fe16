info:
  name: open-webui
  cve: CVE-2024-7049
  summary: Vulnerability in open-webui allows pending role users to act without admin confirmation.
  details: |
    In version v0.3.8 of open-webui/open-webui, a vulnerability exists where a token is returned when a user with a pending role logs in. 
    This allows the user to perform actions without admin confirmation, bypassing the intended approval process.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui version v0.3.9 or higher.
    2. Implement additional checks to ensure that users with pending roles cannot receive tokens.
    3. Review and enforce proper role-based access control (RBAC) policies.
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7049
  - https://huntr.com/bounties/ee9e3532-8ef1-4599-bb59-b8e2ba43a1fc