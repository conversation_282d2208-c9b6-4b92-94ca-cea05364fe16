info:
  name: open-webui
  cve: CVE-2024-7039
  summary: Improper privilege management vulnerability in open-webui
  details: |
    In open-webui/open-webui version v0.3.8, there is an improper privilege management vulnerability. 
    The application allows an attacker, acting as an admin, to delete other administrators via the API endpoint 
    `http://0.0.0.0:8080/api/v1/users/{uuid_administrator}`. This action is restricted by the user interface but 
    can be performed through direct API calls.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= v0.3.9
    2. Implement strict API access controls to prevent unauthorized deletion of administrators
    3. Review and enhance user role management to ensure proper privilege separation
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7039
  - https://huntr.com/bounties/27fc8a5a-546e-4cf2-8edb-df42e36518fc