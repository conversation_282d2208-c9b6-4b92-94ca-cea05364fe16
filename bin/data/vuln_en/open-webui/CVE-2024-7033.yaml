info:
  name: open-webui
  cve: CVE-2024-7033
  summary: Arbitrary file write vulnerability in open-webui
  details: |
    In version 0.3.8 of open-webui/open-webui, an arbitrary file write vulnerability exists in the download_model endpoint.
    When deployed on Windows, the application improperly handles file paths, allowing an attacker to manipulate the file path
    to write files to arbitrary locations on the server's filesystem. This can result in overwriting critical system or
    application files, causing denial of service, or potentially achieving remote code execution (RCE). RCE can allow an
    attacker to execute malicious code with the privileges of the user running the application, leading to a full system
    compromise.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Implement strict file path validation in the download_model endpoint
    3. Regularly audit and monitor file system changes for unauthorized access
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7033
  - https://huntr.com/bounties/7078261f-8414-4bb7-9d72-a2a4d8bfd5d1