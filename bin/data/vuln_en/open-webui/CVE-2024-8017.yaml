info:
  name: open-webui
  cve: CVE-2024-8017
  summary: XSS vulnerability in open-webui versions <= 0.3.8
  details: |
    An XSS vulnerability exists in open-webui/open-webui versions <= 0.3.8, specifically in the function that constructs the HTML for tooltips. This vulnerability allows attackers to perform operations with the victim's privileges, such as stealing chat history, deleting chats, and escalating their own account to an admin if the victim is an admin.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Review and sanitize all user-generated content to prevent XSS attacks
    3. Implement strict content security policies (CSP) to mitigate the risk of XSS
rule: version <= "0.3.8"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8017
  - https://huntr.com/bounties/ef06c7c8-1cb2-42a7-a6e6-17b2e1c744f7