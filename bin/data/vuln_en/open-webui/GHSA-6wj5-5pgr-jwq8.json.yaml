info:
  name: open-webui
  cve: CVE-2024-53981
  summary: Open WebUI Unauthenticated Multipart Boundary Denial of Service (DoS) Vulnerability in api/chat/file
  details: |
    A vulnerability in open-webui/open-webui version 79778fa allows an attacker to cause a Denial of Service (DoS) by uploading a file with a malformed multipart boundary. By appending a large number of characters to the end of the multipart boundary, the server continuously processes each character, rendering the application inaccessible. This issue can prevent all users from accessing the application until the server recovers.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= 0.4.7
    2. Implement input validation for multipart boundaries to prevent excessively long inputs
    3. Monitor server logs for unusual activity that may indicate a DoS attempt
rule: version < "0.4.7"
references:
  - https://github.com/Kludex/python-multipart/security/advisories/GHSA-59g5-xgcq-4qw3
  - https://nvd.nist.gov/vuln/detail/CVE-2024-53981
  - https://github.com/open-webui/open-webui/commit/f311c03a21dc09e279a0cad7482a68b273517baf
  - https://github.com/open-webui/open-webui
  - https://huntr.com/bounties/15eb4fbe-70d4-420e-806a-ec6f4ecb7202