info:
  name: open-webui
  cve: CVE-2024-12537
  summary: Absence of authentication mechanisms in open-webui allows unauthenticated access leading to denial of service.
  details: |
    In version 0.3.32 of open-webui/open-webui, the absence of authentication mechanisms allows any unauthenticated attacker to access the `api/v1/utils/code/format` endpoint. If a malicious actor sends a POST request with an excessively high volume of content, the server could become completely unresponsive. This could lead to severe performance issues, causing the server to become unresponsive or experience significant degradation, ultimately resulting in service interruptions for legitimate users.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Implement authentication mechanisms for all endpoints.
    2. Rate limit POST requests to prevent excessive content volumes.
    3. Monitor server performance for unusual activity.
rule: version < "0.3.33"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12537
  - https://huntr.com/bounties/edabd06c-acc0-428c-a481-271f333755bc