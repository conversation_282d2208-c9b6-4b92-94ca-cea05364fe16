info:
  name: open-webui
  cve: CVE-2024-7041
  summary: open-webui Insecure Direct Object Reference (IDOR) vulnerability
  details: |
    An Insecure Direct Object Reference (IDOR) vulnerability exists in open-webui/open-webui version v0.3.8.
    The vulnerability occurs in the API endpoint `http://0.0.0.0:3000/api/v1/memories/{id}/update`, where
    the decentralization design is flawed, allowing attackers to edit other users' memories without proper authorization.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Review and enforce proper authorization checks in the API endpoint `http://0.0.0.0:3000/api/v1/memories/{id}/update`
    3. Implement input validation to ensure that users can only access and modify their own data
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7041
  - https://github.com/open-webui/open-webui
  - https://github.com/open-webui/open-webui/blob/main/backend/apps/webui/routers/memories.py#L71
  - https://huntr.com/bounties/6855227f-1237-47b8-8d37-29aad7ddec3a