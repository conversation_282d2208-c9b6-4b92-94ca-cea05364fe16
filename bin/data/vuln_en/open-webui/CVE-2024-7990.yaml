info:
  name: open-webui
  cve: CVE-2024-7990
  summary: Stored cross-site scripting (XSS) vulnerability in open-webui
  details: |
    A stored cross-site scripting (XSS) vulnerability exists in open-webui/open-webui version 0.3.8. 
    The vulnerability is present in the `/api/v1/models/add` endpoint, where the model description field 
    is improperly sanitized before being rendered in chat. This allows an attacker to inject malicious scripts 
    that can be executed by any user, including administrators, potentially leading to arbitrary code execution.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:R/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Implement proper input sanitization for all user-generated content
    3. Regularly review and update security measures to prevent similar vulnerabilities
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7990
  - https://huntr.com/bounties/2256e336-0f67-449e-a82d-7fc57081a21c