info:
  name: open-webui
  cve: CVE-2024-7048
  summary: Improper privilege management in open-webui API endpoints
  details: |
    In version v0.3.8 of open-webui, there exists an improper privilege management vulnerability in the API endpoints GET /api/v1/documents/ and POST /rag/api/v1/doc. 
    This allows a lower-privileged user to access and overwrite files managed by a higher-privileged admin, potentially compromising the integrity 
    and availability of the RAG models by viewing metadata of admin-uploaded files and overwriting them.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui version v0.3.9 or later.
    2. Review and adjust API endpoint permissions to ensure proper privilege management.
    3. Implement additional access controls and audits to monitor file access and modification.
rule: version == "0.3.8"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7048
  - https://huntr.com/bounties/acd0b2dd-61eb-4712-82d3-a4e35d6ee560