info:
  name: open-webui
  cve: CVE-2024-7036
  summary: Vulnerability in open-webui allows unauthenticated attacker to cause Admin panel unresponsiveness.
  details: |
    A vulnerability in open-webui/open-webui v0.3.8 allows an unauthenticated attacker to sign up with excessively large text in the 'name' field, causing the Admin panel to become unresponsive. 
    This prevents administrators from performing essential user management actions such as deleting, editing, or adding users. 
    The vulnerability can also be exploited by authenticated users with low privileges, leading to the same unresponsive state in the Admin panel.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= v0.3.9
    2. Implement input validation to restrict the size of text in the 'name' field
    3. Monitor for unusual activity on the Admin panel
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7036
  - https://huntr.com/bounties/ba62d093-ab27-48fa-9c53-0602c8cdc48a