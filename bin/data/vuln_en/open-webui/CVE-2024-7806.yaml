info:
  name: open-webui
  cve: CVE-2024-7806
  summary: Remote code execution via CSRF in open-webui versions <= 0.3.8
  details: |
    A vulnerability in open-webui/open-webui versions <= 0.3.8 allows remote code execution by non-admin users via Cross-Site Request Forgery (CSRF). The application uses cookies with the SameSite attribute set to lax for authentication and lacks CSRF tokens. This allows an attacker to craft a malicious HTML that, when accessed by a victim, can modify the Python code of an existing pipeline and execute arbitrary code with the victim's privileges.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Implement CSRF tokens for all state-changing operations
    3. Ensure cookies used for authentication have the SameSite attribute set to Strict or Lax
rule: version <= "0.3.8"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7806
  - https://huntr.com/bounties/9350a68d-5f33-4b3d-988b-81e778160ab8