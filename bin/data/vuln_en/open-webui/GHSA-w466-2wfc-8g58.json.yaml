info:
  name: open-webui
  cve: CVE-2024-47874
  summary: Open WebUI has vulnerable dependency on starlette via fastapi
  details: |
    In version 0.3.32 of open-webui, the application uses a vulnerable version of the starlette package through its dependency on fastapi. 
    The starlette package versions <=0.49 are susceptible to uncontrolled resource consumption, which can be exploited to cause a denial of service through memory exhaustion. 
    This issue is addressed in fastapi version 0.115.3.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >=0.3.33
    2. Ensure fastapi is updated to version 0.115.3 or later
    3. Monitor for updates on the starlette package and fastapi for any further security patches
rule: version < "0.3.33"
references:
  - https://github.com/encode/starlette/security/advisories/GHSA-f96h-pmfr-66vw
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47874
  - https://github.com/open-webui/open-webui
  - https://huntr.com/bounties/56175583-70e3-4d53-94de-3f3a8e2423ec