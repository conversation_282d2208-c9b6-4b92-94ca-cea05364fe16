info:
  name: open-webui
  cve: CVE-2024-7046
  summary: Improper access control vulnerability in open-webui allows viewing admin details.
  details: |
    An improper access control vulnerability in open-webui/open-webui v0.3.8 allows an attacker to view admin details.
    The application does not verify whether the attacker is an administrator, allowing the attacker to directly call
    the /api/v1/auths/admin/details interface to retrieve the first admin (owner) details.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui >= v0.3.9 (or the latest version with the fix)
    2. Implement proper access control checks before allowing access to admin details
    3. Regularly review and update access control mechanisms to prevent similar vulnerabilities
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7046
  - https://huntr.com/bounties/684185e4-6766-4638-b08a-0de9c2820aee