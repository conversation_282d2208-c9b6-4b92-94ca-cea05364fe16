info:
  name: open-webui
  cve: CVE-2024-7038
  summary: open-webui allows enumeration of file names and traversal of directories by observing the error messages
  details: |
    An information disclosure vulnerability exists in open-webui version 0.3.8. 
    The vulnerability is related to the embedding model update feature under admin settings. 
    When a user updates the model path, the system checks if the file exists and provides different error messages based on the existence and configuration of the file. 
    This behavior allows an attacker to enumerate file names and traverse directories by observing the error messages, leading to potential exposure of sensitive information.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:L/I:N/A:N
  severity: LOW
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Review and modify the error handling logic in the embedding model update feature to prevent information disclosure
    3. Implement additional security measures to restrict directory traversal and file enumeration attempts
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7038
  - https://github.com/open-webui/open-webui
  - https://github.com/open-webui/open-webui/blob/eff736acd2e0bbbdd0eeca4cc209b216a1f23b6a/backend/apps/rag/main.py#L199
  - https://huntr.com/bounties/f42cf72a-8015-44a6-81a9-c6332ef05afc