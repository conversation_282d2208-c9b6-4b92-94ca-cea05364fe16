info:
  name: open-webui
  cve: CVE-2024-12534
  summary: Lack of character length validation in open-webui leading to DoS
  details: |
    In version v0.3.32 of open-webui/open-webui, the application allows users to submit large payloads in the email and password fields during the sign-in process due to the lack of character length validation on these inputs. This vulnerability can lead to a Denial of Service (DoS) condition when a user submits excessively large strings, exhausting server resources such as CPU, memory, and disk space, and rendering the service unavailable for legitimate users.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= v0.3.33
    2. Implement character length validation for email and password fields
    3. Monitor server resources for unusual activity that could indicate an attack
rule: version < "0.3.33"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12534
  - https://huntr.com/bounties/c7c0a4e6-acd3-49b4-8684-2c2c27014b76