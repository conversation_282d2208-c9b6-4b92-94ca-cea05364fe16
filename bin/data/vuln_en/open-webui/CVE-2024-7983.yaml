info:
  name: open-webui
  cve: CVE-2024-7983
  summary: Unauthenticated markdown-to-HTML conversion endpoint in open-webui leads to DoS
  details: |
    In version 0.3.8 of open-webui, an endpoint for converting markdown to HTML is exposed without authentication. 
    A maliciously crafted markdown payload can cause the server to spend excessive time converting it, 
    leading to a denial of service. The server becomes unresponsive to other requests until the conversion is complete.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Implement authentication for the markdown-to-HTML conversion endpoint
    3. Validate and sanitize all input to prevent excessive processing
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7983
  - https://huntr.com/bounties/f8156ca5-1328-480f-a72b-8d3dfdad87dc