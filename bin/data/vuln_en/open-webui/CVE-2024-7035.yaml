info:
  name: open-webui
  cve: CVE-2024-7035
  summary: Insecure GET method for sensitive actions in open-webui
  details: |
    In version v0.3.8 of open-webui/open-webui, sensitive actions such as deleting and resetting are performed using the GET method. This vulnerability allows an attacker to perform Cross-Site Request Forgery (CSRF) attacks, where an unaware user can unintentionally perform sensitive actions by simply visiting a malicious site or through top-level navigation. The affected endpoints include /rag/api/v1/reset, /rag/api/v1/reset/db, /api/v1/memories/reset, and /rag/api/v1/reset/uploads. This impacts both the availability and integrity of the application.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:R/S:C/C:N/I:H/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui >= v0.3.9
    2. Implement CSRF protection for all sensitive actions
    3. Ensure that sensitive actions are only performed using POST or other secure HTTP methods
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7035
  - https://huntr.com/bounties/2ac81740-410b-467a-9244-75d82a6f9e11