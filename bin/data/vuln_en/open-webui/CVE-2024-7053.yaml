info:
  name: open-webui
  cve: CVE-2024-7053
  summary: Session fixation vulnerability in open-webui/open-webui version 0.3.8
  details: |
    A vulnerability in open-webui/open-webui version 0.3.8 allows an attacker with a user-level account to perform a session fixation attack. The session cookie for all users is set with the default `SameSite=Lax` and does not have the `Secure` flag enabled, allowing the session cookie to be sent over HTTP to a cross-origin domain. An attacker can exploit this by embedding a malicious markdown image in a chat, which, when viewed by an administrator, sends the admin's session cookie to the attacker's server. This can lead to a stealthy administrator account takeover, potentially resulting in remote code execution (RCE) due to the elevated privileges of administrator accounts.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:L/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui/open-webui >= 0.3.9
    2. Ensure the session cookie is set with the `Secure` flag
    3. Implement strict SameSite cookie attributes to prevent cross-site request forgery
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7053
  - https://huntr.com/bounties/947f8191-0abf-4adf-b7c4-d4c19683aba2