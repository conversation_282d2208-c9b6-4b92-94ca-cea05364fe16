info:
  name: open-webui
  cve: CVE-2024-7034
  summary: Arbitrary file write vulnerability in open-webui
  details: |
    In open-webui version 0.3.8, the endpoint `/models/upload` is vulnerable to arbitrary file write due to improper handling of user-supplied filenames. The vulnerability arises from the usage of `file_path = f\"{UPLOAD_DIR}/{file.filename}\"` without proper input validation or sanitization. An attacker can exploit this by manipulating the `file.filename` parameter to include directory traversal sequences, causing the resulting `file_path` to escape the intended `UPLOAD_DIR` and potentially overwrite arbitrary files on the system. This can lead to unauthorized modifications of system binaries, configuration files, or sensitive data, potentially enabling remote command execution.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Implement proper input validation and sanitization for user-supplied filenames
    3. Review and update file handling logic to prevent directory traversal attacks
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7034
  - https://huntr.com/bounties/711beada-10fe-4567-9278-80a689da8613