info:
  name: open-webui
  cve: CVE-2024-7043
  summary: Improper access control vulnerability in open-webui allows unauthorized file operations
  details: |
    An improper access control vulnerability in open-webui/open-webui v0.3.8 allows attackers to view and delete any files. 
    The application does not verify whether the attacker is an administrator, allowing the attacker to directly call 
    the GET /api/v1/files/ interface to retrieve information on all files uploaded by users, which includes the ID values. 
    The attacker can then use the GET /api/v1/files/{file_id} interface to obtain information on any file and the 
    DELETE /api/v1/files/{file_id} interface to delete any file.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= v0.3.9
    2. Implement proper access control checks to ensure only administrators can perform file operations
    3. Regularly audit and monitor file access logs for any suspicious activities
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7043
  - https://huntr.com/bounties/c01e0c7f-68d8-45cf-91d2-521c97f33b00