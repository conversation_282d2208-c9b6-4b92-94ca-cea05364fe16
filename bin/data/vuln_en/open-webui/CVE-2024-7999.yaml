info:
  name: open-webui
  cve: CVE-2024-7999
  summary: Denial of Service (DoS) vulnerability in open-webui due to malformed multipart boundary
  details: |
    A vulnerability in open-webui/open-webui version 79778fa allows an attacker to cause a Denial of Service (DoS) by uploading a file with a malformed multipart boundary. By appending a large number of characters to the end of the multipart boundary, the server continuously processes each character, rendering the application inaccessible. This issue can prevent all users from accessing the application until the server recovers.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of open-webui to mitigate this vulnerability.
    2. Implement server-side validation to detect and reject files with excessively long multipart boundaries.
    3. Monitor server logs for unusual activity that may indicate an attack attempt.
rule: version < "79778fa"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7999
  - https://huntr.com/bounties/15eb4fbe-70d4-420e-806a-ec6f4ecb7202