info:
  name: open-webui
  cve: CVE-2024-8060
  summary: Open WebUI allows Remote Code Execution via Arbitrary File Upload
  details: |
    OpenWebUI version 0.3.0 contains a vulnerability in the audio API endpoint `/audio/api/v1/transcriptions` that allows for arbitrary file upload. The application performs insufficient validation on the `file.content_type` and allows user-controlled filenames, leading to a path traversal vulnerability. This can be exploited by an authenticated user to overwrite critical files within the Docker container, potentially leading to remote code execution as the root user.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui>=0.5.17
    2. Implement strict file validation for uploads
    3. Review and enforce proper file permissions within the Docker container
rule: version >= "0" && version < "0.5.17"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8060
  - https://github.com/open-webui/open-webui/commit/613a087387c094e71ee91d29c015195ef401e160
  - https://github.com/open-webui/open-webui
  - https://huntr.com/bounties/a3b1a4b7-c723-496d-842c-844cc0988fe9