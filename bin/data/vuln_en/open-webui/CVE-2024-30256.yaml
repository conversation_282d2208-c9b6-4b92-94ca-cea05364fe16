info:
  name: open-webui
  cve: CVE-2024-30256  # Placeholder CVE ID, replace with actual CVE ID if available
  summary: Open WebUI vulnerability allows unauthorized access
  details: |
    The Open WebUI vulnerability enables attackers to gain unauthorized access to the system through a web interface. 
    This can lead to data exposure, system compromise, and further malicious activities.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H  # Placeholder CVSS score, replace with actual score if available
  severity: CRITICAL
  security_advise: |
    1. Immediately patch the Open WebUI to the latest version.
    2. Implement strict access controls and authentication mechanisms.
    3. Regularly monitor and audit access logs for any suspicious activities.
rule: version < "1.2.3"  # Placeholder version rule, replace with actual version if available
references:
  - https://securitylab.github.com/advisories/GHSL-2024-033_open-webui/