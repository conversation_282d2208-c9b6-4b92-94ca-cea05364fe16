info:
  name: open-webui
  cve: CVE-2024-7959
  summary: SSRF vulnerability in `/openai/models` endpoint of open-webui/open-webui version 0.3.8
  details: |
    The `/openai/models` endpoint in open-webui/open-webui version 0.3.8 is vulnerable to Server-Side Request Forgery (SSRF). 
    An attacker can change the OpenAI URL to any URL without checks, causing the endpoint to send a request to the specified URL and return the output. 
    This vulnerability allows the attacker to access internal services and potentially gain command execution by accessing instance secrets.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui/open-webui >=0.3.9
    2. Implement strict URL validation for the `/openai/models` endpoint
    3. Monitor and restrict outbound requests from the application
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7959
  - https://huntr.com/bounties/3c8bea0a-d678-4d67-bb9c-2b5b610a2193