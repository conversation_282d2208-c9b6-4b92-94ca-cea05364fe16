info:
  name: open-webui
  cve: CVE-2024-6707
  summary: Path traversal vulnerability allowing attacker-controlled file uploads
  details: |
    The vulnerability allows attackers to upload files to arbitrary locations on the web server's filesystem by exploiting a path traversal issue.
    This could lead to unauthorized data modification, code execution, or further system compromise.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Review and patch the path traversal vulnerability in the web application.
    2. Implement strict file upload validation to ensure files are only uploaded to designated directories.
    3. Regularly update and patch all software components to mitigate known vulnerabilities.
rule: version > "0" && version < "1.4.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6707
  - https://korelogic.com/Resources/Advisories/KL-001-2024-006.txt