info:
  name: open-webui
  cve: CVE-2024-6706
  summary: Open WebUI Stored Cross-Site Scripting Vulnerability
  details: |
    Attackers can craft a malicious prompt that coerces the language model into executing arbitrary JavaScript in the context of the web page.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui >= 0.1.106
    2. Implement input validation to prevent malicious script injection
    3. Regularly monitor and review for any suspicious activity related to XSS attacks
rule: version >= "0" && version < "0.1.106"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6706
  - https://github.com/open-webui/open-webui
  - https://korelogic.com/Resources/Advisories/KL-001-2024-005.txt