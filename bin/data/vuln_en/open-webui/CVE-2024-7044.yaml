info:
  name: open-webui
  cve: CVE-2024-7044
  summary: Stored Cross-Site Scripting (XSS) vulnerability in open-webui chat file upload
  details: |
    A Stored Cross-Site Scripting (XSS) vulnerability exists in the chat file upload functionality of open-webui/open-webui version 0.3.8. 
    An attacker can inject malicious content into a file, which, when accessed by a victim through a URL or shared chat, executes JavaScript in the victim's browser. 
    This can lead to user data theft, session hijacking, malware distribution, and phishing attacks.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Implement server-side validation for all uploaded files to prevent malicious content
    3. Regularly update and patch the open-webui application to mitigate future vulnerabilities
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7044
  - https://huntr.com/bounties/c25a885c-d6e2-4169-9ee8-4d33bcbb5ef6