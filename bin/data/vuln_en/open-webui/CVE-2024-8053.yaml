info:
  name: open-webui
  cve: CVE-2024-8053
  summary: Lack of authentication in PDF generation endpoint in open-webui
  details: |
    In version v0.3.10 of open-webui/open-webui, the `api/v1/utils/pdf` endpoint lacks authentication mechanisms.
    Attackers can exploit this by sending a POST request with an excessively large payload, potentially leading to server resource exhaustion and denial of service (DoS).
    Additionally, unauthorized users can misuse the endpoint to generate PDFs without verification, resulting in service misuse and potential operational and financial impacts.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= v0.3.11
    2. Implement authentication mechanisms for the `api/v1/utils/pdf` endpoint
    3. Validate and limit the size of payloads for PDF generation requests
rule: version < "0.3.11"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8053
  - https://huntr.com/bounties/ebe8c1fa-113b-4df9-be03-a406b9adb9f4