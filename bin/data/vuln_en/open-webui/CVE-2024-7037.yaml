info:
  name: open-webui
  cve: CVE-2024-7037
  summary: open-webui allows writing and deleting arbitrary files
  details: |
    In version v0.3.8 of open-webui/open-webui, the endpoint /api/pipelines/upload is vulnerable to arbitrary file write and delete due to unsanitized file.filename concatenation with CACHE_DIR. This vulnerability allows attackers to overwrite and delete system files, potentially leading to remote code execution.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to open-webui >= 0.3.9
    2. Review and sanitize all file handling logic in the application
    3. Implement strict access controls around file upload functionalities
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7037
  - https://github.com/open-webui/open-webui
  - https://github.com/open-webui/open-webui/blob/main/backend/main.py#L1513
  - https://huntr.com/bounties/8508db68-9c99-4b1c-828c-e1bfcacfb847