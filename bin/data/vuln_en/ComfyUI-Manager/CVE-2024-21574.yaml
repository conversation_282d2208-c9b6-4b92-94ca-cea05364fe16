info:
  name: ComfyUI-Manager
  cve: CVE-2024-21574
  summary: Missing validation of pip field in ComfyUI-Manager allows remote code execution.
  details: |
    The vulnerability arises from the absence of validation for the pip field in POST requests
    to the /customnode/install endpoint. This enables attackers to execute arbitrary code on
    the server by triggering a pip install of a user-controlled package or URL.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately update to the latest version of ComfyUI-Manager.
    2. Review and implement proper input validation for all endpoints, especially those handling package installations.
    3. Monitor server logs for suspicious activity and consider implementing additional security measures such as network segmentation and intrusion detection systems.
rule: version < "1.4.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-21574
  - https://github.com/ltdrdata/ComfyUI-Manager/commit/ffc095a3e5acc1c404773a0510e6d055a6a72b0e
  - https://github.com/ltdrdata/ComfyUI-Manager/blob/ffc095a3e5acc1c404773a0510e6d055a6a72b0e/glob/manager_server.py#L798