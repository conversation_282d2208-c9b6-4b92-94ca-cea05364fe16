info:
  name: Chuanhugpt
  cve: CVE-2024-6036
  summary: Vulnerability in gaizhenbiao/chuanhuchatgpt allows unauthorized server restart
  details: |
    A vulnerability in gaizhenbiao/chuanhuchatgpt version 20240410 allows any user to restart the server at will by sending a specific request to the `/queue/join?` endpoint with `\"fn_index\":66`. This unrestricted server restart capability can severely disrupt service availability, cause data loss or corruption, and potentially compromise system integrity.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt
    2. Implement proper access controls for the `/queue/join?` endpoint
    3. Monitor server logs for unauthorized restart attempts
rule: version < "20240411"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6036
  - https://huntr.com/bounties/e9eaaea9-5750-4955-9142-2f12ad4b06db