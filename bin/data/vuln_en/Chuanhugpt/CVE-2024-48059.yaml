info:
  name: Chuanhugpt
  cve: CVE-2024-48059
  summary: Stored Cross-Site Scripting (XSS) in WebSocket session transmission in gaizhenbiao/chuanhuchatgpt project versions <=20240802.
  details: |
    The vulnerability affects the gaizhenbiao/chuanhuchatgpt project, versions <=20240802, and is related to stored Cross-Site Scripting (XSS) in WebSocket session transmission. An attacker can inject malicious content into a WebSocket message, which, when accessed by a victim, results in the execution of malicious JavaScript in the victim's browser.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to a version of gaizhenbiao/chuanhuchatgpt greater than 20240802.
    2. Implement input validation to prevent malicious script injection.
    3. Monitor and review WebSocket messages for suspicious activity.
rule: version <= "20240802"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-48059
  - https://gist.github.com/AfterSnows/c5a4cb029fb9142be5c54e531a9a240e
  - https://rumbling-slice-eb0.notion.site/Stored-XSS-via-Chat-message-in-gaizhenbiao-chuanhuchatgpt-104e3cda9e8c80b4b611dfc491c488d8?pvs=4