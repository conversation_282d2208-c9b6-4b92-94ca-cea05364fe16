info:
  name: Chuanhugpt
  cve: CVE-2024-3402
  summary: Stored Cross-Site Scripting (XSS) vulnerability in Chuanhugpt
  details: |
    A stored Cross-Site Scripting (XSS) vulnerability existed in version (20240121) of gaizhenbiao/chuanhuchatgpt due to inadequate sanitization and validation of model output data. Despite user-input validation efforts, the application fails to properly sanitize or validate the output from the model, allowing for the injection and execution of malicious JavaScript code within the context of a user's browser. This vulnerability can lead to the execution of arbitrary JavaScript code in the context of other users' browsers, potentially resulting in the hijacking of victims' browsers.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of Chuanhugpt that addresses this vulnerability.
    2. Implement strict input and output sanitization for model-generated content.
    3. Regularly review and update security measures to prevent similar vulnerabilities.
rule: version < "20240121"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3402
  - https://huntr.com/bounties/389570c4-0bf2-4bc3-84f5-2e7afdba8ed1