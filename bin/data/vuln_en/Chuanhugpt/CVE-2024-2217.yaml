info:
  name: Chuanhugpt
  cve: CVE-2024-2217
  summary: Improper access control in gaizhenbiao/chuanhuchatgpt allows unauthorized access to config.json.
  details: |
    The vulnerability in gaizhenbiao/chuanhuchatgpt arises from improper handling of HTTP requests for the `config.json` file,
    which does not adequately restrict access based on user authentication. This allows attackers to obtain sensitive information
    such as API keys (`openai_api_key`, `google_palm_api_key`, `xmchat_api_key`, etc.), configuration details, and user credentials,
    regardless of whether the application is accessed in authenticated or unauthenticated mode.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Immediately update to the latest version of gaizhenbiao/chuanhuchatgpt, if available.
    2. Implement proper access controls to restrict access to the `config.json` file based on user authentication.
    3. Regularly audit and monitor access logs for any unauthorized attempts to access sensitive files.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-2217
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/c5ae3b5ae6b47259e0ce8730e0a47e85121f4a7d
  - https://huntr.com/bounties/e4df74bf-b2ee-490f-a9c9-e5c8010b8b29