info:
  name: Chuanhugpt
  cve: CVE-2024-9159
  summary: Incorrect authorization vulnerability in gaizhenbiao/chuanhuchatgpt allows any user to restart the server.
  details: |
    The vulnerability arises due to improper authorization checks in the function responsible for restarting the server.
    This allows any user to restart the server at will, potentially leading to a complete loss of availability.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Review and update the authorization logic for server restart functions to ensure only authorized users can perform this action.
    2. Implement proper admin checks to guard sensitive functions.
    3. Regularly audit and test authorization mechanisms to prevent similar vulnerabilities.
rule: version == "git c91dbfc"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9159
  - https://huntr.com/bounties/ab0f8fbb-c17a-45a7-8dab-7d4c8b90490a