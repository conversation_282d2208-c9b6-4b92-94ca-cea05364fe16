info:
  name: Chuanhugpt
  cve: CVE-2024-3404
  summary: Insecure access control in Chuanhuchatgpt allows authenticated users to read other users' chat history.
  details: |
    In gaizhenbiao/chuanhuchatgpt, specifically the version tagged as 20240121, there exists a vulnerability due to improper access control mechanisms. This flaw allows an authenticated attacker to bypass intended access restrictions and read the `history` files of other users, potentially leading to unauthorized access to sensitive information. The vulnerability is present in the application's handling of access control for the `history` path, where no adequate mechanism is in place to prevent an authenticated user from accessing another user's chat history files.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of Chuanhuchatgpt that addresses this vulnerability.
    2. Implement proper access control mechanisms to restrict access to user chat history files.
    3. Regularly review and update access control policies to prevent similar vulnerabilities.
rule: version == "20240121"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3404
  - https://huntr.com/bounties/ed32fc32-cb8f-4fbd-8209-cc835d279699