info:
  name: Chuanhugpt
  cve: CVE-2024-6038
  summary: Regular Expression Denial of Service (ReDoS) vulnerability in gaizhenbiao/chuanhuchatgpt.
  details: |
    A Regular Expression Denial of Service (ReDoS) vulnerability exists in the latest version of gaizhenbiao/chuanhuchatgpt.
    The vulnerability is located in the filter_history function within the utils.py module. This function takes a user-provided
    keyword and attempts to match it against chat history filenames using a regular expression search. Due to the lack of
    sanitization or validation of the keyword parameter, an attacker can inject a specially crafted regular expression,
    leading to a denial of service condition. This can cause severe degradation of service performance and potential system
    unavailability.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt that addresses this vulnerability.
    2. Implement input validation and sanitization for user-provided keywords in the filter_history function.
    3. Monitor system performance for unusual activity that may indicate a ReDoS attack.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6038
  - https://huntr.com/bounties/d41cca0a-82bc-4cbf-a52a-928d304fb42d