info:
  name: Chuanhugpt
  cve: CVE-2024-8400
  summary: Stored cross-site scripting (XSS) vulnerability in gaizhenbiao/chuanhuchatgpt
  details: |
    A stored cross-site scripting (XSS) vulnerability exists in the latest version of gaizhenbiao/chuanhuchatgpt. 
    The vulnerability allows an attacker to upload a malicious HTML file containing JavaScript code, 
    which is then executed when the file is accessed. This can lead to the execution of arbitrary JavaScript 
    in the context of the user's browser.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Update to the latest version of gaizhenbiao/chuanhuchatgpt that addresses this vulnerability.
    2. Implement input validation to prevent malicious HTML and JavaScript uploads.
    3. Regularly review and patch any identified security vulnerabilities in dependencies.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8400
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/2cca68e34f029babbe4eaa5a77d220dad68fdd49
  - https://huntr.com/bounties/405f16b8-848e-427d-a61a-ea7d3fd6f0e3