info:
  name: Chuanhugpt
  cve: CVE-2024-8613
  summary: Vulnerability in Chuanhuchatgpt allows unauthorized access to chat histories.
  details: |
    A vulnerability in gaizhenbiao/chuanhuchatgpt version 20240802 allows attackers to access, copy, and delete other users' chat histories. This issue arises due to improper handling of session data and lack of access control mechanisms, enabling attackers to view and manipulate chat histories of other users.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt.
    2. Implement proper session data handling and access control mechanisms.
    3. Regularly review and update security measures to prevent unauthorized access.
rule: version < "20240803"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8613
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/526c615c437377ee9c71f866fd0f19011910f705
  - https://huntr.com/bounties/76258774-b011-4044-9c3d-c2609b1cbd29