info:
  name: Chuanhugpt
  cve: CVE-2024-5278
  summary: Unrestricted file upload vulnerability in gaizhenbiao/chuanhuchatgpt
  details: |
    gaizhenbiao/chuanhuchatgpt is vulnerable to an unrestricted file upload vulnerability due to insufficient validation of uploaded file types in its `/upload` endpoint. Specifically, the `handle_file_upload` function does not sanitize or validate the file extension or content type of uploaded files, allowing attackers to upload files with arbitrary extensions, including HTML files containing XSS payloads and Python files. This vulnerability, present in the latest version as of 20240310, could lead to stored XSS attacks and potentially result in remote code execution (RCE) on the server hosting the application.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Implement strict file type validation for all uploaded files.
    2. Sanitize and validate file extensions and content types before processing.
    3. Update the application to the latest version that addresses this vulnerability.
rule: version < "20240310"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-5278
  - https://huntr.com/bounties/ea821d86-941b-40f3-a857-91f758848e05