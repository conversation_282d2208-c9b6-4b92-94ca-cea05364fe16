info:
  name: Chuanhugpt
  cve: CVE-2024-7962
  summary: Arbitrary file read vulnerability in Chuanhuchatgpt
  details: |
    An arbitrary file read vulnerability exists in gaizhenbiao/chuanhuchatgpt version ******** due to insufficient validation when loading prompt template files. 
    An attacker can read any file that matches specific criteria using an absolute path. 
    The file must not have a .json extension and, except for the first line, every other line must contain commas. 
    This vulnerability allows reading parts of format-compliant files, including code and log files, which may contain highly sensitive information such as account credentials.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of Chuanhuchatgpt that addresses this vulnerability.
    2. Implement strict validation for file paths and content when loading prompt template files.
    3. Regularly audit and monitor file access logs for any suspicious activity.
rule: version == "********"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7962
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/2836fd1db3efcd5ede63c0e7fbbdf677730dbb51
  - https://huntr.com/bounties/83f0a8e1-490c-49e7-b334-02125ee0f1b1