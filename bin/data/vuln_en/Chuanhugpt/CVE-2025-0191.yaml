info:
  name: Chuanhugpt
  cve: CVE-2025-0191
  summary: Denial of Service (DoS) vulnerability in file upload feature
  details: |
    A Denial of Service (DoS) vulnerability exists in the file upload feature of gaizhenbiao/chuanhuchatgpt version 20240914. 
    The vulnerability is due to improper handling of form-data with a large filename in the file upload request. 
    By sending a payload with an excessively large filename, the server becomes overwhelmed and unresponsive, 
    leading to unavailability for legitimate users.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt
    2. Implement server-side validation to limit the size of filenames in file upload requests
    3. Monitor server logs for unusual activity that may indicate an attack
rule: version == "20240914"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0191
  - https://huntr.com/bounties/c89a1dfd-a733-41b3-af20-6ef6024361eb