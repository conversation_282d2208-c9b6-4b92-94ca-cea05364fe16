info:
  name: Chuanhugpt
  cve: CVE-2024-4321
  summary: Local File Inclusion (LFI) vulnerability in gaizhenbiao/chuanhuchatgpt application
  details: |
    A Local File Inclusion (LFI) vulnerability exists in the gaizhenbiao/chuanhuchatgpt application, specifically within the functionality for uploading chat history. The vulnerability arises due to improper input validation when handling file paths during the chat history upload process. An attacker can exploit this vulnerability by intercepting requests and manipulating the 'name' parameter to specify arbitrary file paths. This allows the attacker to read sensitive files on the server, leading to information leakage, including API keys and private information.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt application.
    2. Implement strict input validation for file paths during the chat history upload process.
    3. Regularly audit and monitor file access logs for any suspicious activities.
rule: version < "20240311"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4321
  - https://huntr.com/bounties/19a16f8e-3d92-498f-abc9-8686005f067e