info:
  name: Chuanhugpt
  cve: CVE-2024-5823
  summary: File overwrite vulnerability in Chuanhuchatgpt
  details: |
    A file overwrite vulnerability exists in gaizhenbiao/chuanhuchatgpt versions <= 20240410.
    This allows an attacker to gain unauthorized access to overwrite critical configuration files,
    potentially leading to unauthorized system changes, security setting alterations,
    or denial of service (DoS) conditions.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to gaizhenbiao/chuanhuchatgpt version > 20240410
    2. Implement strict file access controls to prevent unauthorized overwrites
    3. Regularly audit configuration files for any signs of tampering
rule: version <= "20240410"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-5823
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/720c23d755a4a955dcb0a54e8c200a2247a27f8b
  - https://huntr.com/bounties/ca361701-7d68-4df6-8da0-caad4b85b9ae