info:
  name: Chuanhugpt
  cve: CVE-2024-6035
  summary: Stored Cross-Site Scripting (XSS) vulnerability in gaizhenbiao/chuanhuchatgpt
  details: |
    A Stored Cross-Site Scripting (XSS) vulnerability exists in gaizhenbiao/chuanhuchatgpt version 20240410. This vulnerability allows an attacker to inject malicious JavaScript code into the chat history file. When a victim uploads this file, the malicious script is executed in the victim's browser. This can lead to user data theft, session hijacking, malware distribution, and phishing attacks.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt
    2. Implement strict input validation to prevent XSS attacks
    3. Regularly review and update security measures to address new vulnerabilities
rule: version < "20240410"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6035
  - https://huntr.com/bounties/e4e8da71-53a9-4540-8d70-6b670b076987