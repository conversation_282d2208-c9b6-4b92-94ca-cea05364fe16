info:
  name: Chuanhugpt
  cve: CVE-2024-6090
  summary: Path traversal vulnerability in gaizhenbiao/chuanhuchatgpt
  details: |
    A path traversal vulnerability exists in gaizhenbiao/chuanhuchatgpt version 20240410, allowing any user to delete other users' chat histories. This vulnerability can also be exploited to delete any files ending in `.json` on the target system, leading to a denial of service as users are unable to authenticate.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to gaizhenbiao/chuanhuchatgpt version 20240411 or later
    2. Implement strict file path validation to prevent traversal attacks
    3. Regularly audit and monitor file system changes for unauthorized deletions
rule: version < "20240411"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6090
  - https://huntr.com/bounties/bd0f8f89-5c8a-4662-89aa-a6861d84cf4c