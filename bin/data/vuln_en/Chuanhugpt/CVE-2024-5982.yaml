info:
  name: Chuanhugpt
  cve: CVE-2024-5982
  summary: Path traversal vulnerability in gaizhenbiao/chuanhuchatgpt
  details: |
    A path traversal vulnerability exists in the latest version of gaizhenbiao/chuanhuchatgpt due to unsanitized input handling in multiple features. 
    - The `load_chat_history` function allows arbitrary file uploads, potentially leading to remote code execution (RCE).
    - The `get_history_names` function permits arbitrary directory creation.
    - The `load_template` function can be exploited to leak the first column of CSV files.
    These issues arise from improper sanitization of user inputs concatenated with directory paths using `os.path.join`.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Immediately update to the latest version of gaizhenbiao/chuanhuchatgpt that addresses this vulnerability.
    2. Review and sanitize all user inputs to prevent path traversal attacks.
    3. Implement strict access controls and monitoring for file uploads and directory creation.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-5982
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/952fc8c3cbacead858311747cddd4bedcb4721d7
  - https://huntr.com/bounties/5d5c5356-e893-44d1-b5ca-642aa05d96bb