info:
  name: Chuanhugpt
  cve: CVE-2024-9107
  summary: Stored cross-site scripting (XSS) vulnerability in Chuanhuchatgpt
  details: |
    A stored cross-site scripting (XSS) vulnerability exists in the gaizhenbiao/chuanhuchatgpt repository, affecting version git 20b2e02. 
    The vulnerability arises from improper sanitization of HTML tags in chat history uploads. Specifically, the sanitization logic fails to handle HTML tags within code blocks correctly, allowing an attacker to inject malicious scripts. 
    This can lead to the execution of arbitrary JavaScript code in the context of the user's browser, potentially leading to identity theft or other malicious actions.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of Chuanhuchatgpt to receive the fix.
    2. Implement strict input validation and sanitization for all user-generated content, especially within chat history uploads.
    3. Regularly review and update security measures to prevent similar vulnerabilities.
rule: version < "git 20b2e02"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9107
  - https://huntr.com/bounties/a2972c51-4780-4f60-afbf-a7a8ee4066ea