info:
  name: Chuanhugpt
  cve: CVE-2024-10707
  summary: Local file inclusion vulnerability in gaizhenbiao/chuanhuchatgpt due to gradio component issue
  details: |
    The vulnerability arises from the use of the gradio component gr.JSON in gaizhenbiao/chuanhuchatgpt version git d4ec6a3.
    It allows unauthenticated users to access arbitrary files on the server by uploading a specially crafted JSON file and
    exploiting improper input validation in the handle_dataset_selection function.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to a version of gaizhenbiao/chuanhuchatgpt that addresses this vulnerability.
    2. Review and enhance input validation for file uploads and JSON processing.
    3. Monitor for any suspicious activity related to file access and upload functionalities.
rule: version == "git d4ec6a3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10707
  - https://huntr.com/bounties/98fdedea-6ad0-4157-b7d2-ae71c9786ee8