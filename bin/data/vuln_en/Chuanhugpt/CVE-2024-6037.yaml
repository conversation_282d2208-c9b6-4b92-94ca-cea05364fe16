info:
  name: Chuanhugpt
  cve: CVE-2024-6037
  summary: Vulnerability in gaizhenbiao/chuanhuchatgpt allows arbitrary folder creation leading to resource exhaustion and potential data loss.
  details: |
    A vulnerability in gaizhenbiao/chuanhuchatgpt version 20240410 allows an attacker to create arbitrary folders at any location on the server, including the root directory (C: dir). This can lead to uncontrolled resource consumption, resulting in resource exhaustion, denial of service (DoS), server unavailability, and potential data loss or corruption.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Immediately update to the latest version of gaizhenbiao/chuanhuchatgpt.
    2. Implement strict file and directory permission checks to prevent unauthorized folder creation.
    3. Monitor server resources for unusual activity that may indicate an attack.
rule: version == "20240410"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6037
  - https://huntr.com/bounties/eca6904f-f9fd-40c8-9e85-96f54daf405e