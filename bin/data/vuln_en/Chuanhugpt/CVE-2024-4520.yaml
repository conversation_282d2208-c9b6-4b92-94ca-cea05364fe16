info:
  name: Chuanhugpt
  cve: CVE-2024-4520
  summary: Improper access control in gaizhenbiao/chuanhuchatgpt allows unauthorized chat history access.
  details: |
    An improper access control vulnerability exists in the gaizhenbiao/chuanhuchatgpt application, specifically in version 20240410. 
    This vulnerability allows any user on the server to access the chat history of any other user without requiring any form of interaction between the users. 
    Exploitation of this vulnerability could lead to data breaches, including the exposure of sensitive personal details, financial data, or confidential conversations. 
    Additionally, it could facilitate identity theft and manipulation or fraud through the unauthorized access to users' chat histories.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt that addresses this vulnerability.
    2. Implement proper access control mechanisms to restrict chat history access to authorized users only.
    3. Regularly audit and monitor access logs for any suspicious activity related to chat history access.
rule: version == "20240410"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4520
  - https://huntr.com/bounties/0dd2da9f-998d-45aa-a646-97391f524000