info:
  name: Chuanhugpt
  cve: CVE-2024-7807
  summary: Denial of Service (DOS) vulnerability in ChuanhuChatGPT
  details: |
    A vulnerability exists in gaizhenbiao/chuanhuchatgpt version 20240628 that allows for a Denial of Service (DOS) attack. 
    When uploading a file, if an attacker appends a large number of characters to the end of a multipart boundary, 
    the system will continuously process each character, rendering ChuanhuChatGPT inaccessible. 
    This uncontrolled resource consumption can lead to prolonged unavailability of the service, disrupting operations 
    and causing potential data inaccessibility and loss of productivity.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of ChuanhuChatGPT.
    2. Implement input validation to limit the size and content of multipart boundaries during file uploads.
    3. Monitor resource usage for unusual activity that may indicate an attack.
rule: version == "20240628"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7807
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/919222d285d73b9dcd71fb34de379eef8c90d175
  - https://huntr.com/bounties/db67276d-36ee-4487-9165-b621c67ef8a3