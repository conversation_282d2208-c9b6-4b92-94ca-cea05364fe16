info:
  name: Chuanhugpt
  cve: CVE-2024-8143
  summary: Unauthorized access to private chat histories in Chuanhuchatgpt
  details: |
    In the latest version (20240628) of gaizhenbiao/chuanhuchatgpt, an issue exists in the /file endpoint that allows authenticated users to access the chat history of other users. When a user logs in, a directory is created in the history folder with the user's name. By manipulating the /file endpoint, an authenticated user can enumerate and access files in other users' directories, leading to unauthorized access to private chat histories.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt that addresses this vulnerability.
    2. Implement additional access controls to restrict file enumeration and access within user directories.
    3. Regularly audit and monitor user activity to detect and respond to unauthorized access attempts.
rule: version < "20240629"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8143
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/ccc7479ace5c9e1a1d9f4daf2e794ffd3865fc2b
  - https://huntr.com/bounties/71c5ea4b-524a-4173-8fd4-2fbabd69502e