info:
  name: Chuanhugpt
  cve: CVE-2025-0188
  summary: Server-Side Request Forgery (SSRF) vulnerability in gaizhenbiao/chuanhuchatgpt
  details: |
    A Server-Side Request Forgery (SSRF) vulnerability was discovered in gaizhenbiao/chuanhuchatgpt version 20240914. 
    The vulnerability allows an attacker to construct a response link by saving the response in a folder named after the SHA-1 hash of the target URL. 
    This enables the attacker to access the response directly, potentially leading to unauthorized access to internal systems, data theft, service disruption, or further attacks such as port scanning and accessing metadata endpoints.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt.
    2. Implement strict input validation to prevent SSRF attacks.
    3. Monitor and restrict access to internal systems and services.
rule: version < "20240915"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0188
  - https://huntr.com/bounties/879d2470-eca5-49c0-b3d1-57469cfff412