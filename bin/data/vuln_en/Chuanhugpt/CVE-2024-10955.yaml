info:
  name: Chuanhugpt
  cve: CVE-2024-10955
  summary: Regular Expression Denial of Service (ReDoS) vulnerability in gaizhenbiao/chuanhuchatgpt
  details: |
    A Regular Expression Denial of Service (ReDoS) vulnerability exists in gaizhenbiao/chuanhuchatgpt, as of commit 20b2e02. The server uses the regex pattern `r'<[^>]+>'` to parse user input. In Python's default regex engine, this pattern can take polynomial time to match certain crafted inputs. An attacker can exploit this by uploading a malicious JSON payload, causing the server to consume 100% CPU for an extended period. This can lead to a Denial of Service (DoS) condition, potentially affecting the entire server.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Update gaizhenbiao/chuanhuchatgpt to the latest version that addresses this vulnerability.
    2. Review and modify regex patterns used for user input parsing to prevent ReDoS attacks.
    3. Implement input validation and rate limiting to mitigate the impact of potential attacks.
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10955
  - https://huntr.com/bounties/8291f8d0-5060-47e7-9986-1f411310fb7b