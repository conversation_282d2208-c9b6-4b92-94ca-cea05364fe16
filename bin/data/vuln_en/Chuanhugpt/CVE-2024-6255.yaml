info:
  name: Chuanhugpt
  cve: CVE-2024-6255
  summary: Vulnerability in JSON file handling allows unauthorized deletions.
  details: |
    A vulnerability in the JSON file handling of gaizhenbiao/chuanhuchatgpt version 20240410 allows any user to delete any JSON file on the server, including critical configuration files such as `config.json` and `ds_config_chatbot.json`. This issue arises due to improper validation of file paths, enabling directory traversal attacks. An attacker can exploit this vulnerability to disrupt the functioning of the system, manipulate settings, or potentially cause data loss or corruption.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to the latest version of gaizhenbiao/chuanhuchatgpt that addresses this vulnerability.
    2. Implement strict file path validation to prevent directory traversal attacks.
    3. Regularly backup critical configuration files to mitigate the risk of data loss or corruption.
rule: version == "20240410"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6255
  - https://huntr.com/bounties/48f3e370-6dcd-4f38-9350-d0419b3a7f82