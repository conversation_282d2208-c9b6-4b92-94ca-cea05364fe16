info:
  name: ChatRTX
  cve: CVE-2024-0082
  summary: NVIDIA ChatRTX for Windows contains a vulnerability in the UI that can lead to improper privilege management, potentially resulting in local escalation of privileges, information disclosure, and data tampering.
  details: |
    The vulnerability is located in the UI of NVIDIA ChatRTX for Windows. An attacker can exploit this by sending open file requests to the application, which may lead to improper privilege management. A successful exploit could result in local escalation of privileges, information disclosure, and data tampering.
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to ChatRTX version 0.3 or later.
    2. Implement strict input validation to prevent unauthorized file requests.
    3. Regularly monitor and audit system logs for any suspicious activity.
rule: version <= "0.2"
references:
  - https://nvidia.custhelp.com/app/answers/detail/a_id/5532
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0082