info:
  name: mlflow
  cve: CVE-2024-3848
  summary: MLflow has a Local File Read/Path Traversal bypass
  details: |
    A path traversal vulnerability exists in mlflow/mlflow version 2.11.0, identified as a bypass for the previously addressed CVE-2023-6909. The vulnerability arises from the application's handling of artifact URLs, where a '#' character can be used to insert a path into the fragment, effectively skipping validation. This allows an attacker to construct a URL that, when processed, ignores the protocol scheme and uses the provided path for filesystem access. As a result, an attacker can read arbitrary files, including sensitive information such as SSH and cloud keys, by exploiting the way the application converts the URL into a filesystem path. The issue stems from insufficient validation of the fragment portion of the URL, leading to arbitrary file read through path traversal.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow>=2.12.1
    2. Review and enhance URL validation logic to prevent path traversal
    3. Implement strict checks on artifact URLs to ensure they do not contain malicious fragments
rule: version >= "2.9.2" && version < "2.12.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3848
  - https://github.com/mlflow/mlflow/commit/f8d51e21523238280ebcfdb378612afd7844eca8
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/8d5aadaa-522f-4839-b41b-d7da362dd610