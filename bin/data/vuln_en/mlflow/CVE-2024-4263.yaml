info:
  name: mlflow
  cve: CVE-2024-4263
  summary: M<PERSON><PERSON> allows low privilege users to delete any artifact
  details: |
    A broken access control vulnerability exists in mlflow/mlflow versions before 2.10.1, where low privilege users with only EDIT permissions on an experiment can delete any artifacts. This issue arises due to the lack of proper validation for DELETE requests by users with EDIT permissions, allowing them to perform unauthorized deletions of artifacts.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to mlflow>=2.10.1
    2. Implement strict access control checks for DELETE requests
    3. Review and update user permissions to ensure they align with intended functionality
rule: version < "2.10.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4263
  - https://github.com/mlflow/mlflow/commit/b43e0e3de5b500554e13dc032ba2083b2d6c94b8
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2024-51.yaml
  - https://huntr.com/bounties/bfa116d3-2af8-4c4a-ac34-ccde7491ae11