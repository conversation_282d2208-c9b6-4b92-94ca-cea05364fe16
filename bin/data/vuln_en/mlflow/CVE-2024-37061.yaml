info:
  name: mlflow
  cve: CVE-2024-37061
  summary: M<PERSON>low improper input validation
  details: |
    Remote Code Execution can occur in versions of the MLflow platform running version 1.11.0 or newer, enabling a maliciously crafted MLproject to execute arbitrary code on an end user’s system when run due to unfiltered input.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.13.2
    2. Review and sanitize all MLproject inputs to prevent arbitrary code execution
    3. Implement strict input validation for all user-provided data
rule: version >= "1.11.0" && version <= "2.13.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-37061
  - https://github.com/mlflow/mlflow
  - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024