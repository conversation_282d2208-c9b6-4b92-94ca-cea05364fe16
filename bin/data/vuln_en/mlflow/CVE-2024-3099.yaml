info:
  name: mlflow
  cve: CVE-2024-3099
  summary: Undefined Behavior in mlflow allowing model name collision and potential DoS or data poisoning.
  details: |
    A vulnerability in mlflow/mlflow version 2.11.1 allows attackers to create multiple models with the same name by exploiting URL encoding. This flaw can lead to Denial of Service (DoS) as an authenticated user might not be able to use the intended model, as it will open a different model each time. Additionally, an attacker can exploit this vulnerability to perform data model poisoning by creating a model with the same name, potentially causing an authenticated user to become a victim by using the poisoned model. The issue stems from inadequate validation of model names, allowing for the creation of models with URL-encoded names that are treated as distinct from their URL-decoded counterparts.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to mlflow version 2.11.3 or higher.
    2. Implement additional validation for model names to prevent URL-encoded duplicates.
    3. Monitor for unusual model creation activities that could indicate an attack.
rule: version >= "0" && version < "2.11.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3099
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/8d96374a-ce8d-480e-9cb0-0a7e5165c24a