info:
  name: mlflow
  cve: CVE-2025-1474
  summary: <PERSON><PERSON> can create user accounts without setting a password in mlflow version 2.18
  details: |
    In mlflow/mlflow version 2.18, an admin is able to create a new user account without setting a password. 
    This vulnerability could lead to security risks, as accounts without passwords may be susceptible to unauthorized access. 
    Additionally, this issue violates best practices for secure user account management. The issue is fixed in version 2.19.0.
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:L/I:L/A:N
  severity: LOW
  security_advise: |
    1. Upgrade to mlflow >= 2.19.0
    2. Ensure all user accounts have strong passwords
    3. Review and enforce password policies for user account creation
rule: version >= "2.18.0" && version < "2.19.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1474
  - https://github.com/mlflow/mlflow/commit/149c9e18aa219bc47e86b432e130e467a36f4a17
  - https://huntr.com/bounties/e79f7774-10fe-46b2-b522-e73b748e3b2d