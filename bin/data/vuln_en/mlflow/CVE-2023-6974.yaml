info:
  name: mlflow
  cve: CVE-2023-6974
  summary: MLflow Server-Side Request Forgery (SSRF)
  details: |
    A malicious user could use this issue to access internal HTTP(s) servers and in the worst case (e.g., AWS instance), it could be abused to get remote code execution on the victim machine.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow >= 2.9.2
    2. Implement strict input validation for URLs to prevent SSRF attacks
    3. Monitor and restrict access to internal services from external sources
rule: version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6974
  - https://github.com/mlflow/mlflow/commit/8174250f83352a04c2d42079f414759060458555
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/438b0524-da0e-4d08-976a-6f270c688393