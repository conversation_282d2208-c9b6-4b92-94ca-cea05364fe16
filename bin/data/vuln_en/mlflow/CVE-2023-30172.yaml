info:
  name: mlflow
  cve: CVE-2023-30172
  summary: mlflow vulnerable to directory traversal
  details: |
    A directory traversal vulnerability in the /get-artifact API method of the mlflow platform prior to v2.0.0 allows attackers to read arbitrary files on the server via the path parameter.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.0.0rc0
    2. Review and restrict file access permissions for the /get-artifact API method
    3. Monitor server logs for suspicious file access attempts
rule: version < "2.0.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-30172
  - https://github.com/mlflow/mlflow/issues/7166
  - https://github.com/mlflow/mlflow/issues/7166#issuecomment-1541543234
  - https://github.com/mlflow/mlflow/pull/7170
  - https://github.com/mlflow/mlflow/commit/ac4b697bb0bb8a331944dca63f4235b4bf602ab8
  - https://github.com/mlflow/mlflow
  - https://github.com/mlflow/mlflow/commits/v2.0.0?after=00c3b0a350a28c25b16fbb7feddb8147a919ce18+69&branch=v2.0.0&qualified_name=refs%2Ftags%2Fv2.0.0
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-70.yaml