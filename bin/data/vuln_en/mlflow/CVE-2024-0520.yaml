info:
  name: mlflow
  cve: CVE-2024-0520
  summary: Remote code execution in mlflow due to command injection
  details: |
    A vulnerability in mlflow version 8.2.1 allows for remote code execution due to improper neutralization of special elements used in an OS command ('Command Injection') within the `mlflow.data.http_dataset_source.py` module. When loading a dataset from an HTTP source, the filename extracted from the `Content-Disposition` header or the URL path is used to generate the final file path without proper sanitization, enabling an attacker to control the file path and execute arbitrary commands.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow version 2.9.0 or higher.
    2. Review and sanitize all inputs used in OS command execution.
    3. Implement least privilege access controls to limit the impact of any potential exploitation.
rule: version < "2.9.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0520
  - https://github.com/mlflow/mlflow/commit/400c226953b4568f4361bc0a0c223511652c2b9d
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/93e470d7-b6f0-409b-af63-49d3e2a26dbc