info:
  name: mlflow
  cve: CVE-2024-8859
  summary: Path traversal vulnerability in mlflow version 2.15.1
  details: |
    A path traversal vulnerability exists in mlflow/mlflow version 2.15.1. When users configure and use the dbfs service, concatenating the URL directly into the file protocol results in an arbitrary file read vulnerability. This issue occurs because only the path part of the URL is checked, while parts such as query and parameters are not handled. The vulnerability is triggered if the user has configured the dbfs service, and during usage, the service is mounted to a local directory.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.15.2
    2. Review and modify the dbfs service configuration to properly handle URLs, ensuring that query and parameter parts are sanitized.
    3. Implement additional security checks around file operations to prevent arbitrary file reads.
rule: version == "2.15.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8859
  - https://github.com/mlflow/mlflow/commit/7791b8cdd595f21b5f179c7b17e4b5eb5cbbe654
  - https://huntr.com/bounties/2259b88b-a0c6-4c7c-b434-6aacf6056dcb