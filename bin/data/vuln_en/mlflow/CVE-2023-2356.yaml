info:
  name: mlflow
  cve: CVE-2023-2356
  summary: Relative path traversal in mlflow
  details: |
    Relative Path Traversal vulnerability exists in GitHub repository mlflow/mlflow prior to version 2.3.1.
    Attackers could exploit this to access files outside the intended directory, potentially leading to unauthorized data access or modification.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:L
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow version 2.3.1 or higher.
    2. Review and restrict file access permissions to prevent unauthorized traversal.
    3. Regularly update dependencies and conduct security audits.
rule: version < "2.3.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-2356
  - https://github.com/mlflow/mlflow/commit/f73147496e05c09a8b83d95fb4f1bf86696c6342
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-68.yaml
  - https://huntr.dev/bounties/7b5d130d-38eb-4133-8c7d-0dfc9a9d9896