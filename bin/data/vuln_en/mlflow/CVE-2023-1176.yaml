info:
  name: mlflow
  cve: CVE-2023-1176
  summary: Remote file existence check vulnerability in `mlflow server` and `mlflow ui` CLIs
  details: |
    Users of the MLflow Open Source Project who are hosting the MLflow Model Registry using the `mlflow server` or `mlflow ui` commands using an MLflow version older than MLflow 2.2.1 may be vulnerable to a remote file existence check exploit if they are not limiting who can query their server (for example, by using a cloud VPC, an IP allowlist for inbound requests, or authentication / authorization middleware).
    This issue only affects users and integrations that run the `mlflow server` and `mlflow ui` commands. Integrations that do not make use of `mlflow server` or `mlflow ui` are unaffected; for example, the Databricks Managed MLflow product and MLflow on Azure Machine Learning do not make use of these commands and are not impacted by these vulnerabilities in any way.
    The vulnerability enables an actor to check the existence of arbitrary files unrelated to MLflow from the host server, including any files stored in remote locations to which the host server has access.
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to mlflow>=2.2.1
    2. Limit who can access your MLflow Model Registry and MLflow Tracking servers using a cloud VPC, an IP allowlist for inbound requests, authentication / authorization middleware, or another access restriction mechanism.
    3. Limit the remote files to which your MLflow Model Registry and MLflow Tracking servers have access.
rule: version < "2.2.1"
references:
  - https://github.com/mlflow/mlflow/security/advisories/GHSA-wp72-7hj9-5265
  - https://nvd.nist.gov/vuln/detail/CVE-2023-1176
  - https://github.com/mlflow/mlflow/commit/63ef72aa4334a6473ce7f889573c92fcae0b3c0d
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-28.yaml
  - https://huntr.dev/bounties/ae92f814-6a08-435c-8445-eec0ef4f1085