info:
  name: mlflow
  cve: CVE-2023-4033
  summary: mlf<PERSON> vulnerable to OS Command Injection
  details: |
    OS Command Injection vulnerability exists in the GitHub repository mlflow/mlflow prior to version 2.6.0.
    Attackers could exploit this to execute arbitrary commands on the server, potentially leading to complete system compromise.
  cvss: CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow version 2.6.0 or higher.
    2. Review and update any custom code that executes system commands to ensure they are safe and validated.
    3. Regularly monitor for updates and security advisories from the mlflow project.
rule: version < "2.6.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-4033
  - https://github.com/mlflow/mlflow/commit/6dde93758d42455cb90ef324407919ed67668b9b
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-280.yaml
  - https://huntr.dev/bounties/5312d6f8-67a5-4607-bd47-5e19966fa321