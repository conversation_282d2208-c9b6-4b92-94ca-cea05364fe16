info:
  name: mlflow
  cve: CVE-2023-2780
  summary: mlflow Path Traversal vulnerability
  details: |
    mlflow prior to 2.3.0 is vulnerable to path traversal due to a bypass of the fix for CVE-2023-1177.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow >= 2.3.0
    2. Review and reinforce file path handling in your mlflow deployment
    3. Monitor for any unusual file access patterns post-upgrade
rule: version < "2.3.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-2780
  - https://github.com/mlflow/mlflow/commit/fae77a525dd908c56d6204a4cef1c1c75b4e9857
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-69.yaml
  - https://huntr.dev/bounties/b12b0073-0bb0-4bd1-8fc2-ec7f17fd7689