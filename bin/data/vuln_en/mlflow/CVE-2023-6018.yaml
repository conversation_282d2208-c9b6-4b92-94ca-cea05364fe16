info:
  name: mlflow
  cve: CVE-2023-6018
  summary: Remote Code Execution due to Full Controlled File Write in mlflow
  details: |
    The mlflow web server includes tools for tracking experiments, packaging code into reproducible runs, and sharing and deploying models. As this vulnerability allows writing or overwriting any file on the file system, it provides multiple avenues for achieving code execution (e.g., overwriting `/home/<USER>/.bashrc`). A malicious user could exploit this issue to gain command execution on the vulnerable machine, thereby accessing data and model information.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow >= 2.9.2
    2. Review and restrict file write permissions for the mlflow web server
    3. Implement input validation to prevent unauthorized file writes
rule: version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6018
  - https://github.com/mlflow/mlflow/commit/55c72d02380e8db8118595a4fdae7879cb7ac5bd
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/7cf918b5-43f4-48c0-a371-4d963ce69b30