info:
  name: mlflow
  cve: CVE-2023-6975
  summary: MLFlow Path Traversal Vulnerability
  details: |
    A malicious user could use this issue to get command execution on the vulnerable machine and get access to data & models information.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow >= 2.9.2
    2. Review and update your MLFlow deployment configurations to ensure secure handling of file paths.
    3. Regularly monitor and audit your MLFlow instances for any suspicious activities.
rule: version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6975
  - https://github.com/mlflow/mlflow/commit/b9ab9ed77e1deda9697fe472fb1079fd428149ee
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/029a3824-cee3-4cf1-b260-7138aa539b85