info:
  name: mlflow
  cve: CVE-2023-6568
  summary: Cross-site Scripting (XSS) in MLflow
  details: |
    A reflected Cross-Site Scripting (XSS) vulnerability exists in the mlflow/mlflow repository,
    specifically within the handling of the Content-Type header in POST requests. An attacker
    can inject malicious JavaScript code into the Content-Type header, which is then improperly
    reflected back to the user without adequate sanitization or escaping, leading to arbitrary
    JavaScript execution in the context of the victim's browser.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to mlflow version 2.9.0 or higher.
    2. Review and update all code that handles HTTP headers to ensure proper sanitization.
    3. Implement security headers to mitigate XSS attacks, such as Content-Security-Policy.
rule: version < "2.9.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6568
  - https://github.com/mlflow/mlflow/commit/28ff3f94994941e038f2172c6484b65dc4db6ca1
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-260.yaml
  - https://huntr.com/bounties/816bdaaa-8153-4732-951e-b0d92fddf709