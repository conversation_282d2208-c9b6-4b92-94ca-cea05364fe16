info:
  name: mlflow
  cve: CVE-2023-6709
  summary: Jinja2 template injection in mlflow
  details: |
    Improper Neutralization of Special Elements Used in a Template Engine in GitHub repository mlflow/mlflow prior to 2.9.2.
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow>=2.9.2
    2. Review and sanitize all user inputs to prevent template injection
    3. Implement security best practices for using Jinja2 templates
rule: version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6709
  - https://github.com/mlflow/mlflow/commit/432b8ccf27fd3a76df4ba79bb1bec62118a85625
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-281.yaml
  - https://huntr.com/bounties/9e4cc07b-6fff-421b-89bd-9445ef61d34d