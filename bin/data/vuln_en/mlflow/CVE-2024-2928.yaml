info:
  name: mlflow
  cve: CVE-2024-2928
  summary: Local File Inclusion in mlflow
  details: |
    A Local File Inclusion (LFI) vulnerability was identified in mlflow/mlflow, specifically in version 2.9.2, which was fixed in version 2.11.3. This vulnerability arises from the application's failure to properly validate URI fragments for directory traversal sequences such as '../'. An attacker can exploit this flaw by manipulating the fragment part of the URI to read arbitrary files on the local file system, including sensitive files like '/etc/passwd'.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.11.3
    2. Implement comprehensive validation of all parts of URIs to prevent directory traversal sequences
    3. Regularly update and patch all software dependencies to mitigate vulnerabilities
rule: version >= "0" && version < "2.11.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-2928
  - https://github.com/mlflow/mlflow/commit/96f0b573a73d8eedd6735a2ce26e08859527be07
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/19bf02d7-6393-4a95-b9d0-d6d4d2d8c298