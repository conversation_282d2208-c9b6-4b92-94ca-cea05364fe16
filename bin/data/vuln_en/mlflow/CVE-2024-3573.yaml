info:
  name: mlflow
  cve: CVE-2024-3573
  summary: mlflow vulnerable to Path Traversal
  details: |
    mlflow/mlflow is vulnerable to Local File Inclusion (LFI) due to improper parsing of URIs, allowing attackers to bypass checks and read arbitrary files on the system. The issue arises from the 'is_local_uri' function's failure to properly handle URIs with empty or 'file' schemes, leading to the misclassification of URIs as non-local. Attackers can exploit this by crafting malicious model versions with specially crafted 'source' parameters, enabling the reading of sensitive files within at least two directory levels from the server's root.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow>=2.10.0
    2. Review and update URI parsing logic to properly handle 'file' schemes and empty URIs
    3. Implement additional security checks for file inclusion vulnerabilities
rule: version >= "0" && version < "2.10.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-3573
  - https://github.com/mlflow/mlflow/commit/438a450714a3ca06285eeea34bdc6cf79d7f6cbc
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/8ea058a7-4ef8-4baf-9198-bc0147fc543c