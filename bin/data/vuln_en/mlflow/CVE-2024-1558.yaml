info:
  name: mlflow
  cve: CVE-2024-1558
  summary: mlflow vulnerable to Path Traversal
  details: |
    A path traversal vulnerability exists in the `_create_model_version()` function within `server/handlers.py` of the mlflow/mlflow repository, due to improper validation of the `source` parameter. Attackers can exploit this vulnerability by crafting a `source` parameter that bypasses the `_validate_non_local_source_contains_relative_paths(source)` function's checks, allowing for arbitrary file read access on the server. The issue arises from the handling of unquoted URL characters and the subsequent misuse of the original `source` value for model version creation, leading to the exposure of sensitive files when interacting with the `/model-versions/get-artifact` handler.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.12.1
    2. Review and validate all input parameters, especially those related to file paths or URLs, to ensure they do not contain malicious input.
    3. Implement additional security measures such as input sanitization and output encoding to prevent path traversal attacks.
rule: version <= "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1558
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/7f4dbcc5-b6b3-43dd-b310-e2d0556a8081