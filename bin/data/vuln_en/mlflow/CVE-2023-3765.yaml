info:
  name: mlflow
  cve: CVE-2023-3765
  summary: MLflow Path Traversal vulnerability
  details: |
    Absolute Path Traversal in GitHub repository mlflow/mlflow prior to 2.5.0.
    Attackers could exploit this to access sensitive files outside the intended directory,
    potentially leading to data exfiltration and system compromise.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow>=2.5.0
    2. Review and restrict file access permissions in the application
    3. Implement input validation to prevent path traversal attempts
rule: version < "2.5.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-3765
  - https://github.com/mlflow/mlflow/commit/6dde93758d42455cb90ef324407919ed67668b9b
  - https://github.com/mlflow/mlflow
  - https://huntr.dev/bounties/4be5fd63-8a0a-490d-9ee1-f33dc768ed76