info:
  name: mlflow
  cve: CVE-2024-1560
  summary: mlflow vulnerable to Path Traversal
  details: |
    A path traversal vulnerability exists in the mlflow/mlflow repository, specifically within the artifact deletion functionality. Attackers can bypass path validation by exploiting the double decoding process in the `_delete_artifact_mlflow_artifacts` handler and `local_file_uri_to_path` function, allowing for the deletion of arbitrary directories on the server's filesystem. This vulnerability is due to an extra unquote operation in the `delete_artifacts` function of `local_artifact_repo.py`, which fails to properly sanitize user-supplied paths.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.9.3
    2. Review and enhance path validation mechanisms in the artifact deletion functionality
    3. Regularly audit and test for path traversal vulnerabilities
rule: version < "2.9.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1560
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/4a34259c-3c8f-4872-b178-f27fbc876b98