info:
  name: mlflow
  cve: CVE-2024-6838
  summary: Vulnerability in mlflow allows denial of service via large experiment names and unlimited artifact locations.
  details: |
    In mlflow/mlflow version v2.13.2, a vulnerability exists that allows the creation or renaming of an experiment with a large number of integers in its name due to the lack of a limit on the experiment name. This can cause the MLflow UI panel to become unresponsive, leading to a potential denial of service. Additionally, there is no character limit in the `artifact_location` parameter while creating the experiment.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L
  severity: MEDIUM
  security_advise: |
    1. Upgrade to mlflow >= 2.13.3
    2. Implement input validation for experiment names to limit the number of characters and integers.
    3. Set a maximum length for the `artifact_location` parameter to prevent excessively large inputs.
rule: version < "2.13.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6838
  - https://huntr.com/bounties/8ad52cb2-2cda-4eb0-aec9-586060ee43e0