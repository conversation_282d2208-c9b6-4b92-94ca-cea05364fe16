info:
  name: mlflow
  cve: CVE-2023-6977
  summary: MLflow Local File Disclosure Vulnerability
  details: |
    This vulnerability enables malicious users to read sensitive files on the server.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow>=2.9.2
    2. Review and restrict file access permissions for the mlflow application
    3. Regularly audit and monitor file access logs for any suspicious activity
rule: version >= "0" && version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6977
  - https://github.com/mlflow/mlflow/commit/4bd7f27c810ba7487d53ed5ef1038fca0f8dc28c
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/fe53bf71-3687-4711-90df-c26172880aaf