info:
  name: mlflow
  cve: CVE-2023-6909
  summary: MLflow Path Traversal Vulnerability
  details: |
    Path Traversal vulnerability in GitHub repository mlflow/mlflow prior to version 2.9.2 allows attackers to traverse directories using '\\..\\filename'.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow version 2.9.2 or higher.
    2. Review and update file handling logic to prevent path traversal attacks.
    3. Regularly update dependencies and conduct security audits.
rule: version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6909
  - https://github.com/mlflow/mlflow/commit/1da75dfcecd4d169e34809ade55748384e8af6c1
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-252.yaml
  - https://huntr.com/bounties/11209efb-0f84-482f-add0-587ea6b7e850