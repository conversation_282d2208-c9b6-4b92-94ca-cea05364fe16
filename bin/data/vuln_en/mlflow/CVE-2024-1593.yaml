info:
  name: mlflow
  cve: CVE-2024-1593
  summary: mlflow vulnerable to Path Traversal
  details: |
    A path traversal vulnerability exists in the mlflow/mlflow repository due to improper handling of URL parameters. By smuggling path traversal sequences using the ';' character in URLs, attackers can manipulate the 'params' portion of the URL to gain unauthorized access to files or directories. This vulnerability allows for arbitrary data smuggling into the 'params' part of the URL, enabling attacks similar to those described in previous reports but utilizing the ';' character for parameter smuggling. Successful exploitation could lead to unauthorized information disclosure or server compromise.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.9.3
    2. Review and sanitize all URL parameters to prevent path traversal sequences
    3. Implement strict access controls to limit file and directory access
rule: version >= "0" && version < "2.9.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1593
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/dbdc6bd6-d09a-46f2-9d9c-5138a14b6e31