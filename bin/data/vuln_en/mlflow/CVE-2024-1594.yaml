info:
  name: mlflow
  cve: CVE-2024-1594
  summary: mlflow vulnerable to Path Traversal
  details: |
    A path traversal vulnerability exists in the mlflow/mlflow repository, specifically within the handling of the `artifact_location` parameter when creating an experiment. Attackers can exploit this vulnerability by using a fragment component `#` in the artifact location URI to read arbitrary files on the server in the context of the server's process. This issue is similar to CVE-2023-6909 but utilizes a different component of the URI to achieve the same effect.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.9.3
    2. Review and sanitize all inputs related to `artifact_location` to prevent path traversal
    3. Implement strict file access controls to limit file read operations
rule: version >= "0" && version < "2.9.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1594
  - https://github.com/mlflow/mlflow
  - https://github.com/mlflow/mlflow/blob/b929a3e727dc48a1eb19b7e954b7897ac09ad3ec/mlflow/utils/uri.py#L246
  - https://huntr.com/bounties/424b6f6b-e778-4a2b-b860-39730d396f3e