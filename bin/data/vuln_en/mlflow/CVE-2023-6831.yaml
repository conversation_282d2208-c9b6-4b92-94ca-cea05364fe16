info:
  name: mlflow
  cve: CVE-2023-6831
  summary: Path traversal vulnerability in MLflow
  details: |
    The vulnerability involves a path traversal issue in the GitHub repository mlflow/mlflow prior to version 2.9.2, where '\\..\\filename' could be exploited.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow version 2.9.2 or higher.
    2. Review and update file handling mechanisms to prevent path traversal attacks.
    3. Regularly monitor and audit the codebase for similar vulnerabilities.
rule: version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6831
  - https://github.com/mlflow/mlflow/commit/1da75dfcecd4d169e34809ade55748384e8af6c1
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-253.yaml
  - https://huntr.com/bounties/0acdd745-0167-4912-9d5c-02035fe5b314