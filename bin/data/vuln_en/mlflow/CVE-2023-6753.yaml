info:
  name: mlflow
  cve: CVE-2023-6753
  summary: Path traversal vulnerability in MLflow
  details: |
    The path traversal vulnerability exists in the GitHub repository mlflow/mlflow prior to version 2.9.2.
    Attackers could exploit this vulnerability to access files outside of the intended directory,
    potentially leading to unauthorized data access or modification.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow version 2.9.2 or higher.
    2. Review and restrict file access permissions to prevent unauthorized traversal.
rule: version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6753
  - https://github.com/mlflow/mlflow/commit/1c6309f884798fbf56017a3cc808016869ee8de4
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/b397b83a-527a-47e7-b912-a12a17a6cfb4