info:
  name: mlflow
  cve: CVE-2023-6976
  summary: MLflow Path Traversal Vulnerability
  details: |
    This vulnerability allows attackers to write arbitrary files into arbitrary locations on the remote filesystem in the context of the server process.
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.9.2
    2. Review and restrict file write permissions for the mlflow server process
    3. Implement additional input validation to prevent path traversal attempts
rule: version >= "0" && version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6976
  - https://github.com/mlflow/mlflow/commit/5044878da0c1851ccfdd5c0a867157ed9a502fbc
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/2408a52b-f05b-4cac-9765-4f74bac3f20f