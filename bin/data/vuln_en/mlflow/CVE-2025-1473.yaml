info:
  name: mlflow
  cve: CVE-2025-1473
  summary: Cross-Site Request Forgery (CSRF) vulnerability in mlflow Signup feature
  details: |
    A Cross-Site Request Forgery (CSRF) vulnerability exists in the Signup feature of mlflow/mlflow versions 2.17.0 to 2.20.1. This vulnerability allows an attacker to create a new account, which may be used to perform unauthorized actions on behalf of the malicious user.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. Upgrade to mlflow >= 2.20.2
    2. Implement CSRF protection measures for the Signup feature
    3. Regularly review and update security configurations
rule: version >= "2.17.0" && version <= "2.20.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1473
  - https://github.com/mlflow/mlflow/commit/ecfa61cb43d3303589f3b5834fd95991c9706628
  - https://huntr.com/bounties/43dc50b6-7d1e-41b9-9f97-f28809df1d45