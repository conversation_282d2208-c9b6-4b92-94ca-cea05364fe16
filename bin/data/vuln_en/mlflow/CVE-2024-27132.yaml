info:
  name: mlflow
  cve: CVE-2024-27132
  summary: Cross-site Scripting in MLFlow
  details: |
    Insufficient sanitization in MLflow leads to XSS when running an untrusted recipe.
    This issue leads to a client-side RCE when running an untrusted recipe in Jupyter Notebook.
    The vulnerability stems from lack of sanitization over template variables.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow>=2.10.0
    2. Implement strict input validation for all user inputs
    3. Regularly review and update security practices for handling untrusted data
rule: version < "2.10.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-27132
  - https://github.com/mlflow/mlflow/pull/10873
  - https://github.com/mlflow/mlflow
  - https://research.jfrog.com/vulnerabilities/mlflow-untrusted-recipe-xss-jfsa-2024-000631930