info:
  name: mlflow
  cve: CVE-2024-37057
  summary: MLFlow unsafe deserialization
  details: |
    Deserialization of untrusted data can occur in versions of the MLflow platform running version 2.0.0rc0 or newer, enabling a maliciously uploaded Tensorflow model to run arbitrary code on an end user’s system when interacted with.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.14.2
    2. Implement strict validation for deserialized data
    3. Monitor and restrict uploads of untrusted models
rule: version >= "2.0.0rc0" && version <= "2.14.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-37057
  - https://github.com/mlflow/mlflow
  - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024