info:
  name: mlflow
  cve: CVE-2023-43472
  summary: Information exposure in MLflow
  details: |
    An issue in MLFlow versions 2.8.1 and before allows a remote attacker to obtain sensitive information via a crafted request to the REST API.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow>=2.9.0
    2. Review and restrict access to MLflow REST API endpoints
    3. Monitor for unusual activity that may indicate information exposure
rule: version < "2.9.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-43472
  - https://github.com/mlflow/mlflow
  - https://mlflow.org/news/2023/12/06/2.9.0-release/index.html
  - https://www.contrastsecurity.com/security-influencers/discovering-mlflow-framework-zero-day-vulnerability-machine-language-model-security-contrast-security