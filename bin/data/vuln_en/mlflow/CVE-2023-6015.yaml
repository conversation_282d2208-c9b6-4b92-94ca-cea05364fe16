info:
  name: mlflow
  cve: CVE-2023-6015
  summary: <PERSON><PERSON><PERSON> allowed arbitrary files to be PUT onto the server
  details: |
    ML<PERSON> allowed arbitrary files to be PUT onto the server, potentially leading to unauthorized data manipulation or server compromise.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow >= 2.8.1
    2. Review and enforce stricter file upload policies
    3. Implement additional security measures to prevent unauthorized file uploads
rule: version < "2.8.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6015
  - https://github.com/mlflow/mlflow/pull/10330
  - https://github.com/mlflow/mlflow/commit/cf83dad4df26dd4a850622fe8a51ccab1471a5e7
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/43e6fb72-676e-4670-a225-15d6836f65d3