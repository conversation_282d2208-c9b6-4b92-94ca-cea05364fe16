info:
  name: mlflow
  cve: CVE-2024-37055
  summary: MLFlow unsafe deserialization
  details: |
    Deserialization of untrusted data can occur in versions of the MLflow platform running version 1.24.0 or newer, enabling a maliciously uploaded pmdarima model to run arbitrary code on an end user’s system when interacted with.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.14.2
    2. Avoid deserializing untrusted data
    3. Implement strict input validation for model uploads
rule: version >= "1.24.0" && version <= "2.14.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-37055
  - https://github.com/mlflow/mlflow
  - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024