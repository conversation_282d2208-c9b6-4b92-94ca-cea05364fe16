info:
  name: mlflow
  cve: CVE-2025-0453
  summary: Denial of Service vulnerability in mlflow GraphQL endpoint
  details: |
    In mlflow/mlflow version 2.17.2, the `/graphql` endpoint is vulnerable to a denial of service attack.
    An attacker can create large batches of queries that repeatedly request all runs from a given experiment,
    potentially tying up all the workers allocated by MLFlow and rendering the application unable to respond
    to other requests due to uncontrolled resource consumption.
  cvss: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. Upgrade to mlflow >= 2.17.3
    2. Implement rate limiting on the `/graphql` endpoint to prevent large batches of queries
    3. Monitor resource usage and set up alerts for abnormal activity
rule: version == "2.17.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0453
  - https://huntr.com/bounties/788327ec-714a-4d5c-83aa-8df04dd7612b