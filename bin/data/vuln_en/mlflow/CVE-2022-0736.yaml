info:
  name: mlflow
  cve: CVE-2022-0736
  summary: Insecure Temporary File in mlflow
  details: |
    mlflow prior to 1.23.1 contains an insecure temporary file. The insecure function `tempfile.mktemp()` is deprecated and `mkstemp()` should be used instead.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow>=1.23.1
    2. Replace `tempfile.mktemp()` with `tempfile.mkstemp()` in the codebase
    3. Regularly update dependencies to the latest secure versions
rule: version >= "0" && version < "1.23.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2022-0736
  - https://github.com/mlflow/mlflow/commit/61984e6843d2e59235d82a580c529920cd8f3711
  - https://github.com/advisories/GHSA-vqj2-4v8m-8vrq
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2022-28.yaml
  - https://huntr.dev/bounties/e5384764-c583-4dec-a1d8-4697f4e12f75