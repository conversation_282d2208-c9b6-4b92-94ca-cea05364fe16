info:
  name: mlflow
  cve: CVE-2024-1483
  summary: mlflow Path Traversal vulnerability
  details: |
    A path traversal vulnerability exists in mlflow/mlflow version 2.9.2, allowing attackers to access arbitrary files on the server. By crafting a series of HTTP POST requests with specially crafted 'artifact_location' and 'source' parameters, using a local URI with '#' instead of '?', an attacker can traverse the server's directory structure. The issue occurs due to insufficient validation of user-supplied input in the server's handlers.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.12.1
    2. Review and enhance input validation in server handlers to prevent path traversal attacks
rule: version <= "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1483
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/52a3855d-93ff-4460-ac24-9c7e4334198d