info:
  name: mlflow
  cve: CVE-2023-6940
  summary: mlflow Command Injection vulnerability
  details: |
    With only one user interaction (download a malicious config), attackers can gain full command execution on the victim system.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow >= 2.9.2
    2. Review and validate all configuration files to ensure they are from trusted sources
    3. Implement additional security measures such as input validation and least privilege access controls
rule: version >= "0" && version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6940
  - https://github.com/mlflow/mlflow/pull/10676
  - https://github.com/mlflow/mlflow/commit/5139b1087d686fa52e2b087e09da66aff86297b1
  - https://github.com/mlflow/mlflow/commit/a98a341a7222f894b7735db575ad9311ecaba4e3
  - https://github.com/mlflow/mlflow
  - https://github.com/mlflow/mlflow/commits/v2.9.2
  - https://huntr.com/bounties/c6f59480-ce47-4f78-a3dc-4bd8ca15029c