info:
  name: mlflow
  cve: CVE-2023-1177
  summary: mlflow is vulnerable to remote file access in `mlflow server` and `mlflow ui` CLIs
  details: |
    Users of the MLflow Open Source Project who are hosting the MLflow Model Registry using the `mlflow server` or `mlflow ui` commands using an MLflow version older than MLflow 2.2.1 may be vulnerable to a remote file access exploit if they are not limiting who can query their server (for example, by using a cloud VPC, an IP allowlist for inbound requests, or authentication / authorization middleware).
    This issue only affects users and integrations that run the `mlflow server` and `mlflow ui` commands. Integrations that do not make use of `mlflow server` or `mlflow ui` are unaffected; for example, the Databricks Managed MLflow product and MLflow on Azure Machine Learning do not make use of these commands and are not impacted by these vulnerabilities in any way.
    The vulnerability detailed in https://nvd.nist.gov/vuln/detail/CVE-2023-1177 enables an actor to download arbitrary files unrelated to MLflow from the host server, including any files stored in remote locations to which the host server has access.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow>=2.2.1
    2. Limit who can access your MLflow Model Registry and MLflow Tracking servers using a cloud VPC, an IP allowlist for inbound requests, authentication / authorization middleware, or another access restriction mechanism.
    3. Limit the remote files to which your MLflow Model Registry and MLflow Tracking servers have access.
rule: version < "2.2.1"
references:
  - https://github.com/mlflow/mlflow/security/advisories/GHSA-xg73-94fp-g449
  - https://nvd.nist.gov/vuln/detail/CVE-2023-1177
  - https://github.com/mlflow/mlflow/pull/7891/commits/7162a50c654792c21f3e4a160eb1a0e6a34f6e6e
  - https://github.com/mlflow/mlflow/commit/7162a50c654792c21f3e4a160eb1a0e6a34f6e6e
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-29.yaml
  - https://huntr.dev/bounties/1fe8f21a-c438-4cba-9add-e8a5dab94e28