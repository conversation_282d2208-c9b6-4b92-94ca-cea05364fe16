info:
  name: mlflow
  cve: CVE-2024-27133
  summary: MLFlow Cross-site Scripting vulnerability leads to client-side Remote Code Execution
  details: |
    Insufficient sanitization in MLflow leads to XSS when running a recipe that uses an untrusted dataset.
    This issue leads to a client-side RCE when running the recipe in Jupyter Notebook.
    The vulnerability stems from lack of sanitization over dataset table fields.
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. Upgrade to mlflow>=2.10.0
    2. Ensure all datasets used in recipes are sanitized
    3. Implement additional input validation for dataset fields
rule: version >= "0" && version < "2.10.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-27133
  - https://github.com/mlflow/mlflow/pull/10893
  - https://github.com/mlflow/mlflow/commit/c43823750bffa5b6abcc086683b15a068513b67b
  - https://github.com/mlflow/mlflow/commit/cfa71879a884cc3520e23ccab998c9aa78fdf2b1
  - https://github.com/mlflow/mlflow
  - https://research.jfrog.com/vulnerabilities/mlflow-untrusted-dataset-xss-jfsa-2024-000631932