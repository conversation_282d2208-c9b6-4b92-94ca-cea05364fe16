info:
  name: mlflow
  cve: CVE-2024-27134
  summary: MLflow's excessive directory permissions allow local privilege escalation
  details: |
    Excessive directory permissions in MLflow leads to local privilege escalation when using spark_udf. 
    This behavior can be exploited by a local attacker to gain elevated permissions by using a ToCToU attack. 
    The issue is only relevant when the spark_udf() MLflow API is called.
  cvss: CVSS:3.1/AV:L/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. Upgrade to mlflow>=2.16.0
    2. Review and adjust directory permissions to ensure they are not excessively permissive
    3. Monitor for any suspicious activity related to local privilege escalation
rule: version < "2.16.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-27134
  - https://github.com/mlflow/mlflow/pull/10874
  - https://github.com/mlflow/mlflow/commit/0b1d995d66a678153e01ed3040f3f4dfc16a0d6b
  - https://github.com/mlflow/mlflow