info:
  name: Xinference
  cve: CVE-2025-3622
  summary: Critical deserialization vulnerability in Xorbits Inference up to 1.4.1
  details: |
    A critical vulnerability has been discovered in the `xinference/thirdparty/cosyvoice/cli/model.py` file of Xorbits Inference versions up to 1.4.1.
    This issue arises from improper handling during the file load function, leading to potential deserialization attacks.
  cvss: CVSS:3.1/AV:A/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: CRITICAL
  security_advise: |
    1. Immediately upgrade to Xorbits Inference version 1.4.2 or later.
    2. Review and reinforce file handling and deserialization practices in your application.
    3. Monitor for any suspicious activities that may indicate an ongoing attack.
rule: version <= "1.4.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-3622
  - https://github.com/xorbitsai/inference/issues/3190
  - https://vuldb.com/?ctiid.304679