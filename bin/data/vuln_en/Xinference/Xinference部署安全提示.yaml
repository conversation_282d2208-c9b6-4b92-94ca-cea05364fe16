info:
  name: Xinference
  cve: Xinference Deployment Security Notice
  summary: Exposing Xinference externally may lead to computational power theft and data resource misuse
  details: Exposing Xinference externally may lead to computational power theft and data resource misuse
  cvss:
  severity: LOW
  security_advise: It is recommended to only expose Xinference locally
rule: is_internal=="true"
references: