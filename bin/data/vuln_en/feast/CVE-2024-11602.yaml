info:
  name: feast
  cve: CVE-2024-11602
  summary: Insecure CORS configuration in feast-dev/feast version 0.40.0
  details: |
    A Cross-Origin Resource Sharing (CORS) vulnerability exists in feast-dev/feast version 0.40.0.
    The CORS configuration on the agentscope server does not properly restrict access to only trusted origins,
    allowing any external domain to make requests to the API. This can bypass intended security controls
    and potentially expose sensitive information.
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. Upgrade to feast-dev/feast version 0.40.1 or later.
    2. Review and modify the CORS configuration to restrict access to trusted origins only.
    3. Implement additional security controls to prevent unauthorized API access.
rule: version == "0.40.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11602
  - https://huntr.com/bounties/7b24ecbe-0af7-4125-ab56-bce09786042e