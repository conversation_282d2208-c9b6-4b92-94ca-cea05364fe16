info:
  name: Xinference
  cve: CVE-2025-3622
  summary: Xorbits Inference up to 1.4.1中的严重反序列化漏洞
  details: |
    在Xorbits Inference版本1.4.1及之前的版本中的`xinference/thirdparty/cosyvoice/cli/model.py`文件中发现了一个严重漏洞。
    这个问题是由于文件加载函数处理不当，可能导致反序列化攻击。
  cvss: CVSS:3.1/AV:A/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: CRITICAL
  security_advise: |
    1. 立即升级到Xorbits Inference版本1.4.2或更高版本。
    2. 审查并加强应用程序中的文件处理和反序列化实践。
    3. 监控任何可能表明正在进行攻击的可疑活动。
rule: version <= "1.4.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-3622
  - https://github.com/xorbitsai/inference/issues/3190
  - https://vuldb.com/?ctiid.304679