info:
  name: vllm
  cve: CVE-2024-8768
  summary: vLLM拒绝服务漏洞
  details: 在vLLM库中发现了一个缺陷。带有空提示的completions API请求将导致vLLM API服务器崩溃，从而导致拒绝服务。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: 升级到vllm>=0.5.5以解决此问题。
rule: version >= "0" && version < "0.5.5"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-8768
 - https://github.com/vllm-project/vllm/issues/7632
 - https://github.com/vllm-project/vllm/pull/7746
 - https://github.com/vllm-project/vllm/commit/e25fee57c2e69161bd261f5986dc5aeb198bbd42
 - https://access.redhat.com/security/cve/CVE-2024-8768
 - https://bugzilla.redhat.com/show_bug.cgi?id=2311895
 - https://github.com/vllm-project/vllm