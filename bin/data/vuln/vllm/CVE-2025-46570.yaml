info:
  name: vllm
  cve: CVE-2025-46570
  summary: vLLM 的基于块的前缀缓存中潜在的定时旁路漏洞
  details: |
    此问题源于前缀缓存机制，可能会使系统暴露于定时旁路攻击。
    当处理新提示时，如果 PageAttention 机制找到匹配的前缀块，预填充过程会加快，这反映在 TTFT（首个令牌的时间）中。我们的测试表明，由匹配块引起的定时差异足够大，可以被识别和利用。
    例如，如果受害者提交了敏感提示或者有价值的系统提示被缓存，共享同一后端的攻击者可以尝试猜测受害者的输入。通过基于前缀匹配测量 TTFT，攻击者可以验证他们的猜测是否正确，可能导致私人信息的潜在泄露。
    与逐个令牌共享机制不同，vLLM 的基于块的方法（PageAttention）以更大的单位（块）处理令牌。在我们的测试中，当 chunk_size=2 时，定时差异变得足够明显，允许攻击者推断他们输入的部分是否在块级别与受害者的提示匹配。
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:R/S:U/C:L/I:N/A:N
  severity: LOW
  security_advise: |
    1. 升级到 vllm>=0.9.0
    2. 监控任何与定时旁路攻击相关的可疑活动
    3. 审查并调整系统配置以尽量减少潜在的攻击向量
rule: version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-4qjh-9fv9-r85r
  - https://nvd.nist.gov/vuln/detail/CVE-2025-46570
  - https://github.com/vllm-project/vllm/pull/17045
  - https://github.com/vllm-project/vllm/commit/77073c77bc2006eb80ea6d5128f076f5e6c6f54f
  - https://github.com/vllm-project/vllm