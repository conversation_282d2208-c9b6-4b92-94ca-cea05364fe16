info:
  name: vllm
  cve: CVE-2025-48944
  summary: vLLM 工具模式允许通过畸形模式和类型字段导致拒绝服务
  details: |
    当调用工具功能时，与 `/v1/chat/completions` OpenAPI 端点一起使用的 vLLM 后端未能验证 `"pattern"` 和 `"type"` 字段中的意外或畸形输入。这些输入在编译或解析之前未经过验证，导致推理工作进程在单个请求下崩溃。工作进程将保持关闭状态，直到重新启动。
    `"type"` 字段应为以下之一：`"string"`、`"number"`、`"object"`、`"boolean"`、`"array"` 或 `"null"`。提供任何其他值都将导致工作进程崩溃。
    `"pattern"` 字段在传递给本地正则表达式编译器之前会经过 Jinja2 渲染，而不会进行验证或转义。这允许畸形表达式到达底层 C++ 正则表达式引擎，从而导致致命错误。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到 vllm >= 0.9.0
    2. 在处理之前验证并清理 `"pattern"` 和 `"type"` 字段的输入
    3. 监控任何工作进程崩溃，并在必要时实施自动重启
rule: version >= "0.8.0" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-vrq3-r879-7m65
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48944
  - https://github.com/vllm-project/vllm/pull/17623
  - https://github.com/vllm-project/vllm