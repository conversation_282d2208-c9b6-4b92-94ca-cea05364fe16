info:
  name: vllm
  cve: CVE-2025-32444
  summary: vLLM 通过 Mooncake 集成易受远程代码执行攻击
  details: |
    **请注意，不使用 mooncake 集成的 vLLM 实例不受此漏洞影响。**
    vLLM 与 mooncake 的集成由于使用基于 `pickle` 的序列化通过不安全的 ZeroMQ 套接字而容易受到远程代码执行攻击。易受攻击的套接字被设置为监听所有网络接口，增加了攻击者能够接触到易受攻击的 ZeroMQ 套接字以实施攻击的可能性。
    这与 [GHSA - x3m8 - f7g5 - qhm7](https://github.com/vllm-project/vllm/security/advisories/GHSA-x3m8-f7g5-qhm7) 类似，问题在于
    [recv_pyobj()](https://github.com/zeromq/pyzmq/blob/453f00c5645a3bea40d79f53aa8c47d85038dc2d/zmq/sugar/socket.py#L961) 包含隐式的 `pickle.loads()`，这可能导致潜在的 RCE。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到 vllm >= 0.8.5
    2. 如果立即升级不可行，请考虑将使用 mooncake 集成的 vLLM 实例隔离到安全的网络段。
    3. 审查并更新序列化方法，避免在不安全的通道上使用 `pickle`。
rule: version >= "0.6.5" && version < "0.8.5"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-hj4w-hm2g-p6w5
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-x3m8-f7g5-qhm7
  - https://nvd.nist.gov/vuln/detail/CVE-2025-32444
  - https://github.com/vllm-project/vllm/commit/a5450f11c95847cf51a17207af9a3ca5ab569b2c
  - https://github.com/vllm-project/vllm
  - https://github.com/vllm-project/vllm/blob/32b14baf8a1f7195ca09484de3008063569b43c5/vllm/distributed/kv_transfer/kv_pipe/mooncake_pipe.py#L179