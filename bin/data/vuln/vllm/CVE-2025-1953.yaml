info:
  name: vllm
  cve: CVE-2025-1953
  summary: vLLM AIBrix的前缀缓存组件中随机值不足
  details: |
    在vLLM AIBrix 0.2.0的前缀缓存组件中发现了一个漏洞，
    具体在文件pkg/plugins/gateway/prefixcacheindexer/hash.go中。该操纵导致随机值不足，
    可能会影响系统的安全性。攻击的复杂性相当高，利用似乎很困难。
  cvss: CVSS:3.1/AV:A/AC:H/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: 低
  security_advise: |
    1. 升级到vLLM AIBrix版本0.3.0或更高版本。
    2. 监控任何与该漏洞相关的进一步更新或补丁。
rule: version < "0.3.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1953
  - https://github.com/vllm-project/aibrix/issues/749
  - https://github.com/vllm-project/aibrix/pull/752
  - https://vuldb.com/?ctiid.298543