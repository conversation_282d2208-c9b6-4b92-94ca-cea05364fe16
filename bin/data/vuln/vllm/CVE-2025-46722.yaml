info:
  name: vllm
  cve: CVE-2025-46722
  summary: vLLM在多模态哈希器图像哈希实现中存在弱点
  details: |
    在文件`vllm/multimodal/hasher.py`中，`MultiModalHasher`类的图像哈希方法存在安全和数据完整性问题。目前，它仅使用`obj.tobytes()`序列化`PIL.Image.Image`对象，该方法仅返回原始像素数据，而不包括元数据，例如图像的形状（宽度、高度、模式）。因此，两个不同大小（例如30x100和100x30）但具有相同像素字节序列的图像可能会生成相同的哈希值。这可能会导致哈希冲突、错误的缓存命中，甚至数据泄露或安全风险。
    影响扩展到其他模态，由于numpy排序不正确，视频受到影响，而音频受影响较小，因为librosa默认处理单通道编码。
    **建议：**在`serialize_item`方法中，对`Image.Image`对象的序列化应不仅包括像素数据，还应包括所有关键元数据，例如尺寸（`size`）、颜色模式（`mode`）、格式，尤其是`info`字典。
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:N/A:L
  severity: MEDIUM
  security_advise: |
    1. 升级到vllm >= 0.9.0以应用修复。
    2. 如果升级不是立即可行的，实现一个自定义哈希方法，包括图像元数据，用于`PIL.Image.Image`对象。
    3. 监控任何意外的缓存命中或哈希冲突，并调查潜在的安全影响。
rule: version >= "0.7.0" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-c65p-x677-fgj6
  - https://nvd.nist.gov/vuln/detail/CVE-2025-46722
  - https://github.com/vllm-project/vllm/pull/17378
  - https://github.com/vllm-project/vllm/commit/99404f53c72965b41558aceb1bc2380875f5d848
  - https://github.com/pypa/advisory-database/tree/main/vulns/vllm/PYSEC-2025-43.yaml
  - https://github.com/vllm-project/vllm