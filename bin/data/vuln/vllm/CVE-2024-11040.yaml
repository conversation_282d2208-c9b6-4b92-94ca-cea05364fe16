info:
  name: vllm
  cve: CVE-2024-11040
  summary: vllm-project vllm版本0.5.2.2易受拒绝服务攻击。
  details: |
    此漏洞影响“POST /v1/completions”和“POST /v1/embeddings”端点。
    - 对于“POST /v1/completions”，启用“use_beam_search”并将“best_of”设置为高值会导致HTTP连接超时，vllm停止有效工作且请求处于“挂起”状态，阻塞新的完成请求。
    - 对于“POST /v1/embeddings”，向JSON对象提供无效输入会在后台循环中引发问题，导致所有后续完成请求返回500 HTTP错误代码（“内部服务器错误”），直到重新启动vllm。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到vllm-project vllm版本0.5.2.3或更高版本。
    2. 为“POST /v1/embeddings”端点实施输入验证以防止无效输入。
    3. 监控并调整“POST /v1/completions”端点的“use_beam_search”和“best_of”设置以防止资源耗尽。
rule: version < "0.5.2.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11040
  - https://huntr.com/bounties/8ce20bbe-3c96-4cd1-97e5-25a5630925be