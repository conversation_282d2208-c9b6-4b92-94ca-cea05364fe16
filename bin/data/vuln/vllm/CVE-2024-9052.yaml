info:
  name: vllm
  cve: CVE-2024-9052
  summary: vllm-project vllm分布式训练API中的远程代码执行漏洞
  details: |
    vllm-project vllm版本0.6.0在分布式训练API中存在漏洞。
    函数`vllm.distributed.GroupCoordinator.recv_object()`使用`pickle.loads()`反序列化接收到的对象字节，而没有进行消毒，
    导致远程代码执行漏洞。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 立即升级到解决此漏洞的vllm-project vllm版本。
    2. 尽可能避免在网络应用程序中使用pickle进行反序列化。
    3. 对所有反序列化的数据实施输入验证和消毒。
rule: version == "0.6.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9052
  - https://huntr.com/bounties/ea75728f-4efe-4a3d-9f53-33f2c908e9f8