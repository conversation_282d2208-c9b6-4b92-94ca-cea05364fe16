info:
  name: vllm
  cve: CVE-2025-30202
  summary: 通过多节点vLLM部署中的ZeroMQ数据泄露
  details: |
    在多节点vLLM部署中，vLLM使用ZeroMQ进行一些多节点通信。主vLLM主机打开一个`XPUB` ZeroMQ套接字并将其绑定到所有接口。虽然套接字始终为多节点部署打开，但仅在跨多个主机进行张量并行性时使用。
    任何具有此主机网络访问权限的客户端都可以连接到此`XPUB`套接字，除非其端口被防火墙阻止。一旦连接，这些任意客户端将接收到广播到所有辅助vLLM主机的所有相同数据。这些数据是内部vLLM状态信息，对攻击者没有用处。
    通过可能多次连接到此套接字并且不读取发布到它们的数据，攻击者还可以通过减慢或可能阻止发布者来导致服务拒绝。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到vllm>=0.8.5
    2. 确保只有其他vLLM主机能够连接到用于`XPUB`套接字的TCP端口。
    3. 如果不需要跨多个主机进行张量并行性，则在防火墙上阻止`XPUB`套接字使用的端口。
rule: version >= "0.5.2" && version < "0.8.5"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-9f8f-2vmf-885j
  - https://nvd.nist.gov/vuln/detail/CVE-2025-30202
  - https://github.com/vllm-project/vllm/pull/17197
  - https://github.com/vllm-project/vllm/pull/6183
  - https://github.com/vllm-project/vllm/commit/a0304dc504c85f421d38ef47c64f83046a13641c
  - https://github.com/vllm-project/vllm