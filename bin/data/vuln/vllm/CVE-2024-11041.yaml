info:
  name: vllm
  cve: CVE-2024-11041
  summary: vllm-project v0.6.2 中通过 MessageQueue.dequeue() 的远程代码执行漏洞
  details: |
    vllm-project v0.6.2 的 MessageQueue.dequeue() API 函数中的漏洞源于使用 pickle.loads 直接解析接收到的套接字。
    这允许攻击者向 MessageQueue 发送恶意有效载荷，从而在受害者的机器上执行任意代码。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: 严重
  security_advise: |
    1. 立即升级到解决此漏洞的 vllm-project 版本。
    2. 避免使用 pickle.loads 解析套接字数据；考虑用于数据反序列化的其他更安全的方法。
    3. 对通过 MessageQueue 接收到的所有数据实施严格的输入验证和消毒。
rule: version == "0.6.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11041
  - https://huntr.com/bounties/00136195-11e0-4ad0-98d5-72db066e867f