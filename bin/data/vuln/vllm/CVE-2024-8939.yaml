info:
  name: vllm
  cve: CVE-2024-8939
  summary: vLLM 通过 best_of 参数的拒绝服务漏洞
  details: 在 ilab 模型服务组件中发现了一个漏洞，其中 vllm JSON Web API 对 best_of 参数的处理不当可能导致拒绝服务（DoS）。用于基于 LLM 的句子或聊天完成的 API 接受一个 best_of 参数以从多个选项中返回最佳完成。当此参数设置为大值时，API 无法正确处理超时或资源耗尽，允许攻击者通过消耗过多的系统资源导致 DoS。这会导致 API 无响应，阻止合法用户访问服务。
  cvss: CVSS:3.1/AV:L/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: 升级到 vllm >= "0.5.0.post2" 以解决此问题，或者通过修改 API 代码来处理 best_of 参数的超时和资源耗尽情况，以防止潜在的漏洞利用。
rule: version < "0.5.0.post2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8939
  - https://github.com/vllm-project/vllm/issues/6137
  - https://access.redhat.com/security/cve/CVE-2024-8939
  - https://bugzilla.redhat.com/show_bug.cgi?id=2312782
  - https://github.com/vllm-project/vllm