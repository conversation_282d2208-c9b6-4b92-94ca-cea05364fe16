info:
  name: vllm
  cve: CVE-2025-29770
  summary: vLLM 通过 outlines 无界磁盘缓存导致拒绝服务
  details: |
    outlines 库被 vLLM 用于结构化输出支持，该库在本地文件系统上包含一个用于编译语法的可选缓存。此缓存默认在 vLLM 中启用，通过发送具有唯一模式的短解码请求流可以利用此缓存，导致缓存不受控制地增长，如果文件系统空间耗尽，可能会导致拒绝服务（DoS）。此问题仅影响 vLLM 的 V0 引擎。
    受影响的代码: [vllm/model_executor/guided_decoding/outlines_logits_processors.py](https://github.com/vllm-project/vllm/blob/53be4a863486d02bd96a59c674bbec23eec508f6/vllm/model_executor/guided_decoding/outlines_logits_processors.py)
    注意: 即使 vLLM 默认配置为使用不同的后端，仍然可以使用 `guided_decoding_backend` 键在每个请求的基础上选择 outlines。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. 升级到 vLLM 版本 0.8.0 或更高版本。
    2. 如果使用 outlines 缓存是必要的，将 `VLLM_V0_USE_OUTLINES_CACHE` 环境变量设置为 `1`。
    3. 防止对 OpenAI 兼容 API 服务器的不受信任访问。
rule: version >= "0" && version < "0.8.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-mgrm-fgjv-mhv8
  - https://github.com/vllm-project/vllm/pull/14837
  - https://github.com/vllm-project/vllm/blob/53be4a863486d02bd96a59c674bbec23eec508f6/vllm/model_executor/guided_decoding/outlines_logits_processors.py