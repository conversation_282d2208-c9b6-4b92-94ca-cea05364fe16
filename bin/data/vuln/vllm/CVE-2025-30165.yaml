info:
  name: vllm
  cve: CVE-2025-30165
  summary: vLLM多节点集群配置中的远程代码执行漏洞
  details: |
    ### 受影响的环境
    请注意，此问题仅影响V0引擎，该引擎自v0.8.0起默认关闭。此外，此问题仅适用于在多个主机上使用张量并行性的部署，我们预计这不是常见的部署模式。
    由于V0自v0.8.0起默认关闭且修复相当侵入性，我们决定不修复此问题。相反，我们建议用户确保其环境处于安全网络中，以防使用此模式。
    V1引擎不受此问题影响。
    ### 影响
    在使用V0引擎的多节点vLLM部署中，vLLM使用ZeroMQ进行一些多节点通信。辅助vLLM主机打开一个`SUB` ZeroMQ套接字并连接到主vLLM主机上的`XPUB`套接字。
    当在此`SUB`套接字上接收数据时，它通过`pickle`反序列化。这是不安全的，因为它可以被滥用以在远程机器上执行代码。
    由于漏洞存在于连接到主vLLM主机的客户端中，因此此漏洞可作为升级点。如果主vLLM主机受到损害，则可以使用此漏洞来损害vLLM部署中的其他主机。
    攻击者还可以使用其他手段利用漏洞，而无需访问主vLLM主机。一个示例是使用ARP缓存中毒将流量重定向到用于传递在目标机器上执行的任意代码的有效负载的恶意端点。
  cvss: CVSS:3.1/AV:A/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: 高
  security_advise: |
    1. 确保您的环境处于安全网络中。
    2. 避免在多节点部署中使用V0引擎。
    3. 升级到不再支持V0引擎或已修复的版本。
rule: version >= "0.5.2" && version <= "0.8.5.post1"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-9pcc-gvx5-r5wm
  - https://nvd.nist.gov/vuln/detail/CVE-2025-30165
  - https://github.com/vllm-project/vllm
  - https://github.com/vllm-project/vllm/blob/c21b99b91241409c2fdf9f3f8c542e8748b317be/vllm/distributed/device_communicators/shm_broadcast.py#L295-L301
  - https://github.com/vllm-project/vllm/blob/c21b99b91241409c2fdf9f3f8c542e8748b317be/vllm/distributed/device_communicators/shm_broadcast.py#L468-L470