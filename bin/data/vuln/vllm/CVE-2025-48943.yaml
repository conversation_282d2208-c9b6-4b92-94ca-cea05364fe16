info:
  name: vllm
  cve: CVE-2025-48943
  summary: vLLM允许客户端使用无效的正则表达式使openai服务器崩溃
  details: |
    一个拒绝服务漏洞导致如果在使用结构化输出时提供了无效的正则表达式，vLLM服务器会崩溃。此漏洞类似于[GHSA-6qc9-v4r8-22xg](https://github.com/vllm-project/vllm/security/advisories/GHSA-6qc9-v4r8-22xg)，但针对的是正则表达式而不是JSON模式。
    更多详细信息的议题：[vllm issue #17313](https://github.com/vllm-project/vllm/issues/17313)
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. 升级到vllm >= 0.9.0
    2. 审查并强制执行正则表达式模式的输入验证，以防止服务器崩溃
rule: version >= "0.8.0" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-9hcf-v7m4-6m2j
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48943
  - https://github.com/vllm-project/vllm/issues/17313
  - https://github.com/vllm-project/vllm/pull/17623
  - https://github.com/vllm-project/vllm/commit/08bf7840780980c7568c573c70a6a8db94fd45ff
  - https://github.com/vllm-project/vllm