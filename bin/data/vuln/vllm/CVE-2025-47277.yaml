info:
  name: vllm
  cve: CVE-2025-47277
  summary: vLLM分布式节点模式下通过PyNcclPipe通信服务的远程代码执行漏洞
  details: |
   腾讯朱雀实验室发现：vLLM支持分布式节点之间的点对点通信。由于对客户端提供的序列化数据处理不当，PyNcclPipe通信服务存在远程代码执行漏洞，允许攻击者获得服务器控制权限。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 立即更新vLLM至最新版本。
    2. 审查并修补反序列化过程，确保它只处理可信数据。
    3. 实施网络级保护，限制对PyNcclPipe通信服务的访问。
rule: version <= "v0.8.5"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-47277