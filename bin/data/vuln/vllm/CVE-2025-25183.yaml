info:
  name: vllm
  cve: CVE-2025-25183
  summary: vLLM 使用 Python 3.12 内置 hash() 导致前缀缓存中的可预测哈希碰撞
  details: |
    恶意制作的提示可能导致哈希碰撞，从而导致前缀缓存的重复使用，这可能会干扰后续响应并导致意外行为。
    vLLM 的前缀缓存利用了 Python 的内置 hash() 函数。从 Python 3.12 开始，hash(None) 的行为已更改为一个可预测的常量值。这使得某人尝试利用哈希碰撞变得更加可行。
    碰撞的影响将是使用使用不同内容生成的缓存。鉴于对正在使用的提示的了解以及可预测的哈希行为，某人可能会故意使用已知与正在使用的另一个提示碰撞的提示来填充缓存。
    我们通过在 vllm 中初始化一个不再恒定且可预测的哈希值来解决这个问题。每次运行 vllm 时，它都会有所不同。这恢复了我们在 Python 3.12 之前的版本中获得的行为。
    使用不太容易发生碰撞的哈希算法（例如 sha256）将是避免碰撞可能性的最佳方法。然而，这将对性能和内存占用产生影响。尽管哈希碰撞仍可能发生，但它们不再那么容易预测。
    为了给出碰撞可能性的大致概念，对于随机生成的哈希值（假设 Python 内置的哈希生成是均匀分布的），在缓存容量为 50,000 条消息且平均提示长度为 300 的情况下，平均每 1 万亿次请求会发生一次碰撞。
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:R/S:U/C:N/I:L/A:N
  severity: LOW
  security_advise: 升级到 vllm >= 0.7.2 版本以解决此问题，该版本通过初始化不再恒定且可预测的哈希值来防止哈希碰撞。或者，可以考虑使用不太容易发生碰撞的哈希算法，如 sha256，但这可能会对性能和内存占用产生影响。
rule: version < "0.7.2"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-rm76-4mrf-v9r8
  - https://nvd.nist.gov/vuln/detail/CVE-2025-25183
  - https://github.com/python/cpython/pull/99541
  - https://github.com/vllm-project/vllm/pull/12621
  - https://github.com/python/cpython/commit/432117cd1f59c76d97da2eaff55a7d758301dbc7
  - https://github.com/vllm-project/vllm/commit/73b35cca7f3745d07d439c197768b25d88b6ab7f
  - https://github.com/vllm-project/vllm