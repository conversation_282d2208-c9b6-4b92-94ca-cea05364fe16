info:
  name: vllm
  cve: CVE-2025-48887
  summary: vLLM 在 `pythonic_tool_parser.py` 中存在正则表达式拒绝服务（ReDoS）漏洞
  details: |
    vLLM 项目的文件[`vllm/entrypoints/openai/tool_parsers/pythonic_tool_parser.py`](https://github.com/vllm-project/vllm/blob/main/vllm/entrypoints/openai/tool_parsers/pythonic_tool_parser.py)中存在正则表达式拒绝服务（ReDoS）漏洞。根本原因是用于工具调用检测的正则表达式高度复杂且嵌套，可被攻击者利用从而导致严重的性能下降或使服务不可用。
    以下正则表达式用于匹配工具/函数调用模式：
    ```
    r\"\\[([a-zA-Z]+\\w*\\(([a-zA-Z]+\\w*=.*,\\s*)*([a-zA-Z]+\\w*=.*\\s)?\\),\\s*)*([a-zA-Z]+\\w*\\(([a-zA-Z]+\\w*=.*,\\s*)*([a-zA-Z]+\\w*=.*\\s*)?\\)\\s*)+\\]\"\"
    ```
    此模式包含多个嵌套量词（`*`、`+`）、可选组和内部重复，这使其容易受到灾难性回溯的影响。
    **攻击示例：**
    恶意输入例如
    ```
    [A(A=\t)A(A=,\t\t)A(A=,\t\t)A(A=,\t\t)...（重复数十次）...]
    ```
    或者
    ```
    "[A(A=\" + \"\\t)A(A=,\\t\" * 重复
    ```
    可能导致正则表达式引擎随输入长度呈指数级消耗 CPU，有效地冻结或使服务器崩溃（DoS）。
    **概念验证：**
    一个 Python 脚本表明，与上述正则表达式匹配这样制作的字符串会导致指数级的时间复杂度。即使是适度的输入长度也可能使系统停止。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到 vllm>=0.9.0
    2. 审查并简化用于工具调用检测的正则表达式，以防止灾难性回溯
    3. 监控 CPU 使用情况并实施速率限制，以减轻潜在 DoS 攻击的影响
rule: version >= "0.6.4" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-w6q7-j642-7c25
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48887
  - https://github.com/vllm-project/vllm/pull/18454
  - https://github.com/vllm-project/vllm/commit/4fc1bf813ad80172c1db31264beaef7d93fe0601
  - https://github.com/vllm-project/vllm