info:
  name: vllm
  cve: CVE-2025-46560
  summary: 输入令牌处理中的二次时间复杂度导致拒绝服务
  details: |
    ### 摘要
    在多模态分词器的输入预处理逻辑中识别出一个关键性能漏洞。由于低效的列表连接操作，算法表现出二次时间复杂度（O(n²)），允许恶意行为者通过特制的输入触发资源耗尽。
    ### 详情
    受影响组件：input_processor_for_phi4mm 函数。
    https://github.com/vllm-project/vllm/blob/8cac35ba435906fb7eb07e44fe1a8c26e8744f4e/vllm/model_executor/models/phi4mm.py#L1182-L1197
    代码使用 input_ids = input_ids[:i] + tokens + input_ids[i+1:] 原地修改 input_ids 列表。每次连接操作都会复制整个列表，导致每次替换进行 O(n) 次操作。对于扩展为 m 个令牌的 k 个占位符，总时间变为 O(kmn)，在最坏情况下近似为 O(n²)。
    ### 证明概念
    测试数据显示时间呈指数增长：
    ```python
    test_cases = [100, 200, 400, 800, 1600, 3200, 6400]
    run_times = [0.002, 0.007, 0.028, 0.136, 0.616, 2.707, 11.854]  # 秒
    ```
    输入大小加倍会使运行时间增加约 4 倍（与 O(n²) 一致）。
    ### 影响
    拒绝服务（DoS）：攻击者可以提交包含许多占位符的输入（例如，10,000 <|audio_1|> 令牌），导致 CPU/内存耗尽。
    示例：10,000 个占位符 → 约 1 亿次操作。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到 vllm>=0.8.5
    2. 提前计算所有占位符位置和扩展长度。
    3. 用单个预分配数组替换动态列表连接。
    ```python
    # O(n) 解决方案的伪代码
    new_input_ids = []
    for token in input_ids:
        if token 是占位符:
            new_input_ids.extend([token] * 预计算长度)
        else:
            new_input_ids.append(token)
    ```
rule: version >= "0.8.0" && version < "0.8.5"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-vc6m-hm49-g9qg
  - https://nvd.nist.gov/vuln/detail/CVE-2025-46560
  - https://github.com/vllm-project/vllm
  - https://github.com/vllm-project/vllm/blob/8cac35ba435906fb7eb07e44fe1a8c26e8744f4e/vllm/model_executor/models/phi4mm.py#L1182-L1197