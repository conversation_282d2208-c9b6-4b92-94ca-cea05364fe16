info:
  name: vllm
  cve: CVE-2024-9053
  summary: vllm-project中由于不安全的pickle反序列化导致的远程代码执行漏洞
  details: |
    vllm-project版本0.6.0中的漏洞影响AsyncEngineRPCServer() RPC服务器入口点。
    核心功能run_server_loop()调用函数_make_handler_coro()，该函数直接在接收到的消息上使用cloudpickle.loads()，而没有任何消毒。这可能导致通过反序列化恶意的pickle数据来执行远程代码。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到vllm-project版本0.6.1或更高版本。
    2. 对所有反序列化的数据实施输入验证和消毒。
    3. 考虑使用不太容易被滥用的替代序列化方法，例如带有适当模式验证的JSON。
rule: version < "0.6.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9053
  - https://huntr.com/bounties/75a544f3-34a3-4da0-b5a3-1495cb031e09