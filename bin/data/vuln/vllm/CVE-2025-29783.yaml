info:
  name: vllm
  cve: CVE-2025-29783
  summary: vLLM 允许通过 Mooncake 集成进行远程代码执行
  details: |
    当 vLLM 配置为使用 Mooncake 时，直接通过 ZMQ/TCP 在所有网络接口上暴露的不安全反序列化将允许攻击者在分布式主机上执行远程代码。
    ### 详情
    1. Pickle 反序列化漏洞已有[详细文档](https://docs.python.org/3/library/pickle.html)。
    2. [mooncake 管道](https://github.com/vllm-project/vllm/blob/9bebc9512f9340e94579b9bd69cfdc452c4d5bb0/vllm/distributed/kv_transfer/kv_pipe/mooncake_pipe.py#L206)通过网络暴露（设计上为了在分布式环境中实现分散预填充）使用 ZMQ over TCP，极大地增加了可利用性。此外，mooncake 集成打开这些套接字监听主机上的所有接口，这意味着它不能配置为仅使用私有、受信任的网络。
    3. 根本问题是[`recv_tensor()`](https://github.com/vllm-project/vllm/blob/9bebc9512f9340e94579b9bd69cfdc452c4d5bb0/vllm/distributed/kv_transfer/kv_pipe/mooncake_pipe.py#L257)调用[`_recv_impl`](https://github.com/vllm-project/vllm/blob/9bebc9512f9340e94579b9bd69cfdc452c4d5bb0/vllm/distributed/kv_transfer/kv_pipe/mooncake_pipe.py#L244)，它将原始网络字节传递给`pickle.loads()`。此外，似乎没有任何控制（网络、身份验证等）来防止任意用户向受影响的服务发送此有效负载。
    ### 影响
    这是一个远程代码执行漏洞，影响任何使用 Mooncake 在分布式主机之间分发 KV 的部署。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到 vllm >= 0.8.0
    2. 审查并限制对 Mooncake 集成的网络访问
    3. 在 Mooncake 管道周围实施额外的安全控制以防止未经授权的访问
rule: version < "0.8.0"
references:
  - https://github.com/vllm-project/vllm/security/advisories/GHSA-x3m8-f7g5-qhm7
  - https://github.com/vllm-project/vllm/pull/14228
  - https://github.com/vllm-project/vllm/commit/288ca110f68d23909728627d3100e5a8db820aa2
  - https://github.com/vllm-project/vllm