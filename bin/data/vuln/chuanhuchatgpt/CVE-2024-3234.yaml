info:
  name: chuanhuchatgpt
  cve: CVE-2024-3234
  summary: chuanhuchatgpt应用程序的路径遍历漏洞
  details: gaizhenbiao/chuanhuchatgpt应用程序由于使用了过时的gradio组件而容易受到路径遍历攻击。该应用程序旨在限制用户访问`web_assets`文件夹内的资源。然而，它所使用的过时版本的gradio容易受到路径遍历攻击，如CVE-2023-51449中所识别。此漏洞允许未经授权的用户绕过预期的限制并访问敏感文件，例如包含API密钥的`config.json`。该问题影响在20240305发布修复版本之前的chuanhuchatgpt的最新版本。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到修复了此问题的chuanhuchatgpt版本或更新gradio组件以解决路径遍历漏洞。
rule: version < "20240305"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3234
 - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/6b8f7db347b390f6f8bd07ea2a4ef01a47382f00
 - https://huntr.com/bounties/277e3ff0-5878-4809-a4b9-73cdbb70dc9f