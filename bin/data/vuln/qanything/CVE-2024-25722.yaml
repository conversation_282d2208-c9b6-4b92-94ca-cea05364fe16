info:
  name: qanything
  cve: CVE-2024-25722
  summary: QAnything 在 1.2.0 之前版本的 MySQL 客户端存在 SQL 注入漏洞
  details: qanything_kernel/connector/database/mysql/mysql_client.py 在 qanything.ai QAnything 1.2.0 之前版本中允许 SQL 注入。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 QAnything 1.2.0 或更高版本以解决此漏洞。
rule: version < "1.2.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-25722
  - https://github.com/netease-youdao/QAnything/commit/35753b892c2c4361b318d68dfa3e251c85ce889c
  - https://github.com/netease-youdao/QAnything/compare/v1.1.1...v1.2.0