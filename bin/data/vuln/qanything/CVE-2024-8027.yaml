info:
  name: qanything
  cve: CVE-2024-8027
  summary: 网易有道/QAnything中的存储型跨站脚本（XSS）漏洞
  details: |
    网易有道/QAnything中存在存储型跨站脚本（XSS）漏洞。
    攻击者可以将恶意知识文件上传到知识库，这将在用户聊天过程中触发XSS攻击。此漏洞影响修复前的所有版本。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. 更新到包含此漏洞修复的最新版本的QAnything。
    2. 实施输入验证，以防止恶意脚本被上传到知识库。
    3. 定期监控和审核知识库，以发现任何未经授权或恶意内容的迹象。
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8027
  - https://huntr.com/bounties/cf75f024-3d64-416d-adfe-c4255d7c3f34