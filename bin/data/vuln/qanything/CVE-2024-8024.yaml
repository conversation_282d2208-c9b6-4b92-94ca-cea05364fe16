info:
  name: qanything
  cve: CVE-2024-8024
  summary: 网易有道/qanything中的CORS配置错误漏洞
  details: |
    网易有道/qanything版本1.4.1中存在CORS配置错误漏洞。
    此漏洞允许攻击者绕过同源策略，
    可能导致敏感信息的泄露。
    正确实施限制性的CORS策略对于防止此类安全问题至关重要。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. 升级至qanything >= 1.4.2或修复了CORS配置错误的后续版本。
    2. 实施严格的CORS策略，仅允许受信任的来源。
    3. 定期审查和更新CORS配置，确保其符合安全最佳实践。
rule: version < "1.4.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8024
  - https://huntr.com/bounties/bda53fab-88aa-4e03-8d9d-4cf50a98ffc7