info:
  name: qanything
  cve: CVE-2024-12866
  summary: 网易有道/qanything中的本地文件包含漏洞
  details: |
    网易有道/qanything版本v2.0.0中存在本地文件包含漏洞。该漏洞允许攻击者读取文件系统上的任意文件，通过获取私有的SSH密钥、阅读私有文件、源代码和配置文件，可能导致远程代码执行。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: 高
  security_advise: |
    1. 升级至qanything >= v2.0.1（假设此版本或更高版本中有修复）
    2. 实施严格的文件包含检查以防止任意文件读取
    3. 定期审计和更新依赖项以修补已知漏洞
rule: version < "2.0.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12866
  - https://huntr.com/bounties/c23da7c7-a226-40a2-83db-6a8ab1b2ef64