info:
  name: qanything
  cve: CVE-2024-12864
  summary: 文件上传功能中的拒绝服务（DoS）漏洞
  details: |
    在netease-youdao/qanything版本v2.0.0的文件上传功能中发现了一个拒绝服务（DoS）漏洞。该漏洞是由于在文件上传请求中不正确处理带有大文件名的表单数据造成的。攻击者可以通过发送大文件名来利用此漏洞，导致服务器不堪重负，无法为合法用户提供服务。此攻击不需要身份验证，使其具有很高的可扩展性，并增加了被利用的风险。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级至qanything >= v2.0.1
    2. 在文件上传请求中实施服务器端对文件名大小的验证
    3. 监控服务器日志以查找与文件上传相关的异常活动
rule: version < "2.0.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12864
  - https://huntr.com/bounties/365c3b9a-180c-4bb5-98d8-dbd78d93fcb7