info:
  name: qanything
  cve: CVE-2024-8026
  summary: 网易有道/qanything 后端 API 的 CSRF 漏洞
  details: |
    网易有道/qanything 的后端 API 存在跨站请求伪造（CSRF）漏洞，自提交 d9ab8bc 起存在。
    后端服务器具有过于宽松的 CORS 头，允许所有跨域调用。此漏洞影响所有后端端点，
    能够执行诸如创建、上传、列出、删除文件以及管理知识库等操作。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. 将 CORS 头限制为仅允许受信任的来源。
    2. 为所有状态更改请求实施 CSRF 令牌。
    3. 定期更新和修补后端 API 以解决安全漏洞。
rule: version < "d9ab8bc"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8026
  - https://huntr.com/bounties/e57f1e32-0fe5-4997-926c-587461aa6274