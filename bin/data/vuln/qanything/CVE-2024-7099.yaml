info:
  name: qanything
  cve: CVE-2024-7099
  summary: qanything SQL注入漏洞
  details: netease-youdao/qanything版本1.4.1存在漏洞，其中从用户输入获取的不安全数据被拼接在SQL查询中，导致SQL注入。受影响的函数包括`get_knowledge_base_name`、`from_status_to_status`、`delete_files`和`get_file_by_status`。攻击者可以利用此漏洞执行任意SQL查询，可能从数据库中窃取信息。该问题在版本1.4.2中得到修复。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到qanything版本1.4.2或更高版本以解决此问题。
rule: version < "1.4.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-7099
 - https://github.com/netease-youdao/qanything/commit/a87354f09d93e95350fb45eb343dc75454387554
 - https://huntr.com/bounties/bc98983e-06cc-4a4b-be01-67e5010cb2c1