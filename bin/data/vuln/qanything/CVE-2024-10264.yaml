info:
  name: qanything
  cve: CVE-2024-10264
  summary: 网易有道/qanything中的HTTP请求走私漏洞
  details: |
    该漏洞允许攻击者利用代理和服务器之间对HTTP请求解释的不一致性。这可能导致未经授权的访问、绕过安全控制、会话劫持、数据泄露，以及潜在的任意代码执行。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到网易有道/qanything版本1.4.2或更高版本。
    2. 实施严格的HTTP请求验证以防止走私攻击。
    3. 定期更新和修补所有软件组件以减轻潜在漏洞。
rule: version < "1.4.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10264
  - https://huntr.com/bounties/988247d5-fd60-4d85-845a-e867d62c0d02