info:
  name: ComfyUI-Manager
  cve: CVE-2024-21574
  summary: ComfyUI 安装自定义节点时的远程代码执行漏洞
  details: 该问题源于发送到 /customnode/install 端点的 POST 请求中缺少对 pip 字段的验证，该端点用于安装由扩展添加到服务器的自定义节点。这允许攻击者制作一个请求，在用户控制的包或 URL 上触发 pip 安装，从而在服务器上实现远程代码执行 (RCE)。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 请立即更新 ComfyUI 至最新版本或应用官方发布的安全补丁来解决此漏洞。同时，建议检查并限制对 /customnode/install 端点的访问权限，仅允许受信任的来源进行请求。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-21574
 - https://github.com/ltdrdata/ComfyUI-Manager/commit/ffc095a3e5acc1c404773a0510e6d055a6a72b0e
 - https://github.com/ltdrdata/ComfyUI-Manager/blob/ffc095a3e5acc1c404773a0510e6d055a6a72b0e/glob/manager_server.py#L798