info:
  name: llamafactory
  cve: CVE-2024-52803
  summary: LLama Factory 远程操作系统命令注入漏洞
  details: |
    在LLama Factory训练过程中发现了一个严重的远程操作系统命令注入漏洞。此漏洞源于对用户输入的不当处理，允许恶意行为者在主机系统上执行任意操作系统命令。该问题是由于不安全地使用`Popen`函数并启用`shell=True`，以及未对用户输入进行消毒造成的。需要立即修复以降低风险。
    受影响版本：LLama Factory版本**<=0.9.0**。
    影响：利用此漏洞，攻击者可以执行服务器上的任意操作系统命令，可能泄露敏感数据或提升权限，在系统中部署恶意软件或创建持久后门。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 避免在`Popen`中使用`shell=True`。相反，将命令及其参数作为列表传递。这可以防止用户输入被作为shell命令的一部分执行。
rule: version <= "0.9.0"
references:
  - https://github.com/hiyouga/LLaMA-Factory/security/advisories/GHSA-hj3w-wrh4-44vp
  - https://github.com/hiyouga/LLaMA-Factory/commit/b3aa80d54a67da45e9e237e349486fb9c162b2ac
  - https://github.com/hiyouga/LLaMA-Factory