info:
  name: llamafactory
  cve: CVE-2025-46567
  summary: LLaMA-Factory 允许通过 Ilamafy_baichuan2.py 中的不安全反序列化执行任意代码
  details: |
    LLaMA-Factory 项目的 `llamafy_baichuan2.py` 脚本存在一个关键漏洞。该脚本使用 `torch.load()` 对来自输入目录的用户提供的 `.bin` 文件执行不安全的反序列化，允许攻击者在反序列化期间执行任意命令。
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:U/C:H/I:L/A:L
  severity: 危急
  security_advise: |
    1. 用更安全的替代品（如 `safetensors`）替换 `torch.load()`。
    2. 在反序列化之前验证并白名单文件类型。
    3. 要求对加载的文件进行校验和验证。
    4. 避免使用不受信任的 `.bin` 文件运行脚本。
    5. 使用容器或虚拟机隔离脚本执行。
rule: version < "0.9.2"
references:
  - https://github.com/hiyouga/LLaMA-Factory/security/advisories/GHSA-f2f7-gj54-6vpv
  - https://nvd.nist.gov/vuln/detail/CVE-2025-46567
  - https://github.com/hiyouga/LLaMA-Factory/commit/2989d39239d2f46e584c1e1180ba46b9768afb2a
  - https://github.com/hiyouga/LLaMA-Factory/blob/main/scripts/convert_ckpt/llamafy_baichuan2.py#L35