info:
  name: ray
  cve: CVE-2023-6020
  summary: Ray 缺失授权漏洞
  details: Ray 的 /static/ 目录存在本地文件包含（LFI）漏洞，允许攻击者在未经身份验证的情况下读取服务器上的任意文件。此问题已在版本 2.8.1+ 中修复。Ray 维护者的回应可以在此处找到：https://www.anyscale.com/blog/update-on-ray-cves-cve-2023-6019-cve-2023-6020-cve-2023-6021-cve-2023-48022-cve-2023-48023
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
  severity: CRITICAL
  security_advise: 升级到 ray >= 2.8.1 版本以解决此问题。
rule: version < "2.8.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6020
  - https://github.com/ray-project/ray
  - https://github.com/ray-project/ray/releases/tag/ray-2.8.1
  - https://huntr.com/bounties/83dd8619-6dc3-4c98-8f1b-e620fedcd1f6
  - https://www.anyscale.com/blog/update-on-ray-cves-cve-2023-6019-cve-2023-6020-cve-2023-6021-cve-2023-48022-cve-2023-48023