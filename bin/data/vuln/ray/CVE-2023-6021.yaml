info:
  name: ray
  cve: CVE-2023-6021
  summary: Ray路径遍历漏洞
  details: |
    Ray的日志API端点存在本地文件包含（LFI）漏洞，允许攻击者在未经身份验证的情况下读取服务器上的任何文件。该问题已在版本2.8.1+中修复。Ray维护者的回应可以在此处找到：https://www.anyscale.com/blog/update-on-ray-cves-cve-2023-6019-cve-2023-6020-cve-2023-6021-cve-2023-48022-cve-2023-48023
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
  severity: CRITICAL
  security_advise: 升级到ray>=2.8.1以解决此问题。
rule: version < "2.8.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6021
 - https://github.com/ray-project/ray
 - https://github.com/ray-project/ray/releases/tag/ray-2.8.1
 - https://huntr.com/bounties/5039c045-f986-4cbc-81ac-370fe4b0d3f8
 - https://www.anyscale.com/blog/update-on-ray-cves-cve-2023-6019-cve-2023-6020-cve-2023-6021-cve-2023-48022-cve-2023-48023