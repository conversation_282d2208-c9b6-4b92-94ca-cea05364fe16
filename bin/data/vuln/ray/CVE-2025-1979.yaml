info:
  name: ray
  cve: CVE-2025-1979
  summary: 在 Ray 2.43.0 之前的版本中记录 Redis 密码
  details: |
    在 2.43.0 之前的 Ray 包版本中，存在将敏感信息插入日志文件的漏洞，其中 Redis 密码被记录在标准日志中。如果 Redis 密码作为参数传递，它将被记录下来，并可能泄露密码。
    这只有在以下情况下才可被利用：
    1) 启用了日志记录；
    2) Redis 使用密码认证；
    3) 这些日志可以被能够访问该 Redis 实例的攻击者获取。
    **注意：**
    建议任何在这种配置下运行的人应更新到 Ray 的最新版本，然后轮换他们的 Redis 密码。
  cvss: CVSS:3.1/AV:L/AC:H/PR:L/UI:N/S:C/C:H/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. 更新到 Ray 的最新版本（2.43.0 或更高）。
    2. 如果 Redis 密码已暴露，则轮换 Redis 密码。
    3. 确保包含敏感信息的日志对未经授权的用户不可访问。
rule: version < "2.43.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1979
  - https://github.com/ray-project/ray/issues/50266
  - https://github.com/ray-project/ray/pull/50409
  - https://github.com/ray-project/ray/commit/64a2e4010522d60b90c389634f24df77b603d85d
  - https://security.snyk.io/vuln/SNYK-PYTHON-RAY-8745212