info:
  name: ray
  cve: CVE-2023-6019
  summary: Ray OS 命令注入漏洞
  details: Ray 的 cpu_profile URL 参数存在命令注入漏洞，允许攻击者在未经认证的情况下远程执行系统命令。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 ray >= 2.8.1 版本以修复此漏洞。
rule: version < "2.8.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6019
 - https://github.com/ray-project/ray
 - https://github.com/ray-project/ray/releases/tag/ray-2.8.1
 - https://huntr.com/bounties/d0290f3c-b302-4161-89f2-c13bb28b4cfe
 - https://www.anyscale.com/blog/update-on-ray-cves-cve-2023-6019-cve-2023-6020-cve-2023-6021-cve-2023-48022-cve-2023-48023