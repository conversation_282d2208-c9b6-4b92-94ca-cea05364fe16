info:
  name: ray
  cve: CVE-2024-57000
  summary: Ray 中的命令注入漏洞
  details: |
    在 Anyscale Inc Ray 版本 v.2.9.3 到 v.2.40.0 之间存在一个问题，允许远程攻击者通过特制的脚本执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 ray 版本大于 2.40.0 以解决此问题。
rule: version >= "2.9.3" && version <= "2.40.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-57000
 - https://github.com/honysyang/Ray.git
 - https://github.com/ray-project/ray