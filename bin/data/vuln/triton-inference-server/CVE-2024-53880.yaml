info:
  name: triton-inference-server
  cve: CVE-2024-53880
  summary: NVIDIA Triton Inference Server模型加载API的漏洞
  details: |
    NVIDIA Triton Inference Server在模型加载API中存在一个漏洞，用户可以通过加载超大文件大小的模型导致内部变量发生整数溢出或环绕错误。成功利用此漏洞可能导致服务拒绝。
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: NVIDIA建议更新至最新版本的Triton Inference Server以修复此漏洞，并确保在部署模型时验证文件大小以防止潜在的整数溢出攻击。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-53880
 - https://nvidia.custhelp.com/app/answers/detail/a_id/5612