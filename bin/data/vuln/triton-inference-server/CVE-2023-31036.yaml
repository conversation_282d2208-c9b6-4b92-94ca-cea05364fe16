info:
  name: triton-inference-server
  cve: CVE-2023-31036
  summary: NVIDIA Triton推理服务器路径遍历漏洞
  details: |
    NVIDIA Triton推理服务器（适用于Linux和Windows）存在一个漏洞，当使用非默认命令行选项--model-control explicit启动时，攻击者可以利用模型加载API造成相对路径遍历。成功利用此漏洞可能导致代码执行、拒绝服务、权限提升、信息泄露和数据篡改。
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 避免使用--model-control explicit命令行选项启动Triton服务器，或升级到修复了此漏洞的版本。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-31036
 - https://nvidia.custhelp.com/app/answers/detail/a_id/5509