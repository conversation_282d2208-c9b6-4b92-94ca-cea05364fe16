info:
  name: anythingllm
  cve: CVE-2024-0455
  summary: AnythingLLM Web Scraper 漏洞
  details: 包含 AnythingLLM 的网络抓取器意味着任何具有适当授权级别（经理、管理员以及单用户时）的用户都可以输入特定的 URL，该 URL 仅在请求来自 EC2 实例内部时解析。这将允许用户查看其特定实例的连接/秘密凭据，并能够管理它，无论谁部署了它。用户必须事先了解目标实例部署的托管基础设施，但如果发送，如果位于 EC2 上且未为其设置配置正确的 `iptable` 或防火墙规则，则会解析。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 更新 AnythingLLM 至最新版本或配置适当的 `iptable` 或防火墙规则以防止未经授权的访问。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-0455
 - https://github.com/mintplex-labs/anything-llm/commit/b2b2c2afe15c48952d57b4d01e7108f9515c5f55
 - https://huntr.com/bounties/07d83b49-7ebb-40d2-83fc-78381e3c5c9c