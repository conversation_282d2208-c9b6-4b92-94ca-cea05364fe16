info:
  name: anythingllm
  cve: CVE-2024-13060
  summary: AnythingLLM Docker中的漏洞允许未经授权的访问用户头像。
  details: |
    AnythingLLM Docker版本1.3.1中的一个漏洞允许拥有“默认”权限的用户通过更改用户cookie中的“id”参数来访问其他用户的头像。这个问题存在于1.3.1版本之前的所有版本中。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到AnythingLLM Docker版本1.3.1或更高版本。
    2. 对用户cookie实施严格的验证，以防止未经授权的访问。
rule: version < "1.3.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-13060
  - https://github.com/mintplex-labs/anything-llm/commit/696af19c45473172ad4d3ca749281800a4d1a45a
  - https://huntr.com/bounties/98a49c90-e095-441f-900c-59d463dc8e8f