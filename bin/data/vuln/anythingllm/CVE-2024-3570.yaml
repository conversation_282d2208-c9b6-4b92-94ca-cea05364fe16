info:
  name: anythingllm
  cve: CVE-2024-3570
  summary: AnythingLLM 存储型跨站脚本（XSS）漏洞
  details: mintplex-labs/anything-llm 仓库的聊天功能存在存储型跨站脚本（XSS）漏洞，允许攻击者在用户会话上下文中执行任意 JavaScript。通过操纵 ChatBot 响应，攻击者可以注入恶意脚本代表用户执行操作，例如创建新的管理员账户或更改用户密码，从而完全接管 AnythingLLM 应用程序。该漏洞源于对用户和 ChatBot 输入的不当清理，特别是通过使用 `dangerouslySetInnerHTML`。成功利用需要说服管理员向其 AnythingLLM 实例添加恶意的 LocalAI ChatBot。
  cvss: CVSS:3.0/AV:L/AC:H/PR:H/UI:R/S:U/C:N/I:N/A:N
  severity: LOW
  security_advise: 对用户和 ChatBot 的输入进行严格清理，避免使用 `dangerouslySetInnerHTML`，及时更新相关依赖库以修复可能存在的安全问题。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3570
 - https://github.com/mintplex-labs/anything-llm/commit/a4ace56a401ffc8ce0082d7444159dfd5dc28834
 - https://huntr.com/bounties/f0eaf552-aaf3-42b6-a5df-cfecd2de15ee