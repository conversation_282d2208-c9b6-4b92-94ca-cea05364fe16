info:
  name: anythingllm
  cve: CVE-2024-0765
  summary: AnythingLLM 数据泄露漏洞
  details: 在多用户实例的AnythingLLM中，默认用户可以调用系统的`/export-data`端点，然后解压缩并读取导出的数据，从而能够窃取系统在该保存状态下的数据。这需要攻击者被授予系统的明确访问权限，但他们可以在任何角色下执行此操作。此外，下载后数据将被删除，因此不会留下数据泄露的证据。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: 确保只有授权用户能够访问系统的`/export-data`端点，并实施严格的访问控制和审计措施以防止未授权的数据导出。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-0765
 - https://github.com/mintplex-labs/anything-llm/commit/08d33cfd8fc47c5052b6ea29597c964a9da641e2
 - https://huntr.com/bounties/8978ab27-710c-44ce-bfd8-a2ea416dc786