info:
  name: anythingllm
  cve: CVE-2024-5211
  summary: anythingllm 路径遍历漏洞
  details: mintplex-labs/anything-llm 中存在路径遍历漏洞，允许管理员绕过 `normalizePath()` 函数的防御。此漏洞使管理员能够读取、删除或覆盖 'anythingllm.db' 数据库文件以及存储在 'storage' 目录中的其他文件（如内部通信密钥和 .env 秘密）。利用此漏洞可能导致应用程序受损、拒绝服务（DoS）攻击以及未经授权的管理员帐户接管。该问题源于在设置应用程序自定义徽标的过程中对用户提供的输入验证不当，可被操纵以实现任意文件读取、删除或覆盖，并通过删除应用程序运行所需的关键文件来执行 DoS 攻击。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 及时更新到修复此漏洞的最新版本或采取严格的输入验证措施以防止路径遍历攻击。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-5211
 - https://github.com/mintplex-labs/anything-llm/commit/e208074ef4c240fe03e4147ab097ec3b52b97619
 - https://huntr.com/bounties/38f282cb-7226-435e-9832-2d4a102dad4b