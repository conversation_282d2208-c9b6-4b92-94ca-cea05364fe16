info:
  name: anythingllm
  cve: CVE-2024-0759
  summary: AnythingLLM 内部网络链接抓取漏洞
  details: 如果 AnythingLLM 实例托管在内部网络上，并且攻击者被明确授予 manager 或 admin 权限级别，他们可以链接抓取内部解析的其他服务的 IP 地址，这些服务与 AnythingLLM 在同一网络上。这需要攻击者还能够猜测这些内部 IP 地址，因为无法进行 `/*` 范围内的猜测，但可以通过暴力破解实现。同一网络上的其他服务有责任不通过简单的 CuRL 进行零身份验证的完全开放和访问，因为无法设置标头或通过链接收集器访问。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: 确保 AnythingLLM 不在内部网络上托管，或者限制对 manager 或 admin 权限的访问，并加强内部服务的身份验证机制以防止未经授权的访问。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-0759
 - https://github.com/mintplex-labs/anything-llm/commit/0db6c3b2aa1787a7054ffdaba975474f122c20eb
 - https://huntr.com/bounties/9a978edd-ac94-41fc-8e3e-c35441bdd12b