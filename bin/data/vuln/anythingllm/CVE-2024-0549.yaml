info:
  name: anythingllm
  cve: CVE-2024-0549
  summary: anythingllm 存在相对路径遍历漏洞
  details: mintplex-labs/anything-llm 存在相对路径遍历漏洞，允许拥有默认角色账户的未授权攻击者删除文件系统中的文件和文件夹，包括关键的数据库文件如 'anythingllm.db'。该漏洞源于处理文件和文件夹删除请求时输入验证和规范化的不足。成功利用会导致数据完整性和可用性的受损。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: 升级到最新版本的 mintplex-labs/anything-llm 以解决此问题，确保对文件和文件夹删除请求进行严格的输入验证和规范化。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-0549
 - https://github.com/mintplex-labs/anything-llm/commit/026849df0224b6a8754f4103530bc015874def62
 - https://huntr.com/bounties/fcb4001e-0290-4b78-a2f0-91ee5d20cc72