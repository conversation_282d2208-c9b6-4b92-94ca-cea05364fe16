info:
  name: anythingllm
  cve: CVE-2024-3279
  summary: anythingllm 应用程序导入端点存在不当访问控制漏洞
  details: anythingllm 应用程序中的导入端点存在不当访问控制漏洞，允许匿名攻击者（无需应用程序帐户）导入自己的数据库文件，导致现有 `anythingllm.db` 文件被删除或伪造。通过利用此漏洞，攻击者可以向用户提供恶意数据或收集他们的信息。漏洞源于应用程序未能正确限制对数据导入功能的访问，从而允许未经授权的数据库操作。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到最新版本的 anythingllm 应用以解决此问题，并确保应用程序正确限制对数据导入功能的访问。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3279
 - https://github.com/mintplex-labs/anything-llm/commit/08d33cfd8fc47c5052b6ea29597c964a9da641e2
 - https://huntr.com/bounties/303c5145-2c14-4945-914a-936be74dd04e