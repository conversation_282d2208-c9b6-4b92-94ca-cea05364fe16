info:
  name: anythingllm
  cve: CVE-2024-10513
  summary: '“文档上传管理器”功能中的路径遍历漏洞'
  details: |
    mintplex-labs/anything-llm 的“文档上传管理器”功能中存在路径遍历漏洞，影响 1.2.2 之前的最新版本。此漏洞允许具有“管理器”角色的用户访问和操作“anythingllm.db”数据库文件。通过利用易受攻击的端点'/api/document/move-files'，攻击者可以将数据库文件移动到公开可访问的目录，下载它，然后将其删除。这可能导致未经授权的敏感数据访问、权限提升和潜在的数据丢失。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. 升级到 anything-llm 版本 1.2.2 或更高版本。
    2. 审查并加强“管理器”角色的访问控制。
    3. 对文件路径输入实施额外的验证，以防止遍历攻击。
rule: version < "1.2.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10513
  - https://github.com/mintplex-labs/anything-llm/commit/47a5c7126c20e2277ee56e2c7ee11990886a40a7
  - https://huntr.com/bounties/ad11cecf-161a-4fb1-986f-6f88272cbb9e