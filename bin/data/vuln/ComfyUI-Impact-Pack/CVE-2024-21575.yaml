info:
  name: ComfyUI-Impact-Pack
  cve: CVE-2024-21575
  summary: ComfyUI-Impact-Pack 存在路径遍历漏洞
  details: ComfyUI-Impact-Pack 在服务器添加的 `/upload/temp` 端点接收 POST 请求时，由于缺失对 `image.filename` 字段的验证，导致可向文件系统写入任意文件，在某些条件下可能造成远程代码执行（RCE）。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:N/I:H/A:N
  severity: CRITICAL
  security_advise: 请及时关注官方发布的安全更新，并遵循相关安全指南进行修复。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-21575
 - https://github.com/ltdrdata/ComfyUI-Impact-Pack/commit/a43dae373e648ae0f0cc0c9768c3cea6a72acff7
 - https://github.com/ltdrdata/ComfyUI-Impact-Pack/blob/1087f2ee063c9d53cd198add79b41a7a3465c05a/modules/impact/impact_server.py#L28