info:
  name: kubeflow
  cve: CVE-2024-5552
  summary: Kubeflow 正则表达式拒绝服务（ReDoS）漏洞
  details: Kubeflow/kubeflow 由于其电子邮件验证机制中的正则表达式复杂度效率低下，容易受到正则表达式拒绝服务（ReDoS）攻击。攻击者可以通过提供特制的输入，在无需身份验证的情况下远程利用此漏洞，导致应用程序消耗过多的 CPU 资源。此漏洞影响 kubeflow/kubeflow 的最新版本，特别是在 centraldashboard-angular 后端组件中。利用此漏洞的影响包括资源耗尽和服务中断。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: 升级到修复了此漏洞的 Kubeflow 版本，或者审查并优化电子邮件验证机制中的正则表达式，以防止 ReDoS 攻击。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-5552
 - https://huntr.com/bounties/0c1d6432-f385-4c54-beea-9f8c677def5b