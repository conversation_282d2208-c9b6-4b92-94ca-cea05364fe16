info:
  name: kubeflow
  cve: CVE-2024-9526
  summary: Kubeflow Pipeline View web UI 存储型 XSS 漏洞
  details: Kubeflow Pipeline View web UI 中存在存储型 XSS 漏洞。Kubeflow Web UI 允许创建新管道，在创建新管道时可添加描述，描述字段允许 html 标签但未进行适当过滤，导致存储型 XSS 漏洞。建议升级至超过提交 930c35f1c543998e60e8d648ce93185c9b5dbe8d 的版本。
  cvss: CVSS:4.0/AV:A/AC:L/AT:P/PR:H/UI:P/VC:H/VI:H/VA:L/SC:H/SI:H/SA:L/E:X/CR:X/IR:X/AR:X/MAV:X/MAC:X/MAT:X/MPR:X/MUI:X/MVC:X/MVI:X/MVA:X/MSC:X/MSI:X/MSA:X/S:P/AU:Y/R:U/V:D/RE:L/U:Green
  severity: HIGH
  security_advise: 升级至超过提交 930c35f1c543998e60e8d648ce93185c9b5dbe8d 的版本以解决此问题。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-9526
 - https://github.com/kubeflow/pipelines/pull/10315