info:
  name: langchain
  cve: CVE-2023-36095
  summary: langchain代码注入漏洞
  details: Harrison Chase langchain中的一个问题允许攻击者通过PALChain,from_math_prompt(llm).run中的python exec方法执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到langchain>=0.0.236以解决此问题。
rule: version < "0.0.236"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-36095
 - https://github.com/langchain-ai/langchain/issues/5872
 - https://github.com/langchain-ai/langchain/pull/6003
 - https://github.com/langchain-ai/langchain/pull/7870
 - https://github.com/langchain-ai/langchain/commit/8ba9835b925473655914f63822775679e03ea137
 - https://github.com/langchain-ai/langchain/commit/e294ba475a355feb95003ed8f1a2b99942509a9e
 - https://github.com/hwchase17/langchain
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/commits/v0.0.236?after=4d8b48bdb3f17c764c5c2e3c7140071603869e74+34&branch=v0.0.236&qualified_name=refs%2Ftags%2Fv0.0.236
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-138.yaml
 - http://langchain.com