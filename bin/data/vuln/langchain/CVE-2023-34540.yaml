info:
  name: langchain
  cve: CVE-2023-34540
  summary: Langchain OS命令注入漏洞
  details: Langchain在v0.0.225之前的版本被发现存在一个远程代码执行（RCE）漏洞，该漏洞位于组件JiraAPIWrapper（即JIRA API包装器）中。此漏洞允许攻击者通过特制的输入执行任意代码。如“releases/tag”参考资料所述，已提供修复程序。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到langchain>=0.0.225以解决此问题。
rule: version < "0.0.225"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-34540
 - https://github.com/hwchase17/langchain/issues/4833
 - https://github.com/langchain-ai/langchain/issues/4833
 - https://github.com/langchain-ai/langchain/pull/6992
 - https://github.com/langchain-ai/langchain/commit/a2f191a32229256dd41deadf97786fe41ce04cbb
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/releases/tag/v0.0.225
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-91.yaml