info:
  name: langchain
  cve: CVE-2024-2057
  summary: LangChain的load_local函数存在服务器端请求伪造漏洞
  details: 在<PERSON> Chase LangChain 0.1.9版本中发现了一个漏洞，该漏洞影响了库libs/community/langchain_community/retrievers/tfidf.py中的load_local函数。该漏洞可导致服务器端请求伪造，攻击者可以远程发起攻击。此漏洞的标识符为VDB-255372。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: CRITICAL
  security_advise: 升级到LangChain的最新版本以解决此问题，确保及时应用所有安全补丁。
rule: version < "0.1.9"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-2057
 - https://github.com/langchain-ai/langchain/pull/18695
 - https://github.com/bayuncao/vul-cve-16
 - https://github.com/bayuncao/vul-cve-16/tree/main/PoC.pkl
 - https://vuldb.com/?ctiid.255372
 - https://vuldb.com/?id.255372