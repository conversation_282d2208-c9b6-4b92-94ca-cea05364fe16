info:
  name: langchain
  cve: CVE-2024-27444
  summary: LangChain Experimental 存在任意代码执行漏洞
  details: langchain_experimental（即 LangChain Experimental）在 0.0.52 之前版本，以及 LangChain 在 0.1.8 之前版本，允许攻击者绕过 CVE-2023-44467 的修复，通过 Python 代码中的 `__import__`、`__subclasses__`、`__builtins__`、`__globals__`、`__getattribute__`、`__bases__`、`__mro__` 或 `__base__` 属性执行任意代码。这些属性未被 `pal_chain/base.py` 禁止。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain_experimental>=0.0.52 或 LangChain>=0.1.8 以解决此问题。
rule: version < "0.0.52"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-27444
 - https://github.com/langchain-ai/langchain/commit/de9a6cdf163ed00adaf2e559203ed0a9ca2f1de7
 - https://github.com/langchain-ai/langchain