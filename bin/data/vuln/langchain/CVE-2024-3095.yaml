info:
  name: langchain
  cve: CVE-2024-3095
  summary: langchain-community.retrievers.web_research.WebResearchRetriever 中的服务器端请求伪造漏洞
  details: langchain-community 的 Web Research Retriever 组件存在服务器端请求伪造（SSRF）漏洞。由于 Web Research Retriever 不限制对远程互联网地址的请求，允许其访问本地地址。此缺陷使攻击者能够执行端口扫描、访问本地服务，并在某些情况下，从云环境中读取实例元数据。该漏洞尤其令人担忧，因为它可被利用来滥用 Web Explorer 服务器作为对第三方的 Web 攻击代理，并与本地网络中的服务器交互，包括读取它们的响应数据。这可能会导致根据本地服务的性质而执行任意代码。该漏洞仅限于 GET 请求，因为不可能进行 POST 请求，但由于潜在的凭据被盗和与内部 API 的状态更改交互，对保密性、完整性和可用性的影响显著。
  cvss: CVSS:3.0/AV:P/AC:H/PR:L/UI:N/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 langchain-community >= 0.2.9 版本以解决此问题，或者参考修复代码，要求用户选择加入，并建议使用代理来防止对本地地址的请求。
rule: version < "0.2.9"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3095
 - https://github.com/langchain-ai/langchain/pull/24451
 - https://github.com/langchain-ai/langchain/commit/604dfe2d99246b0c09f047c604f0c63eafba31e7
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/releases/tag/langchain-community%3D%3D0.2.9
 - https://huntr.com/bounties/e62d4895-2901-405b-9559-38276b6a5273