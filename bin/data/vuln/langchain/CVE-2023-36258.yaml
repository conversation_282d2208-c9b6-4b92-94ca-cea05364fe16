info:
  name: langchain
  cve: CVE-2023-36258
  summary: langchain 任意代码执行漏洞
  details: langchain 中的一个问题允许攻击者通过 PALChain 在 python exec 方法中执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain >= "0.0.247" 版本以解决此问题。
rule: version < "0.0.247"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-36258
  - https://github.com/langchain-ai/langchain/issues/5872
  - https://github.com/langchain-ai/langchain/issues/5872#issuecomment-1697785619
  - https://github.com/langchain-ai/langchain/pull/6003
  - https://github.com/langchain-ai/langchain/pull/7870
  - https://github.com/langchain-ai/langchain/pull/8425
  - https://github.com/langchain-ai/langchain/commit/8ba9835b925473655914f63822775679e03ea137
  - https://github.com/langchain-ai/langchain/commit/e294ba475a355feb95003ed8f1a2b99942509a9e
  - https://github.com/langchain-ai/langchain/commit/fab24457bcf8ede882abd11419769c92bc4e7751
  - https://github.com/hwchase17/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-98.yaml