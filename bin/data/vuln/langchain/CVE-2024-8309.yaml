info:
  name: langchain
  cve: CVE-2024-8309
  summary: Langchain SQL注入漏洞
  details: langchain-ai/langchain版本0.2.5中的GraphCypherQAChain类存在SQL注入漏洞，该漏洞可通过提示注入实现SQL注入。此漏洞可能导致未经授权的数据操作、数据泄露、通过删除所有数据导致的服务拒绝（DoS）、多租户安全环境中的安全漏洞以及数据完整性问题。攻击者可以在未经适当授权的情况下创建、更新或删除节点和关系，提取敏感数据，中断服务，访问不同租户的数据，并破坏数据库的完整性。
  cvss: CVSS:3.0/AV:L/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: LOW
  security_advise: 升级到langchain-community版本0.2.19或langchain版本0.2.0以上以解决此问题。
rule: version < "0.2.19" && (version >= "0.2.0" && version < "0.2.0")
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-8309
 - https://github.com/langchain-ai/langchain/commit/64c317eba05fbac0c6a6fc5aa192bc0d7130972e
 - https://github.com/langchain-ai/langchain/commit/c2a3021bb0c5f54649d380b42a0684ca5778c255
 - https://github.com/langchain-ai/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-115.yaml
 - https://huntr.com/bounties/8f4ad910-7fdc-4089-8f0a-b5df5f32e7c5