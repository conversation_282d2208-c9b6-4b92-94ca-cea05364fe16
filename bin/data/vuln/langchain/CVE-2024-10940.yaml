info:
  name: langchain
  cve: CVE-2024-10940
  summary: langchain-core中的未经授权文件读取漏洞
  details: |
    langchain-core版本 >=0.1.17,<0.1.53、>=0.2.0,<0.2.43 以及 >=0.3.0,<0.3.15 中存在一个漏洞，允许未经授权的用户从主机文件系统读取任意文件。该问题源于能够创建带有输入变量的langchain_core.prompts.ImagePromptTemplate（以及扩展的langchain_core.prompts.ChatPromptTemplate），这些输入变量可以从服务器文件系统读取任何用户指定的路径。如果这些提示模板的输出暴露给用户，无论是直接还是通过下游模型输出，都可能导致敏感信息的泄露。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级至langchain-core >=0.1.53、>=0.2.43 或 >=0.3.15
    2. 审查并限制ImagePromptTemplate和ChatPromptTemplate的使用，以防止文件系统路径的暴露
    3. 实施输入验证，确保用户指定的路径不能用于读取任意文件
rule: (version >= "0.1.17" && version < "0.1.53") || (version >= "0.2.0" && version < "0.2.43") || (version >= "0.3.0" && version < "0.3.15")
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10940
  - https://github.com/langchain-ai/langchain/commit/c1e742347f9701aadba8920e4d1f79a636e50b68
  - https://huntr.com/bounties/be1ee1cb-2147-4ff4-a57b-b6045271cf27