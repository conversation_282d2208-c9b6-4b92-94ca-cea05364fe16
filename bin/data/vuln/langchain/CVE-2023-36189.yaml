info:
  name: langchain
  cve: CVE-2023-36189
  summary: langchain SQL注入漏洞
  details: langchain中的SQL注入漏洞允许远程攻击者通过SQLDatabaseChain组件获取敏感信息。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到langchain版本0.0.247或更高以解决此问题。
rule: version < "0.0.247"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-36189
 - https://github.com/hwchase17/langchain/issues/5923
 - https://github.com/langchain-ai/langchain/issues/5923
 - https://github.com/langchain-ai/langchain/issues/5923#issuecomment-1696053841
 - https://github.com/hwchase17/langchain/pull/6051
 - https://github.com/langchain-ai/langchain/pull/8425
 - https://github.com/langchain-ai/langchain/commit/fab24457bcf8ede882abd11419769c92bc4e7751
 - https://gist.github.com/rharang/9c58d39db8c01db5b7c888e467c0533f
 - https://github.com/langchain-ai/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-110.yaml