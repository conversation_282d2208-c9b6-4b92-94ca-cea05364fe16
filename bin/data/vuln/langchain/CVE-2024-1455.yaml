info:
  name: langchain
  cve: CVE-2024-1455
  summary: LangChain的XMLOutputParser易受XML实体扩展攻击
  details: LangChain中的XMLOutputParser使用了Python标准库中的xml模块的etree，该模块存在一些XML漏洞。这主要影响将LLM（或代理）与XMLOutputParser结合使用，并通过Web服务的端点暴露该组件的用户。恶意方可能会尝试操纵LLM产生恶意负载，从而危害服务的可用性。成功的攻击基于以下条件：1. 使用XMLOutputParser；2. 直接或通过尝试操纵LLM为用户执行此操作，将恶意输入传递给XMLOutputParser；3. 通过Web服务暴露该组件。
  cvss: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: 升级到langchain-core>=0.1.35以解决此问题。
rule: version < "0.1.35"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1455
 - https://github.com/langchain-ai/langchain/pull/17250
 - https://github.com/langchain-ai/langchain/pull/19653
 - https://github.com/langchain-ai/langchain/pull/19660
 - https://github.com/langchain-ai/langchain/commit/727d5023ce88e18e3074ef620a98137d26ff92a3
 - https://github.com/langchain-ai/langchain
 - https://huntr.com/bounties/4353571f-c70d-4bfd-ac08-3a89cecb45b6