info:
  name: langchain
  cve: CVE-2024-46946
  summary: LangChain Experimental Eval 注入漏洞
  details: langchain_experimental（又名 LangChain Experimental）0.1.17 至 0.3.0 版本中的 LangChain 允许攻击者通过 sympy.sympify（使用 eval）在 LLMSymbolicMathChain 中执行任意代码。LLMSymbolicMathChain 在 fcccde406dd9e9b05fc9babcbeb9ff527b0ec0c6（2023-10-05）中引入。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain-experimental 版本大于 0.3.0 以解决此问题。
rule: version >= "0.1.17" && version <= "0.3.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-46946
 - https://docs.sympy.org/latest/modules/codegen.html
 - https://gist.github.com/12end/68c0c58d2564ef4141bccd4651480820#file-cve-2024-46946-txt
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/releases/tag/langchain-experimental%3D%3D0.3.0