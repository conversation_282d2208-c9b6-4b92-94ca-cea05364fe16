info:
  name: langchain
  cve: CVE-2024-7042
  summary: "@langchain/community SQL注入漏洞"
  details: "langchain-ai/langchainjs版本0.2.5及包含GraphCypherQAChain类的所有版本中存在漏洞，允许提示注入，从而导致SQL注入。该漏洞允许未经授权的数据操作、数据泄露、通过删除所有数据导致的服务拒绝（DoS）、多租户安全环境中的违规行为以及数据完整性问题。攻击者可以在未经适当授权的情况下创建、更新或删除节点和关系，提取敏感数据，中断服务，跨不同租户访问数据，并破坏数据库的完整性。"
  cvss: CVSS:3.0/AV:L/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: LOW
  security_advise: 升级到@langchain/community版本0.3.3或更高以解决此问题。
rule: version >= "0" && version < "0.3.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7042
  - https://github.com/langchain-ai/langchainjs/commit/615b9d9ab30a2d23a2f95fb8d7acfdf4b41ad7a6
  - https://github.com/langchain-ai/langchainjs
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-114.yaml
  - https://huntr.com/bounties/b612defb-1104-4fff-9fef-001ab07c7b2d