info:
  name: langchain
  cve: CVE-2023-38860
  summary: LangChain 存在任意代码执行漏洞
  details: LangChain 在 v0.0.247 之前的版本中存在一个问题，允许远程攻击者通过 prompt 参数执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain >= 0.0.247 版本以解决此问题。
rule: version < "0.0.247"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-38860
 - https://github.com/hwchase17/langchain/issues/7641
 - https://github.com/langchain-ai/langchain/issues/7641
 - https://github.com/langchain-ai/langchain/pull/8092
 - https://github.com/langchain-ai/langchain/pull/8425
 - https://github.com/langchain-ai/langchain/commit/d353d668e4b0514122a443cef91de7f76fea4245
 - https://github.com/langchain-ai/langchain/commit/fab24457bcf8ede882abd11419769c92bc4e7751
 - https://github.com/hwchase17/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-145.yaml