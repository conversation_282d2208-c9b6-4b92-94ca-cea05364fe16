info:
  name: langchain
  cve: CVE-2023-36188
  summary: langchain 存在任意代码执行漏洞
  details: langchain 中的一个问题允许远程攻击者通过 Python exec 方法中的 PALChain 参数执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain >= "0.0.236" 版本以修复此漏洞。
rule: version < "0.0.236"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-36188
 - https://github.com/langchain-ai/langchain/issues/5872
 - https://github.com/langchain-ai/langchain/pull/6003
 - https://github.com/langchain-ai/langchain/pull/8425
 - https://github.com/langchain-ai/langchain/commit/e294ba475a355feb95003ed8f1a2b99942509a9e
 - https://github.com/langchain-ai/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-109.yaml