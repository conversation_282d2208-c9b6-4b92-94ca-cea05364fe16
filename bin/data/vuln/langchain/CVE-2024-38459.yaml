info:
  name: langchain
  cve: CVE-2024-38459
  summary: langchain_experimental 通过 Python REPL 访问执行代码漏洞
  details: langchain_experimental（又名 LangChain Experimental）在 0.0.61 之前的版本中提供了无需同意步骤的 Python REPL 访问权限。请注意，此问题是由于对 CVE-2024-27444 的修复不完整而导致的。
  cvss: CVSS:3.1/AV:L/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 langchain-experimental>=0.0.61 以解决此问题。
rule: version < "0.0.61"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-38459
 - https://github.com/langchain-ai/langchain/pull/22860
 - https://github.com/langchain-ai/langchain/commit/ce0b0f22a175139df8f41cdcfb4d2af411112009
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/compare/langchain-experimental==0.0.60...langchain-experimental==0.0.61
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain-experimental/PYSEC-2024-53.yaml