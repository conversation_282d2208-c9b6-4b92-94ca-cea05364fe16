info:
  name: langchain
  cve: CVE-2024-7774
  summary: Langchain 路径遍历漏洞
  details: langchain-ai/langchainjs 版本 0.2.5 的 `getFullPath` 方法中存在路径遍历漏洞。此漏洞允许攻击者在文件系统中任意位置保存文件、覆盖现有文本文件、读取 `.txt` 文件以及删除文件。漏洞通过 `setFileContent`、`getParsedFile` 和 `mdelete` 方法被利用，这些方法未能正确清理用户输入。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 langchainjs >= 0.2.19 版本以解决此问题。
rule: version >= "0" && version < "0.2.19"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7774
  - https://github.com/langchain-ai/langchainjs/commit/a0fad77d6b569e5872bd4a9d33be0c0785e538a9
  - https://github.com/langchain-ai/langchainjs
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-111.yaml
  - https://huntr.com/bounties/8fe40685-b714-4191-af7a-3de5e5628cee