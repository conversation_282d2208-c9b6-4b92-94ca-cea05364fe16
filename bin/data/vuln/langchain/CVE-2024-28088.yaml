info:
  name: langchain
  cve: CVE-2024-28088
  summary: LangChain 目录遍历漏洞
  details: LangChain 在 0.1.10 版本中，允许攻击者通过控制 load_chain 调用中路径参数的最后一部分来实现 ../ 目录遍历。这绕过了仅从 hwchase17/langchain-hub GitHub 仓库加载配置的预期行为。结果可能导致在线大型语言模型的 API 密钥泄露，或者远程代码执行。
  cvss: 
  severity: LOW
  security_advise: 升级到 langchain >= 0.0.339 或 langchain-core >= 0.1.30 以解决此问题。
rule: version < "0.1.30"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-28088
 - https://github.com/langchain-ai/langchain/pull/18600
 - https://github.com/langchain-ai/langchain/commit/e1924b3e93d513ca950c72f8e80e1c133749fba5
 - https://github.com/PinkDraconian/PoC-Langchain-RCE/blob/main/README.md
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/blob/f96dd57501131840b713ed7c2e86cbf1ddc2761f/libs/core/langchain_core/utils/loading.py
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain-core/PYSEC-2024-45.yaml
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-43.yaml