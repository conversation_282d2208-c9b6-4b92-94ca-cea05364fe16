info:
  name: langchain
  cve: CVE-2023-46229
  summary: LangChain 服务器端请求伪造漏洞
  details: LangChain 在 0.0.317 之前的版本中，由于 `document_loaders/recursive_url_loader.py` 文件中的爬取机制可以从外部服务器继续爬取到内部服务器，从而允许 SSRF 漏洞的存在。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 langchain >= "0.0.317" 版本以解决此问题。
rule: version < "0.0.317"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-46229
 - https://github.com/langchain-ai/langchain/pull/11925
 - https://github.com/langchain-ai/langchain/commit/9ecb7240a480720ec9d739b3877a52f76098a2b8
 - https://github.com/langchain-ai/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-205.yaml