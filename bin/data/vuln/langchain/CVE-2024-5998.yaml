info:
  name: langchain
  cve: CVE-2024-5998
  summary: <PERSON><PERSON><PERSON><PERSON> pickle反序列化不受信任数据
  details: <PERSON><PERSON><PERSON><PERSON>中的`FAISS.deserialize_from_bytes`函数存在漏洞，允许对不受信任的数据进行pickle反序列化。这可能导致通过`os.system`函数执行任意命令。该问题影响0.2.4之前的版本。
  cvss: CVSS:3.1/AV:P/AC:L/PR:L/UI:R/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: 升级到langchain-community>=0.2.4以解决此问题。
rule: version < "0.2.4"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-5998
  - https://github.com/langchain-ai/langchain/commit/77209f315efd13442ec51c67719ba37dfaa44511
  - https://github.com/langchain-ai/langchain
  - https://huntr.com/bounties/fa3a2753-57c3-4e08-a176-d7a3ffda28fe