info:
  name: langchain
  cve: CVE-2023-38896
  summary: <PERSON><PERSON><PERSON><PERSON> 存在任意代码执行漏洞
  details: <PERSON> Chase langchain 在 0.0.236 版本之前存在一个问题，允许远程攻击者通过 `from_math_prompt` 和 `from_colored_object_prompt` 函数执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain >= 0.0.236 版本以解决此问题。
rule: version < "0.0.236"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-38896
  - https://github.com/hwchase17/langchain/issues/5872
  - https://github.com/hwchase17/langchain/pull/6003
  - https://github.com/langchain-ai/langchain/commit/8ba9835b925473655914f63822775679e03ea137
  - https://github.com/langchain-ai/langchain/commit/e294ba475a355feb95003ed8f1a2b99942509a9e
  - https://github.com/langchain-ai/langchain
  - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-146.yaml
  - https://twitter.com/llm_sec/status/1668711587287375876