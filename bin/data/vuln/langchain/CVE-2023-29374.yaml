info:
  name: langchain
  cve: CVE-2023-29374
  summary: LangChain 存在代码注入漏洞
  details: 在 LangChain 版本 0.0.131 及之前版本中，`LLMMathChain` 链允许通过 Python 的 `exec()` 方法执行任意代码的提示注入攻击。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain 版本 0.0.132 或更高版本以解决此问题。
rule: version <= "0.0.131"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-29374
 - https://github.com/hwchase17/langchain/issues/1026
 - https://github.com/hwchase17/langchain/issues/814
 - https://github.com/hwchase17/langchain/pull/1119
 - https://github.com/langchain-ai/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-18.yaml
 - https://twitter.com/rharang/status/1641899743608463365/photo/1