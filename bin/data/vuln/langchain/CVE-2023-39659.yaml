info:
  name: langchain
  cve: CVE-2023-39659
  summary: LangChain 存在任意代码执行漏洞
  details: LangChain langchain-ai 在 0.0.325 版本之前存在一个问题，允许远程攻击者通过特制的脚本到 PythonAstREPLTool._run 组件执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain>=0.0.325 版本以解决此漏洞。
rule: version < "0.0.325"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-39659
 - https://github.com/langchain-ai/langchain/issues/7700
 - https://github.com/langchain-ai/langchain/pull/12427
 - https://github.com/langchain-ai/langchain/pull/5640
 - https://github.com/langchain-ai/langchain/commit/cadfce295f8a33828fc635c2e5ea28b883e5c992
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/releases/tag/v0.0.325
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-147.yaml