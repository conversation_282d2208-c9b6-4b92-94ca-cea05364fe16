info:
  name: langchain
  cve: CVE-2024-21513
  summary: langchain-experimental 存在任意代码执行漏洞
  details: langchain-experimental 包的 0.0.15 及之前版本在从数据库检索值时存在任意代码执行漏洞，代码会对所有值尝试调用 'eval'。如果攻击者能够控制输入提示且服务器配置了 VectorSQLDatabaseChain，就可以利用此漏洞执行任意 Python 代码。
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain-experimental >= 0.0.21 版本以解决此问题。
rule: version >= "0.0.0" && version < "0.0.21"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-21513
 - https://github.com/langchain-ai/langchain/commit/7b13292e3544b2f5f2bfb8a27a062ea2b0c34561
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/blob/672907bbbb7c38bf19787b78e4ffd7c8a9026fe4/libs/experimental/langchain_experimental/sql/vector_sql.py#L81
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain-experimental/PYSEC-2024-62.yaml
 - https://security.snyk.io/vuln/SNYK-PYTHON-LANGCHAINEXPERIMENTAL-7278171