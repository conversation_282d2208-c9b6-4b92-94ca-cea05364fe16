info:
  name: langchain
  cve: CVE-2023-34541
  summary: Langchain 存在任意代码执行漏洞
  details: Langchain 0.0.171 版本中的 `load_prompt` 函数存在任意代码执行漏洞。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain >= "0.0.247" 版本以修复此漏洞。
rule: version >= "0" && version < "0.0.247"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-34541
 - https://github.com/langchain-ai/langchain/issues/4849
 - https://github.com/langchain-ai/langchain/issues/4849#issuecomment-1697896569
 - https://github.com/langchain-ai/langchain/pull/8425
 - https://github.com/langchain-ai/langchain/commit/fab24457bcf8ede882abd11419769c92bc4e7751
 - https://github.com/langchain-ai/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-92.yaml