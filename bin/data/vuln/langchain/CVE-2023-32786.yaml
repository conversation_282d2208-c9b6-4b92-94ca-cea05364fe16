info:
  name: langchain
  cve: CVE-2023-32786
  summary: Langchain 服务器端请求伪造漏洞
  details: 在 Langchain 版本低于 0.0.329 时，提示注入允许攻击者强制服务从任意 URL 检索数据，本质上提供了 SSRF，并可能将内容注入到下游任务中。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 langchain >= 0.0.329 版本以解决此问题。
rule: version < "0.0.329"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-32786
 - https://github.com/langchain-ai/langchain/pull/12747
 - https://gist.github.com/rharang/d265f46fc3161b31ac2e81db44d662e1
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/releases/tag/v0.0.329