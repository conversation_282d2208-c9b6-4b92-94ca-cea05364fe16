info:
  name: langchain
  cve: CVE-2023-36281
  summary: langchain 存在任意代码执行漏洞
  details: langchain v.0.0.171 版本中存在一个问题，允许远程攻击者通过 `load_prompt` 参数中的 json 文件执行任意代码。这与 `__subclasses__` 或模板有关。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain >= 0.0.312 版本以解决此问题。
rule: version > "0" && version < "0.0.312"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-36281
 - https://github.com/hwchase17/langchain/issues/4394
 - https://github.com/langchain-ai/langchain/pull/10252
 - https://github.com/langchain-ai/langchain/commit/22abeb9f6cc555591bf8e92b5e328e43aa07ff6c
 - https://aisec.today/LangChain-2e6244a313dd46139c5ef28cbcab9e55
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/releases/tag/v0.0.312
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-151.yaml