info:
  name: langchain
  cve: CVE-2024-2965
  summary: langchain-community 中的拒绝服务漏洞
  details: |
    `langchain-community` 包中的 `SitemapLoader` 文档加载器存在拒绝服务漏洞，影响版本低于 0.2.5 的版本。`parse_sitemap` 方法负责解析站点地图并提取 URL，但缺乏防止无限递归的机制，当站点地图 URL 引用当前站点地图本身时，可能会导致无限循环，从而通过超过 Python 的最大递归深度导致崩溃。此漏洞可被利用来占用服务器套接字/端口资源并导致 Python 进程崩溃，影响依赖于此功能的服务的可用性。
  cvss: CVSS:3.0/AV:P/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: 升级到 langchain-community >= 0.2.5 版本以解决此问题。
rule: version < "0.2.5"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-2965
 - https://github.com/langchain-ai/langchain/pull/22903
 - https://github.com/langchain-ai/langchain/commit/73c42306745b0831aa6fe7fe4eeb70d2c2d87a82
 - https://github.com/langchain-ai/langchain/commit/9a877c7adbd06f90a2518152f65b562bd90487cc
 - https://github.com/langchain-ai/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2024-118.yaml
 - https://huntr.com/bounties/90b0776d-9fa6-4841-aac4-09fde5918cae