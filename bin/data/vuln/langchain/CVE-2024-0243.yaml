info:
  name: langchain
  cve: CVE-2024-0243
  summary: langchain 服务器端请求伪造漏洞
  details: |
   使用以下爬虫配置时，攻击者可通过控制 `https://example.com` 的内容放置恶意 HTML 文件，即使设置 `prevent_outside=True`，爬虫仍会下载文件。如在 `https://example.com` 中放置链接为 `https://example.completely.different/my_file.html` 的恶意文件，爬虫会继续下载该文件。
    ```python
    from bs4 import BeautifulSoup as Soup
    
    url = "https://example.com"
    loader = RecursiveUrlLoader(
    url=url, max_depth=2, extractor=lambda x: Soup(x, "html.parser").text
    )
    docs = loader.load()
    ```
    已在 https://github.com/langchain-ai/langchain/pull/15559 中修复。
cvss: CVSS:3.0/AV:L/AC:H/PR:H/UI:R/S:C/C:L/I:L/A:N
severity: LOW
security_advise: 升级到 langchain >= "0.1.0" 版本以解决此问题。
rule: version < "0.1.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-0243
  - https://github.com/langchain-ai/langchain/pull/15559
  - https://github.com/langchain-ai/langchain/commit/bf0b3cc0b5ade1fb95a5b1b6fa260e99064c2e22
  - https://github.com/langchain-ai/langchain
  - https://github.com/langchain-ai/langchain/blob/bf0b3cc0b5ade1fb95a5b1b6fa260e99064c2e22/libs/community/langchain_community/document_loaders/recursive_url_loader.py#L51-L51
  - https://huntr.com/bounties/370904e7-10ac-40a4-a8d4-e2d16e1ca861