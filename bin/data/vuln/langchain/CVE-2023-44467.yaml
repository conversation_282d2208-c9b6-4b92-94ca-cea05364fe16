info:
  name: langchain
  cve: CVE-2023-44467
  summary: langchain_experimental 存在通过 PALChain 的 python exec 方法执行任意代码的漏洞
  details: langchain_experimental 0.0.14 允许攻击者绕过 CVE-2023-36258 的修复，并通过 PALChain 在 python exec 方法中执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain-experimental 版本高于 0.0.14 以解决此问题。
rule: version = "0.0.14"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-44467
 - https://github.com/langchain-ai/langchain/pull/11233
 - https://github.com/langchain-ai/langchain/commit/4c97a10bd0d9385cfee234a63b5bd826a295e483
 - https://github.com/langchain-ai/langchain
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain-experimental/PYSEC-2023-194.yaml
 - https://pypi.org/project/langchain-experimental/0.0.14