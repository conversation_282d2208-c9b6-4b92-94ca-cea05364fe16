info:
  name: langchain
  cve: CVE-2024-3571
  summary: langchain 存在路径遍历漏洞
  details: langchain-ai/langchain 由于其 LocalFileStore 功能中对路径名限制不当（'路径遍历'），导致存在路径遍历漏洞。攻击者可以利用此漏洞读取或写入文件系统上的任意文件，可能导致信息泄露或远程代码执行。问题在于 mset 和 mget 方法中处理文件路径时，未充分清理用户提供的输入，允许目录遍历序列到达非预期目录。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 langchain >= "0.0.353" 版本以解决此问题。
rule: version < "0.0.353"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3571
 - https://github.com/langchain-ai/langchain/commit/aad3d8bd47d7f5598156ff2bdcc8f736f24a7412
 - https://github.com/langchain-ai/langchain
 - https://huntr.com/bounties/2df3acdc-ee4f-4257-bbf8-a7de3870a9d8