info:
  name: langchain
  cve: CVE-2023-39631
  summary: Langchain 通过 numexpr 库的 evaluate 函数存在任意代码执行漏洞
  details: LangChain-ai Langchain v.0.0.245 中的一个问题允许远程攻击者通过 numexpr 库的 evaluate 函数执行任意代码。该漏洞的补丁已在 v.0.0.308 版本中发布。numexpr 依赖项对 langchain 来说是可选的。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 langchain >= 0.0.308 版本以解决此问题，或者确保 numexpr 库更新到 2.8.5 版本。
rule: version > "0" && version < "0.0.308"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-39631
 - https://github.com/langchain-ai/langchain/issues/8363
 - https://github.com/pydata/numexpr/issues/442
 - https://github.com/langchain-ai/langchain/pull/11302
 - https://github.com/pydata/numexpr/commit/4b2d89cf14e75030d27629925b9998e1e91d23c7
 - https://github.com/langchain-ai/langchain
 - https://github.com/langchain-ai/langchain/releases/tag/v0.0.308
 - https://github.com/pypa/advisory-database/tree/main/vulns/langchain/PYSEC-2023-162.yaml
 - https://github.com/pypa/advisory-database/tree/main/vulns/numexpr/PYSEC-2023-163.yaml