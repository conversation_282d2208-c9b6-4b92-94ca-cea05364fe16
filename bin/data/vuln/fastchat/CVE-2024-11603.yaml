info:
  name: fastchat
  cve: CVE-2024-11603
  summary: fastchat中的服务器端请求伪造（SSRF）漏洞
  details: |
    lm-sys/fastchat版本0.2.36中存在服务器端请求伪造（SSRF）漏洞。
    该漏洞存在于`/queue/join?`端点，其中对路径参数的验证不足，允许攻击者发送特制的请求。这可能导致未经授权访问内部网络或AWS元数据端点，可能暴露敏感数据并危害内部服务器。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. 升级到fastchat版本0.2.37或更高
    2. 在`/queue/join?`端点对路径参数实施严格验证
    3. 监控并限制对内部网络和AWS元数据端点的访问
rule: version < "0.2.37"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-11603
  - https://huntr.com/bounties/89f1158d-4a75-4000-a1bd-f82dd1a62bff