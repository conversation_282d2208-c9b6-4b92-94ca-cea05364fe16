info:
  name: fastchat
  cve: CVE-2024-12376
  summary: lm-sys/fastchat中的服务器端请求伪造(SSRF)漏洞
  details: |
    在lm-sys/fastchat web服务器中识别出一个服务器端请求伪造(SSRF)漏洞，具体影响版本为git 2c68a13。该漏洞允许攻击者访问内部服务器资源和数据，例如AWS元数据凭证，这些资源和数据通常是不可访问的。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: 高
  security_advise: |
    1. 升级到解决此漏洞的最新版本的fastchat。
    2. 对所有外部请求实施严格的输入验证。
    3. 使用Web应用程序防火墙(WAF)来监控和阻止可疑请求。
rule: version < "git 2c68a13"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12376
  - https://huntr.com/bounties/c9cc3f28-ee9f-4d2d-9ee5-8c6455a11892