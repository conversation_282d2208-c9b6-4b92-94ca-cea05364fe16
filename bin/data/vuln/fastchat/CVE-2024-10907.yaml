info:
  name: fastchat
  cve: CVE-2024-10907
  summary: fastchat中由于对多部分边界处理不当导致的拒绝服务（DoS）漏洞
  details: |
    在lm-sys/fastchat版本v0.2.36中，服务器未能处理附加到多部分边界末尾的过多字符。攻击者可以通过发送在边界末尾带有任意字符的畸形多部分请求来利用此缺陷。每个额外字符都在无限循环中处理，导致资源过度消耗，并为所有用户造成完全拒绝服务（DoS）。该漏洞无需身份验证，这意味着攻击者无需用户登录或交互即可利用此问题。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到fastchat >= v0.2.37
    2. 实施对多部分边界的输入验证，以防止过度处理字符
    3. 监控服务器日志中可能表明攻击的不寻常活动
rule: version < "0.2.37"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10907
  - https://huntr.com/bounties/bf3ca81d-3508-4455-95d9-0b653e46d6e4