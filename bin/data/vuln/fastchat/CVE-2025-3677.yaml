info:
  name: fastchat
  cve: CVE-2025-3677
  summary: lm-sys fastchat中最高至0.2.36版本的严重反序列化漏洞。
  details: |
    在文件`fastchat/model/apply_delta.py`的`split_files/apply_delta_low_cpu_mem`函数中发现了一个严重的漏洞。
    此漏洞允许进行本地反序列化攻击，可能导致未经授权的数据访问或操作。
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: 严重
  security_advise: |
    1. 升级到fastchat版本0.2.37或更高版本。
    2. 审查并修补`apply_delta.py`文件以防止未经授权的反序列化。
    3. 实施额外的安全措施以在必要时监控和限制本地访问。
rule: version < "0.2.37"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-3677
  - https://github.com/lm-sys/FastChat/issues/3713
  - https://vuldb.com/?ctiid.304966
  - https://vuldb.com/?id.304966
  - https://vuldb.com/?submit.552755