info:
  name: fastchat
  cve: CVE-2024-10044
  summary: fastchat Controller API Server的SSRF漏洞
  details: lm-sys/fastchat的Controller API Server中的POST /worker_generate_stream API端点存在服务器端请求伪造(SSRF)漏洞。该漏洞允许攻击者利用受害者的controller API服务器凭证执行未经授权的网络操作或访问未经授权的网络资源，特别是当与POST /register_worker端点结合使用时。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
  severity: CRITICAL
  security_advise: 建议更新fastchat至修复该漏洞的最新版本，或者限制Controller API Server的网络访问权限以减少潜在的风险。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-10044
 - https://huntr.com/bounties/44633540-377d-4ac4-b3a3-c2d0fa19d0e6