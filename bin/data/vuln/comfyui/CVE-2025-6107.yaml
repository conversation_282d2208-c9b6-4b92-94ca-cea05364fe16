info:
  name: comfyui
  cve: CVE-2025-6107
  summary: comfyui 0.3.40中的漏洞允许远程操作对象属性。
  details: |
    在comfyui版本0.3.40的`/comfy/utils.py`中的`set_attr`函数中发现了一个漏洞。这个问题使攻击者能够远程动态确定对象属性。尽管攻击复杂性高且可利用性被认为是困难的，但该漏洞已被公开披露并且可能被利用。供应商已被告知但未回应。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:N/I:N/A:L
  severity: 低
  security_advise: |
    1. 升级到解决此漏洞的comfyui较新版本。
    2. 审查并修改`/comfy/utils.py`中的`set_attr`函数，以防止动态属性操作。
    3. 实施额外的安全措施来监控和限制对敏感功能的远程访问。
rule: version <= "0.3.40"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-6107
  - https://gist.github.com/superboy-zjc/f71b84ed074260a5e459581caa2f1fb2
  - https://gist.github.com/superboy-zjc/f71b84ed074260a5e459581caa2f1fb2#proof-of-concept
  - https://vuldb.com/?ctiid.312576
  - https://vuldb.com/?id.312576
  - https://vuldb.com/?submit.590921