info:
  name: comfyui
  cve: CVE-2024-10099
  summary: ComfyUI 存储型跨站脚本（XSS）漏洞
  details: 在 comfyanonymous/comfyui 版本 0.2.2 及可能更早版本中存在存储型跨站脚本（XSS）漏洞。当攻击者通过 `/api/upload/image` 端点上传包含恶意 XSS 载荷的 HTML 文件时，该漏洞就会发生。当通过 `/view` API 端点查看文件时，载荷会被执行，可能导致任意 JavaScript 代码的执行。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 comfyui >= 0.2.3 版本以解决此问题，或者通过加强文件上传接口的过滤和校验机制来防止恶意文件的上传和执行。
rule: version < "0.2.3"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-10099
 - https://huntr.com/bounties/14fb8c9a-692a-4d8c-b4b2-24c6f91a383c