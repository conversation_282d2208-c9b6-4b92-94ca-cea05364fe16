info:
  name: comfyui
  cve: CVE-2024-10481
  summary: comfyanonymous/comfyui 版本直至 v0.2.2 存在 CSRF 漏洞
  details: |
    comfyanonymous/comfyui 版本直至 v0.2.2 存在 CSRF 漏洞。此漏洞允许攻击者托管恶意网站，当认证的 ComfyUI 用户访问这些网站时，攻击者可代表该用户执行任意 API 请求。这可被利用来执行诸如通过 `/upload/image` 端点上传任意文件之类的操作。API 端点如 `/upload/image`、`/prompt` 和 `/history` 缺乏 CSRF 保护，使用户易受未经授权的操作威胁，这可能与其他漏洞（例如存储型 XSS）相结合以进一步危害用户会话。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:H/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级至 comfyui >= v0.2.3
    2. 在所有 API 端点实施 CSRF 保护
    3. 定期审查并更新安全措施以防止类似漏洞
rule: version <= "0.2.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10481
  - https://huntr.com/bounties/f4d5bfb5-6ff1-4356-b81f-f8c01d2e6ded