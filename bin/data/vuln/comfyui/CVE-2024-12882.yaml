info:
  name: comfyui
  cve: CVE-2024-12882
  summary: comfyui中的非盲SSRF漏洞
  details: |
    comfyanonymous/comfyui版本v0.2.4中的非盲服务器端请求伪造（SSRF）漏洞，
    可通过组合REST API `POST /internal/models/download` 和 `GET /view` 来利用。
    这允许攻击者滥用受害服务器的凭据来访问未经授权的网络资源。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: 高
  security_advise: |
    1. 升级至comfyui >= v0.2.5
    2. 为所有REST API端点实施严格的输入验证
    3. 仅限授权用户访问内部API
rule: version < "0.2.5"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12882
  - https://huntr.com/bounties/e8768cb1-6a80-40c1-9cdf-bcd21f01f85a