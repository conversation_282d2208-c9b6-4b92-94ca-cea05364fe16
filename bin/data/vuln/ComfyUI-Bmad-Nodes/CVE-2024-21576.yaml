info:
  name: ComfyUI-Bmad-Nodes
  cve: CVE-2024-21576
  summary: ComfyUI-Bmad-Nodes 存在代码注入漏洞
  details: ComfyUI-Bmad-Nodes 中的 BuildColorRangeHSVAdvanced、FilterContour 和 FindContour 自定义节点存在验证绕过漏洞。在这些节点的入口函数中，存在对 eval 函数的调用，攻击者可通过生成注入了特制字符串的工作流来触发该漏洞，从而在服务器上执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 建议立即更新 ComfyUI 至最新版本，并审查和修改自定义节点的代码，避免使用 eval 函数或确保其输入的安全性，以防止代码注入攻击。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-21576
 - https://github.com/bmad4ever/comfyui_bmad_nodes/blob/392af9490cbadf32a1fe92ff820ebabe88c51ee8/cv_nodes.py#L1814