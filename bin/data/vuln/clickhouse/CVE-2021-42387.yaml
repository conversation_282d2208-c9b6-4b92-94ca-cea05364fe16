info:
  name: clickhouse
  cve: CVE-2021-42387
  summary: Clickhouse的LZ4压缩编解码器堆越界读取漏洞
  details: |
    在解析恶意查询时，Clickhouse的LZ4压缩编解码器存在堆越界读取漏洞。在LZ4::decompressImpl()循环中，从压缩数据中读取一个16位无符号用户提供的值（'offset'）。该偏移量后来用于复制操作的长度，而没有检查复制操作源的上界。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H
  severity: HIGH
  security_advise: 建议升级至最新版本的Clickhouse数据库以修复此漏洞，或者应用官方发布的安全更新。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2021-42387
 - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms
 - https://lists.debian.org/debian-lts-announce/2022/11/msg00002.html