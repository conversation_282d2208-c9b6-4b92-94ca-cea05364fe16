info:
  name: clickhouse
  cve: CVE-2019-16536
  summary: 在19.14.3.3之前的Clickhouse版本中，堆栈溢出导致DoS
  details: |
    在19.14.3.3之前的Clickhouse版本中存在堆栈溢出漏洞，该漏洞可由恶意的已认证客户端触发，可能导致拒绝服务（DoS）。
  cvss: CVSS:4.0/AV:N/AC:L/AT:N/PR:H/UI:N/VC:N/VI:N/VA:H/SC:N/SI:N/SA:H/E:X/CR:X/IR:X/AR:X/MAV:X/MAC:X/MAT:X/MPR:X/MUI:X/MVC:X/MVI:X/MVA:X/MSC:X/MSI:X/MSA:X/S:X/AU:X/R:X/V:X/RE:X/U:X
  severity: HIGH
  security_advise: |
    1. 升级到19.14.3.3版本或更高版本的Clickhouse。
    2. 监控任何可能表明发生攻击的不寻常活动。
    3. 实施额外的安全措施，如速率限制和输入验证。
rule: version < "19.14.3.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2019-16536
  - https://clickhouse.com/docs/whats-new/security-changelog