info:
  name: clickhouse
  cve: CVE-2018-14669
  summary: ClickHouse MySQL客户端文件读取漏洞
  details: |
    ClickHouse MySQL客户端在1.1.54390版本之前启用了\"LOAD DATA LOCAL INFILE\"功能，这允许恶意的MySQL数据库从连接的ClickHouse服务器读取任意文件。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级ClickHouse MySQL客户端至1.1.54390版本或更高版本以解决此问题。
rule: version < "1.1.54390"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2018-14669
 - https://clickhouse.yandex/docs/en/security_changelog