info:
  name: clickhouse
  cve: CVE-2018-14668
  summary: ClickHouse 远程表函数跨协议请求伪造漏洞
  details: |
    在 ClickHouse 1.1.54388 之前的版本中，"remote" 表函数允许在 "user"、"password" 和 "default_database" 字段中使用任意符号，这导致了跨协议请求伪造攻击。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 ClickHouse 版本 1.1.54388 或更高版本以解决此问题。
rule: version < "1.1.54388"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2018-14668
 - https://clickhouse.yandex/docs/en/security_changelog