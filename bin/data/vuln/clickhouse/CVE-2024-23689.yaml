info:
  name: clickhouse
  cve: CVE-2024-23689
  summary: ClickHouse 中敏感信息的泄露
  details: |
    在 ClickHouse 的 clickhouse-r2dbc、com.clickhouse:clickhouse-jdbc 和 com.clickhouse:clickhouse-client 版本低于 0.4.6 时，异常中会泄露敏感信息。当指定 'sslkey' 并且在数据库操作期间抛出异常（如 ClickHouseException 或 SQLException）时，客户端证书密码会包含在记录的异常消息中，导致未经授权的用户可以通过客户端异常日志获取客户端证书密码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 com.clickhouse:clickhouse-r2dbc、com.clickhouse:clickhouse-jdbc 和 com.clickhouse:clickhouse-client 版本 0.4.6 或更高版本以解决此问题。
rule: version < "0.4.6"
references:
  - https://github.com/ClickHouse/clickhouse-java/security/advisories/GHSA-g8ph-74m6-8m7r
  - https://nvd.nist.gov/vuln/detail/CVE-2024-23689
  - https://github.com/ClickHouse/clickhouse-java/issues/1331
  - https://github.com/ClickHouse/clickhouse-java/pull/1334
  - https://github.com/ClickHouse/clickhouse-java
  - https://github.com/ClickHouse/clickhouse-java/releases/tag/v0.4.6
  - https://github.com/advisories/GHSA-g8ph-74m6-8m7r
  - https://vulncheck.com/advisories/vc-advisory-GHSA-g8ph-74m6-8m7r