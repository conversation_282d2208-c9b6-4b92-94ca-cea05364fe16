info:
  name: clickhouse
  cve: CVE-2021-4104
  summary: 使用log4j配置中的JMSAppender可能导致反序列化不受信任的数据
  details: |
    ClickHouse JDBC Bridge使用[slf4j-log4j12 1.7.32](https://repo1.maven.org/maven2/org/slf4j/slf4j-log4j12/1.7.32/)，它依赖于[log4j 1.2.17](https://repo1.maven.org/maven2/log4j/log4j/1.2.17/)。如果您通过添加JMSAppender和一个不安全的JMS代理更改了默认的log4j配置，它允许远程攻击者在服务器上执行代码。
    ### 修复补丁
    补丁版本`2.0.7`通过将`slf4j-log4j12`替换为`slf4j-jdk14`移除了log4j依赖。日志配置也从`log4j.properties`更改为`logging.properties`。
    ### 解决方法
    1. 不要更改log4j配置以使用JMSAppender和不安全的JMS代理
    2. 或者，您可以执行以下命令来移除`JMSAppender.class`:
    
    ```bash
    # 如果没有安装zip命令，请先安装
    apt-get update && apt-get install -y zip
    # 移除该类
    zip -d clickhouse-jdbc-bridge*.jar ru/yandex/clickhouse/jdbcbridge/internal/log4j/net/JMSAppender.class
    ```
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到clickhouse-jdbc-bridge版本`2.0.7`或更高版本以解决此问题，或者避免在log4j配置中使用JMSAppender与不安全的JMS代理。
rule: version < "2.0.7"
references:
 - https://github.com/ClickHouse/clickhouse-jdbc-bridge/security/advisories/GHSA-3w6p-8f82-gw8r
 - https://nvd.nist.gov/vuln/detail/CVE-2021-4104
 - https://access.redhat.com/security/cve/CVE-2021-4104
 - https://github.com/ClickHouse/clickhouse-jdbc-bridge