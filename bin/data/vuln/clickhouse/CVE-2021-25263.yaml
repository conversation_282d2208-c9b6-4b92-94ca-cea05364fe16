info:
  name: clickhouse
  cve: CVE-2021-25263
  summary: Clickhouse 文件读取漏洞
  details: |
    在 Clickhouse 版本 v20.8.18.32-lts, v21.1.9.41-stable, v21.2.9.41-stable, v21.3.6.55-lts, v21.4.3.21-stable 之前，用户可以读取 Clickhouse 用户有权限访问的主机系统上的任何文件。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 Clickhouse 版本 v20.8.18.32-lts, v21.1.9.41-stable, v21.2.9.41-stable, v21.3.6.55-lts, 或 v21.4.3.21-stable 及以上，以解决此安全问题。
rule: version < "20.8.18.32" || (version < "21.1.9.41" && version != "21.1.x") || version < "21.2.9.41" || version < "21.3.6.55" || version < "21.4.3.21"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2021-25263
 - https://clickhouse.tech/docs/en/whats-new/security-changelog
 - https://yandex.com/bugbounty/i/hall-of-fame-browser