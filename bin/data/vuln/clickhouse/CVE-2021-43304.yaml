info:
  name: clickhouse
  cve: CVE-2021-43304
  summary: Clickhouse的LZ4压缩编解码器中的堆缓冲区溢出漏洞
  details: |
    在解析恶意查询时，Clickhouse的LZ4压缩编解码器存在堆缓冲区溢出漏洞。LZ4::decompressImpl循环中的复制操作，特别是任意复制操作wildCopy<copy_amount>(op, ip, copy_end)，没有验证是否超出目标缓冲区的限制。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 建议升级到Clickhouse的最新版本以解决此问题，并确保所有查询都经过适当的验证和过滤，以防止恶意查询的执行。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2021-43304
 - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms
 - https://lists.debian.org/debian-lts-announce/2022/11/msg00002.html