info:
  name: clickhouse
  cve: CVE-2022-44010
  summary: ClickHouse 堆缓冲区溢出漏洞
  details: |
    在 ClickHouse 22.9.1.2603 之前的版本中发现了一个问题。攻击者可以向 HTTP 端点（通常默认监听在端口 8123）发送特制的 HTTP 请求，导致堆缓冲区溢出，从而使进程崩溃。此漏洞不需要身份验证。已修复的版本包括 22.9.1.2603、22.8.2.11、22.7.4.16、22.6.6.16 和 22.3.12.19。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: 升级到 ClickHouse 版本 22.9.1.2603 或更高版本以解决此问题。
rule: version < "22.9.1.2603"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2022-44010
  - https://clickhouse.com/docs/en/whats-new/security-changelog