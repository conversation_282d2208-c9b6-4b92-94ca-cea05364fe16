info:
  name: clickhouse
  cve: CVE-2021-42390
  summary: Clickhouse DeltaDouble 压缩编解码器中的除零漏洞
  details: |
    在解析恶意查询时，Clickhouse 的 DeltaDouble 压缩编解码器存在除零漏洞。压缩缓冲区的第一个字节在模运算中使用而未检查是否为0。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: 建议更新 Clickhouse 至最新版本以修复该漏洞，并确保查询经过严格验证以避免恶意输入。
  references:
    - https://nvd.nist.gov/vuln/detail/CVE-2021-42390
    - https://jfrog.com/blog/7-rce-and-dos-vulnerabilities-found-in-clickhouse-dbms
rule: version < "21.3.7.1" # 假设21.3.7.1是修复该漏洞的版本