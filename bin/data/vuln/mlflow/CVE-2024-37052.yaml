info:
  name: mlflow
  cve: CVE-2024-37052
  summary: M<PERSON><PERSON> 不安全的反序列化
  details: |
    在运行版本为 1.1.0 或更新版本的 MLflow 平台中，可能会发生对不可信数据的反序列化，这使得恶意上传的 scikit-learn 模型在与最终用户的系统交互时能够执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.14.2 版本以解决此问题，该版本已修复了相关的安全漏洞。
rule: version >= "1.1.0" && version <= "2.14.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-37052
 - https://github.com/mlflow/mlflow
 - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024