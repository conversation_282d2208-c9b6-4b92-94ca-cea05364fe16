info:
  name: mlflow
  cve: CVE-2024-6838
  summary: mlflow中的漏洞允许通过大型实验名称和无限的工件位置导致拒绝服务。
  details: |
    在mlflow/mlflow版本v2.13.2中，存在一个漏洞，由于对实验名称没有限制，允许创建或重命名一个名称中有大量整数的实验。这可能导致MLflow UI面板无响应，从而导致潜在的拒绝服务。此外，在创建实验时，`artifact_location`参数中没有字符限制。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L
  severity: MEDIUM
  security_advise: |
    1. 升级到mlflow >= 2.13.3
    2. 实施输入验证以限制实验名称中的字符数和整数数量。
    3. 设置`artifact_location`参数的最大长度以防止过大的输入。
rule: version < "2.13.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6838
  - https://huntr.com/bounties/8ad52cb2-2cda-4eb0-aec9-586060ee43e0