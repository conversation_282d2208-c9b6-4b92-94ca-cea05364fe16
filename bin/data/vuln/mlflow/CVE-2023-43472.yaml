info:
  name: mlflow
  cve: CVE-2023-43472
  summary: MLflow中的信息泄露
  details: |
    MLFlow版本2.8.1及之前版本存在一个问题，允许远程攻击者通过特制的REST API请求获取敏感信息。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到mlflow>=2.9.0以解决此问题。
rule: version >= "0" && version < "2.9.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-43472
 - https://github.com/mlflow/mlflow
 - https://mlflow.org/news/2023/12/06/2.9.0-release/index.html
 - https://www.contrastsecurity.com/security-influencers/discovering-mlflow-framework-zero-day-vulnerability-machine-language-model-security-contrast-security