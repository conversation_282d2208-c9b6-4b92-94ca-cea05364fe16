info:
  name: mlflow
  cve: CVE-2023-30172
  summary: mlflow存在目录遍历漏洞
  details: |
    mlflow平台在v2.0.0之前的版本中，/get-artifact API方法存在目录遍历漏洞。攻击者可以通过路径参数读取服务器上的任意文件。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到mlflow>=2.0.0rc0以解决此问题。
rule: version < "2.0.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-30172
 - https://github.com/mlflow/mlflow/issues/7166
 - https://github.com/mlflow/mlflow/issues/7166#issuecomment-1541543234
 - https://github.com/mlflow/mlflow/pull/7170
 - https://github.com/mlflow/mlflow/commit/ac4b697bb0bb8a331944dca63f4235b4bf602ab8
 - https://github.com/mlflow/mlflow
 - https://github.com/mlflow/mlflow/commits/v2.0.0?after=00c3b0a350a28c25b16fbb7feddb8147a919ce18+69&branch=v2.0.0&qualified_name=refs%2Ftags%2Fv2.0.0
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-70.yaml