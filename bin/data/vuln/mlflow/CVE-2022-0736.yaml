info:
  name: mlflow
  cve: CVE-2022-0736
  summary: mlflow 中的不安全临时文件
  details: |
    mlflow 在 1.23.1 之前的版本中存在不安全的临时文件问题。不安全的函数 `tempfile.mktemp()` 已被弃用，应改用 `mkstemp()`。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:H
  severity: HIGH
  security_advise: 升级到 mlflow >= 1.23.1 版本以解决此问题。
rule: version < "1.23.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2022-0736
  - https://github.com/mlflow/mlflow/commit/61984e6843d2e59235d82a580c529920cd8f3711
  - https://github.com/advisories/GHSA-vqj2-4v8m-8vrq
  - https://github.com/mlflow/mlflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2022-28.yaml
  - https://huntr.dev/bounties/e5384764-c583-4dec-a1d8-4697f4e12f75