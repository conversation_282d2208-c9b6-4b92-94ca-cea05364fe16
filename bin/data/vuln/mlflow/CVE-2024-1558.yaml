info:
  name: mlflow
  cve: CVE-2024-1558
  summary: mlflow 存在路径遍历漏洞
  details: |
    mlflow 的 `_create_model_version()` 函数（位于 `server/handlers.py` 文件中）存在路径遍历漏洞，这是由于 `source` 参数验证不当造成的。攻击者可以通过构造一个绕过 `_validate_non_local_source_contains_relative_paths(source)` 函数检查的 `source` 参数，从而在服务器上实现任意文件读取访问。该问题源于未引用 URL 字符的处理以及随后对原始 `source` 值的滥用，用于模型版本创建，导致在与 `/model-versions/get-artifact` 处理程序交互时暴露敏感文件。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 mlflow >= "2.12.1" 版本以解决此漏洞。
rule: version <= "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1558
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/7f4dbcc5-b6b3-43dd-b310-e2d0556a8081