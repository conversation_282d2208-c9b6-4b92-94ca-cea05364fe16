info:
  name: mlflow
  cve: CVE-2023-4033
  summary: mlflow 存在操作系统命令注入漏洞
  details: |
    在 GitHub 仓库 mlflow/mlflow 版本 2.6.0 之前存在操作系统命令注入漏洞。
  cvss: CVSS:3.0/AV:L/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.6.0 版本以修复此漏洞。
rule: version < "2.6.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-4033
 - https://github.com/mlflow/mlflow/commit/6dde93758d42455cb90ef324407919ed67668b9b
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-280.yaml
 - https://huntr.dev/bounties/5312d6f8-67a5-4607-bd47-5e19966fa321