info:
  name: mlflow
  cve: CVE-2023-6018
  summary: mlflow中的远程代码执行漏洞，由于完全控制的文件写入
  details: |
    mlflow web服务器包含用于跟踪实验、将代码打包成可重现的运行以及共享和部署模型的工具。由于此漏洞允许在文件系统上写入/覆盖任何文件，因此有很多方法可以实现代码执行（例如覆盖`/home/<USER>/.bashrc`）。恶意用户可以利用此问题在易受攻击的机器上获得命令执行权限，并获取数据和模型信息。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到mlflow>=2.9.2以解决此问题，该版本已修复了文件写入漏洞，防止未经授权的文件操作。
rule: version < "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6018
 - https://github.com/mlflow/mlflow/commit/55c72d02380e8db8118595a4fdae7879cb7ac5bd
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/7cf918b5-43f4-48c0-a371-4d963ce69b30