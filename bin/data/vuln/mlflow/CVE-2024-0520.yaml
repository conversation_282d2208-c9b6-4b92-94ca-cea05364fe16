info:
  name: mlflow
  cve: CVE-2024-0520
  summary: mlflow 远程代码执行漏洞
  details: |
    mlflow/mlflow 版本 8.2.1 中存在一个漏洞，由于在 `mlflow.data.http_dataset_source.py` 模块中未正确中和用于 OS 命令的特殊元素（命令注入），允许远程代码执行。具体来说，当从具有 HTTP 方案的源 URL 加载数据集时，从 `Content-Disposition` 标头或 URL 路径中提取的文件名用于生成最终文件路径而未进行适当消毒。这个缺陷使攻击者能够通过使用路径遍历或绝对路径技术（如 '../../tmp/poc.txt' 或 '/tmp/poc.txt'）完全控制文件路径，导致任意文件写入。利用此漏洞可能允许恶意用户在易受攻击的机器上执行命令，有可能获取数据和模型信息。该问题在版本 2.9.0 中得到修复。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 mlflow >= 2.9.0 版本以解决此问题。
rule: version < "2.9.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-0520
 - https://github.com/mlflow/mlflow/commit/400c226953b4568f4361bc0a0c223511652c2b9d
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/93e470d7-b6f0-409b-af63-49d3e2a26dbc