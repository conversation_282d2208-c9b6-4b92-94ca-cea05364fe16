info:
  name: mlflow
  cve: CVE-2024-37057
  summary: M<PERSON><PERSON> 不安全的反序列化
  details: |
    在运行版本为2.0.0rc0或更新的MLflow平台中，可能会发生对不可信数据的反序列化，这使得恶意上传的Tensorflow模型在与最终用户的系统交互时能够执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到mlflow版本大于2.14.1以解决此问题，或者应用官方发布的安全补丁。
rule: version >= "2.0.0rc0" && version <= "2.14.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-37057
 - https://github.com/mlflow/mlflow
 - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024