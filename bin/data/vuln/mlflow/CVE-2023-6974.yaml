info:
  name: mlflow
  cve: CVE-2023-6974
  summary: MLflow 服务器端请求伪造(SSRF)漏洞
  details: |
    恶意用户可以利用此问题访问内部HTTP(s)服务器，在最坏的情况下（例如：aws实例），可能会被滥用从而在受害者的机器上实现远程代码执行。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到mlflow>=2.9.2以解决此问题。
rule: version >= "0" && version < "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6974
 - https://github.com/mlflow/mlflow/commit/8174250f83352a04c2d42079f414759060458555
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/438b0524-da0e-4d08-976a-6f270c688393