info:
  name: mlflow
  cve: CVE-2024-37053
  summary: MLFlow 不安全的反序列化
  details: |
    在运行1.1.0或更新版本的MLflow平台中，对不可信数据的反序列化可能会导致问题，允许恶意上传的scikit-learn模型在与最终用户的系统交互时执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到mlflow版本大于2.14.1以解决此问题，或者应用官方发布的安全补丁。
rule: version >= "1.1.0" && version <= "2.14.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-37053
 - https://github.com/mlflow/mlflow
 - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024