info:
  name: mlflow
  cve: CVE-2023-6909
  summary: MLflow 路径遍历漏洞
  details: |
    路径遍历漏洞存在于 GitHub 仓库 mlflow/mlflow 的版本 2.9.2 之前，具体表现为 '\\..\\filename' 的路径遍历问题。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.9.2 版本以修复此漏洞。
rule: version < "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6909
 - https://github.com/mlflow/mlflow/commit/1da75dfcecd4d169e34809ade55748384e8af6c1
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-252.yaml
 - https://huntr.com/bounties/11209efb-0f84-482f-add0-587ea6b7e850