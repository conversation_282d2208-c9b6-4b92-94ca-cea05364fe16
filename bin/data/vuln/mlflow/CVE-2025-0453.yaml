info:
  name: mlflow
  cve: CVE-2025-0453
  summary: mlflow GraphQL端点存在拒绝服务漏洞
  details: |
    在mlflow/mlflow版本2.17.2中，`/graphql`端点易受拒绝服务攻击。
    攻击者可以创建大批量查询，反复请求给定实验的所有运行，
    可能会占用MLFlow分配的所有工作进程，由于资源消耗不受控制，导致应用程序无法响应其他请求。
  cvss: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. 升级至mlflow >= 2.17.3
    2. 在`/graphql`端点实施速率限制，以防止大批量查询
    3. 监控资源使用情况，并为异常活动设置警报
rule: version == "2.17.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0453
  - https://huntr.com/bounties/788327ec-714a-4d5c-83aa-8df04dd7612b