info:
  name: mlflow
  cve: CVE-2024-1594
  summary: mlflow 存在路径遍历漏洞
  details: |
    mlflow/mlflow 仓库中存在一个路径遍历漏洞，具体是在创建实验时处理 `artifact_location` 参数时。攻击者可以通过在工件位置 URI 中使用片段组件 `#` 来读取服务器上任意文件，这是在服务器进程的上下文中进行的。此问题类似于 CVE-2023-6909，但利用了 URI 的不同组件来实现相同的效果。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.9.3 版本以解决此问题。
rule: version > "0" && version < "2.9.3"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1594
 - https://github.com/mlflow/mlflow
 - https://github.com/mlflow/mlflow/blob/b929a3e727dc48a1eb19b7e954b7897ac09ad3ec/mlflow/utils/uri.py#L246
 - https://huntr.com/bounties/424b6f6b-e778-4a2b-b860-39730d396f3e