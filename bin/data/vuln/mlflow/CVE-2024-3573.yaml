info:
  name: mlflow
  cve: CVE-2024-3573
  summary: mlflow存在路径遍历漏洞
  details: |
    mlflow由于对URI的不当解析，存在本地文件包含（LFI）漏洞，允许攻击者绕过检查并读取系统上的任意文件。该问题源于'is_local_uri'函数未能正确处理空或'file'方案的URI，导致将URI错误分类为非本地。攻击者可以通过制作带有特殊' source '参数的恶意模型版本来利用此漏洞，从而能够读取服务器根目录下至少两级目录内的敏感文件。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:L/A:N
  severity: CRITICAL
  security_advise: 升级到mlflow>=2.10.0以解决此问题。
rule: version >= "0" && version < "2.10.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3573
 - https://github.com/mlflow/mlflow/commit/438a450714a3ca06285eeea34bdc6cf79d7f6cbc
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/8ea058a7-4ef8-4baf-9198-bc0147fc543c