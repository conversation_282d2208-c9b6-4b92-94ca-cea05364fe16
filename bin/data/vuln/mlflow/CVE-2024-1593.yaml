info:
  name: mlflow
  cve: CVE-2024-1593
  summary: mlflow 存在路径遍历漏洞
  details: |
    mlflow/mlflow 仓库中存在路径遍历漏洞，这是由于对 URL 参数处理不当造成的。通过在 URL 中使用 ';' 字符走私路径遍历序列，攻击者可以操纵 URL 的 'params' 部分以获得对文件或目录的未经授权访问。此漏洞允许在 URL 的 'params' 部分任意走私数据，从而实现类似于先前报告中描述的攻击，但利用 ';' 字符进行参数走私。成功利用可能导致未经授权的信息泄露或服务器受损。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.9.3 版本以解决此问题。
rule: version >= "0" && version < "2.9.3"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1593
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/dbdc6bd6-d09a-46f2-9d9c-5138a14b6e31