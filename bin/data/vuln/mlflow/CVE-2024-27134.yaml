info:
  name: mlflow
  cve: CVE-2024-27134
  summary: MLflow的过度目录权限允许本地权限提升
  details: |
    MLflow中过度的目录权限在使用spark_udf时会导致本地权限提升。这种行为可以被本地攻击者利用ToCToU攻击来获得提升的权限。该问题仅在调用spark_udf() MLflow API时相关。
  cvss: CVSS:3.1/AV:L/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到mlflow>=2.16.0以解决此问题。
rule: version >= "0" && version < "2.16.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-27134
 - https://github.com/mlflow/mlflow/pull/10874
 - https://github.com/mlflow/mlflow/commit/0b1d995d66a678153e01ed3040f3f4dfc16a0d6b
 - https://github.com/mlflow/mlflow