info:
  name: mlflow
  cve: CVE-2023-1177
  summary: mlflow 在 `mlflow server` 和 `mlflow ui` CLIs 中存在远程文件访问漏洞
  details: |
    使用旧版本（低于 2.2.1）的 `mlflow server` 或 `mlflow ui` 命令托管 MLflow Model Registry 的用户，如果未限制谁可以查询其服务器（例如，通过使用云 VPC、入站请求的 IP 允许列表或身份验证/授权中间件），可能会受到远程文件访问漏洞的攻击。
    此问题仅影响运行 `mlflow server` 和 `mlflow ui` 命令的用户和集成。不使用 `mlflow server` 或 `mlflow ui` 的集成不受影响；例如，Databricks Managed MLflow 产品和 Azure Machine Learning 上的 MLflow 不使用这些命令，因此不会受到这些漏洞的任何影响。
    该漏洞允许攻击者从主机服务器下载与 MLflow 无关的任意文件，包括主机服务器有权访问的远程位置存储的任何文件。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 mlflow >= 2.2.1 版本以解决此问题。如果无法立即升级，强烈建议使用云 VPC、入站请求的 IP 允许列表、身份验证/授权中间件或其他访问限制机制来限制谁可以访问 MLflow Model Registry 和 MLflow Tracking 服务器。此外，还建议限制 MLflow Model Registry 和 MLflow Tracking 服务器有权访问的远程文件范围。
rule: version < "2.2.1"
references:
 - https://github.com/mlflow/mlflow/security/advisories/GHSA-xg73-94fp-g449
 - https://nvd.nist.gov/vuln/detail/CVE-2023-1177
 - https://github.com/mlflow/mlflow/pull/7891/commits/7162a50c654792c21f3e4a160eb1a0e6a34f6e6e
 - https://github.com/mlflow/mlflow/commit/7162a50c654792c21f3e4a160eb1a0e6a34f6e6e
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-29.yaml
 - https://huntr.dev/bounties/1fe8f21a-c438-4cba-9add-e8a5dab94e28