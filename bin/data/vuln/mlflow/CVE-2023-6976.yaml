info:
  name: mlflow
  cve: CVE-2023-6976
  summary: MLflow 路径遍历漏洞
  details: |
    此漏洞能够在服务器进程的上下文中，将任意文件写入远程文件系统的任意位置。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.9.2 版本以修复此漏洞。
rule: version > "0" && version < "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6976
 - https://github.com/mlflow/mlflow/commit/5044878da0c1851ccfdd5c0a867157ed9a502fbc
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/2408a52b-f05b-4cac-9765-4f74bac3f20f