info:
  name: mlflow
  cve: CVE-2023-6709
  summary: mlflow中的Jinja2模板注入漏洞
  details: |
    在GitHub仓库mlflow/mlflow的2.9.2之前的版本中，存在模板引擎中特殊元素的不当中和问题。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到mlflow>=2.9.2以解决此问题。
rule: version >= "0" && version < "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6709
 - https://github.com/mlflow/mlflow/commit/432b8ccf27fd3a76df4ba79bb1bec62118a85625
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-281.yaml
 - https://huntr.com/bounties/9e4cc07b-6fff-421b-89bd-9445ef61d34d