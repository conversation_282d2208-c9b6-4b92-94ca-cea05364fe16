info:
  name: mlflow
  cve: CVE-2024-37056
  summary: M<PERSON><PERSON> 不安全的反序列化
  details: |
    在运行 MLflow 平台版本 1.23.0 或更新版本时，可能会发生对不可信数据的反序列化，这使得恶意上传的 LightGBM scikit-learn 模型在与终端用户系统交互时能够执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.14.2 版本以解决此问题，该版本已修复了相关的反序列化漏洞。
rule: version >= "1.23.0" && version <= "2.14.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-37056
 - https://github.com/mlflow/mlflow
 - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024