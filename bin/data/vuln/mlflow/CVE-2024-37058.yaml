info:
  name: mlflow
  cve: CVE-2024-37058
  summary: M<PERSON><PERSON> 不安全的反序列化
  details: 在运行 2.5.0 或更新版本的 MLflow 平台中，对不可信数据的反序列化可能会导致恶意上传的 Langchain AgentExecutor 模型在与终端用户系统交互时执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 mlflow 版本大于 2.14.1 或应用官方提供的安全补丁来解决此问题。
rule: version >= "2.5.0" && version <= "2.14.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-37058
 - https://github.com/mlflow/mlflow
 - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024