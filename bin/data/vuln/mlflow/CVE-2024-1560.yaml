info:
  name: mlflow
  cve: CVE-2024-1560
  summary: mlflow 存在路径遍历漏洞
  details: |
    mlflow/mlflow 仓库中的 artifact 删除功能存在路径遍历漏洞。攻击者可以通过利用 `_delete_artifact_mlflow_artifacts` 处理程序和 `local_file_uri_to_path` 函数中的双重解码过程绕过路径验证，从而允许删除服务器文件系统上的任意目录。此漏洞是由于 `local_artifact_repo.py` 中的 `delete_artifacts` 函数中额外的 unquote 操作导致的，该操作未能正确清理用户提供的路径。尽管在 CVE-2023-6831 中尝试修复了类似问题，但此问题一直存在直到版本 2.9.2。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: 升级到 mlflow 版本大于 2.9.2 以解决此问题。
rule: version <= "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1560
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/4a34259c-3c8f-4872-b178-f27fbc876b98