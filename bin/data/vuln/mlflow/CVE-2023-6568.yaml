info:
  name: mlflow
  cve: CVE-2023-6568
  summary: MLflow 中的跨站脚本 (XSS) 漏洞
  details: |
    mlflow/mlflow 存在一个反射型跨站脚本 (XSS) 漏洞，具体存在于处理 POST 请求中的 Content-Type 头时。攻击者可以将恶意 JavaScript 代码注入到 Content-Type 头中，然后未经适当的消毒或转义就将其反射回用户，导致在受害者的浏览器上下文中执行任意 JavaScript。该漏洞存在于 mlflow/server/auth/__init__.py 文件中，用户提供的 Content-Type 头被直接注入到 Python 格式化字符串中并返回给用户，从而便于进行 XSS 攻击。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 mlflow >= 2.9.0 以解决此问题。
rule: version < "2.9.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6568
 - https://github.com/mlflow/mlflow/commit/28ff3f94994941e038f2172c6484b65dc4db6ca1
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-260.yaml
 - https://huntr.com/bounties/816bdaaa-8153-4732-951e-b0d92fddf709