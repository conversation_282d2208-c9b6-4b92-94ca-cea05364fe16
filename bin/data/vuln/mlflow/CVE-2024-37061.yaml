info:
  name: mlflow
  cve: CVE-2024-37061
  summary: M<PERSON>low 输入验证不当
  details: |
    在运行1.11.0版本或更新版本的MLflow平台上，由于输入未过滤，恶意制作的MLproject在运行时可以在最终用户的系统上执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到mlflow版本大于2.13.1以解决此问题，或者应用官方发布的安全补丁来修复输入验证的漏洞。
rule: version >= "1.11.0" && version <= "2.13.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-37061
 - https://github.com/mlflow/mlflow
 - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024