info:
  name: mlflow
  cve: CVE-2024-27133
  summary: MLFlow跨站脚本漏洞导致客户端远程代码执行
  details: |
    MLFlow中由于对数据集表字段缺乏足够的消毒处理，导致在使用不受信任的数据集运行配方时出现XSS漏洞。该问题在Jupyter Notebook中运行配方时会导致客户端远程代码执行。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到mlflow>=2.10.0以解决此问题。
rule: version >= "0" && version < "2.10.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-27133
 - https://github.com/mlflow/mlflow/pull/10893
 - https://github.com/mlflow/mlflow/commit/c43823750bffa5b6abcc086683b15a068513b67b
 - https://github.com/mlflow/mlflow/commit/cfa71879a884cc3520e23ccab998c9aa78fdf2b1
 - https://github.com/mlflow/mlflow
 - https://research.jfrog.com/vulnerabilities/mlflow-untrusted-dataset-xss-jfsa-2024-000631932