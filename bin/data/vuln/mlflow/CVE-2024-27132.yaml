info:
  name: mlflow
  cve: CVE-2024-27132
  summary: MLF<PERSON> 跨站脚本漏洞
  details: |
    MLFlow 中的模板变量缺乏消毒，导致在运行不受信任的配方时发生跨站脚本（XSS）攻击。
    
    此问题在 Jupyter Notebook 中运行不受信任的配方时，会导致客户端远程代码执行（RCE）。
    
    漏洞源于对模板变量缺乏适当的消毒处理。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.10.0 以解决此问题。
rule: version >= "0" && version < "2.10.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-27132
  - https://github.com/mlflow/mlflow/pull/10873
  - https://github.com/mlflow/mlflow
  - https://research.jfrog.com/vulnerabilities/mlflow-untrusted-recipe-xss-jfsa-2024-000631930