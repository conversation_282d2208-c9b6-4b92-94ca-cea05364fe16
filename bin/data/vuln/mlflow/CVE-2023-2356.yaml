info:
  name: mlflow
  cve: CVE-2023-2356
  summary: mlflow中的相对路径遍历漏洞
  details: |
    在GitHub仓库mlflow/mlflow的2.3.1版本之前存在相对路径遍历漏洞。
    攻击者可利用该漏洞通过构造特定的相对路径访问服务器上的任意文件。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:L
  severity: HIGH
  security_advise: 升级到mlflow>=2.3.1版本以修复此漏洞。
rule: version < "2.3.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-2356
 - https://github.com/mlflow/mlflow/commit/f73147496e05c09a8b83d95fb4f1bf86696c6342
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-68.yaml
 - https://huntr.dev/bounties/7b5d130d-38eb-4133-8c7d-0dfc9a9d9896