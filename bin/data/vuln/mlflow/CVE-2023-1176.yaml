info:
  name: mlflow
  cve: CVE-2023-1176
  summary: mlflow server 和 mlflow ui CLIs 的远程文件存在检查漏洞
  details: |
    MLflow 开源项目的用户，如果使用 `mlflow server` 或 `mlflow ui` 命令并且版本低于 MLflow 2.2.1，同时没有限制谁可以查询其服务器（例如，通过使用云 VPC、入站请求的 IP 白名单或身份验证/授权中间件），可能会受到远程文件存在检查利用漏洞的影响。
    此问题仅影响运行 `mlflow server` 和 `mlflow ui` 命令的用户和集成。不使用 `mlflow server` 或 `mlflow ui` 的集成不受影响；例如，Databricks 托管的 MLflow 产品和 Azure Machine Learning 上的 MLflow 不使用这些命令，因此这些漏洞不会以任何方式受到影响。
    该漏洞使攻击者能够从主机服务器检查与 MLflow 无关的任意文件的存在，包括主机服务器有权访问的远程位置存储的任何文件。
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 mlflow >= 2.2.1 版本以解决此问题。如果无法立即升级，强烈建议使用云 VPC、入站请求的 IP 白名单、身份验证/授权中间件或其他访问限制机制来限制谁可以访问 MLflow Model Registry 和 MLflow Tracking 服务器。
rule: version < "2.2.1"
references:
 - https://github.com/mlflow/mlflow/security/advisories/GHSA-wp72-7hj9-5265
 - https://nvd.nist.gov/vuln/detail/CVE-2023-1176
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-28.yaml
 - https://huntr.dev/bounties/ae92f814-6a08-435c-8445-eec0ef4f1085