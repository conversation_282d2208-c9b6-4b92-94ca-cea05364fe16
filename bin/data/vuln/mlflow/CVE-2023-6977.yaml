info:
  name: mlflow
  cve: CVE-2023-6977
  summary: MLflow 本地文件泄露漏洞
  details: |
    此漏洞允许恶意用户读取服务器上的敏感文件。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.9.2 版本以修复此漏洞。
rule: version < "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6977
 - https://github.com/mlflow/mlflow/commit/4bd7f27c810ba7487d53ed5ef1038fca0f8dc28c
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/fe53bf71-3687-4711-90df-c26172880aaf