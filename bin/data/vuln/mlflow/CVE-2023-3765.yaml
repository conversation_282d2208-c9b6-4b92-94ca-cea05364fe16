info:
  name: mlflow
  cve: CVE-2023-3765
  summary: MLflow 路径遍历漏洞
  details: |
    在 GitHub 仓库 mlflow/mlflow 版本 2.5.0 之前存在绝对路径遍历漏洞。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 mlflow >= 2.5.0 以解决此问题。
rule: version < "2.5.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-3765
 - https://github.com/mlflow/mlflow/commit/6dde93758d42455cb90ef324407919ed67668b9b
 - https://github.com/mlflow/mlflow
 - https://huntr.dev/bounties/4be5fd63-8a0a-490d-9ee1-f33dc768ed76