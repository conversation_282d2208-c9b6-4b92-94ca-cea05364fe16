info:
  name: mlflow
  cve: CVE-2023-6940
  summary: mlflow 命令注入漏洞
  details: |
    仅通过一次用户交互（下载恶意配置文件），攻击者就可以在受害系统上获得完全的命令执行权限。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.9.2 版本以解决此问题。
rule: version < "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6940
 - https://github.com/mlflow/mlflow/pull/10676
 - https://github.com/mlflow/mlflow/commit/5139b1087d686fa52e2b087e09da66aff86297b1
 - https://github.com/mlflow/mlflow/commit/a98a341a7222f894b7735db575ad9311ecaba4e3
 - https://github.com/mlflow/mlflow
 - https://github.com/mlflow/mlflow/commits/v2.9.2
 - https://huntr.com/bounties/c6f59480-ce47-4f78-a3dc-4bd8ca15029c