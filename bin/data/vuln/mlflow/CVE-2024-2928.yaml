info:
  name: mlflow
  cve: CVE-2024-2928
  summary: mlflow中的本地文件包含漏洞
  details: |
    在mlflow/mlflow的版本2.9.2中发现了本地文件包含（LFI）漏洞，该漏洞在版本2.11.3中得到修复。此漏洞是由于应用程序未能正确验证URI片段中的目录遍历序列（如'../'）。攻击者可以通过操纵URI的片段部分读取本地文件系统上的任意文件，包括'/etc/passwd'等敏感文件。此漏洞是对之前仅解决URI查询字符串内类似操纵的补丁的绕过，这突显了全面验证URI所有部分以防止LFI攻击的必要性。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到mlflow>=2.11.3以解决此问题。
rule: version >= "0" && version < "2.11.3"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-2928
 - https://github.com/mlflow/mlflow/commit/96f0b573a73d8eedd6735a2ce26e08859527be07
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/19bf02d7-6393-4a95-b9d0-d6d4d2d8c298