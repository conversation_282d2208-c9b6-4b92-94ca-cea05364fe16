info:
  name: mlflow
  cve: CVE-2024-3099
  summary: mlflow中的未定义行为漏洞
  details: |
    mlflow/mlflow版本2.11.1中存在一个漏洞，攻击者可以通过利用URL编码创建多个同名的模型。这个缺陷可能导致服务拒绝（DoS），因为认证用户可能无法使用预期的模型，每次都会打开一个不同的模型。此外，攻击者可以利用此漏洞通过创建同名模型进行数据模型投毒，可能导致认证用户成为受害者，使用被投毒的模型。该问题源于对模型名称验证不足，允许创建URL编码名称的模型，这些模型被视为与其URL解码的对应物不同。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: 升级到mlflow>=2.11.3以解决此问题。
rule: version >= "0" && version < "2.11.3"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3099
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/8d96374a-ce8d-480e-9cb0-0a7e5165c24a