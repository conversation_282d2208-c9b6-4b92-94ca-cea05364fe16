info:
  name: mlflow
  cve: CVE-2023-6975
  summary: MLFlow 路径遍历漏洞
  details: |
    恶意用户可以利用此问题在易受攻击的机器上执行命令，并获取数据和模型信息。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到mlflow>=2.9.2以解决此问题。
rule: version >= "0" && version < "2.9.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-6975
  - https://github.com/mlflow/mlflow/commit/b9ab9ed77e1deda9697fe472fb1079fd428149ee
  - https://github.com/mlflow/mlflow
  - https://huntr.com/bounties/029a3824-cee3-4cf1-b260-7138aa539b85