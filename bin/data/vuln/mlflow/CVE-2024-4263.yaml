info:
  name: mlflow
  cve: CVE-2024-4263
  summary: MLflow 允许低权限用户删除任意工件
  details: |
    mlflow/mlflow 在 2.10.1 版本之前存在一个访问控制漏洞，低权限用户仅具有实验的 EDIT 权限时可以删除任意工件。这个问题是由于缺乏对具有 EDIT 权限用户的 DELETE 请求的正确验证，允许他们执行未经授权的工件删除。该漏洞具体影响应用程序内工件删除的处理，例如低权限用户可以使用 DELETE 请求删除工件内的目录，尽管官方文档指出具有 EDIT 权限的用户只能读取和更新工件，而不能删除它们。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: 升级到 mlflow >= 2.10.1 版本以解决此问题。
rule: version < "2.10.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-4263
 - https://github.com/mlflow/mlflow/commit/b43e0e3de5b500554e13dc032ba2083b2d6c94b8
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2024-51.yaml
 - https://huntr.com/bounties/bfa116d3-2af8-4c4a-ac34-ccde7491ae11