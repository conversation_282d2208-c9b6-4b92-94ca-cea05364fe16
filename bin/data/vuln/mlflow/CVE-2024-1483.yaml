info:
  name: mlflow
  cve: CVE-2024-1483
  summary: mlflow 路径遍历漏洞
  details: |
    mlflow/mlflow 版本 2.9.2 中存在路径遍历漏洞，允许攻击者访问服务器上的任意文件。通过制作一系列带有特殊制作的 'artifact_location' 和 'source' 参数的 HTTP POST 请求，使用 '#' 代替 '?' 的本地 URI，攻击者可以遍历服务器的目录结构。该问题是由于服务器处理程序中对用户提供的输入验证不足所致。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 mlflow >= 2.12.1 版本以解决此问题。
rule: version <= "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1483
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/52a3855d-93ff-4460-ac24-9c7e4334198d