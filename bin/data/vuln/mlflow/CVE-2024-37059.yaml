info:
  name: mlflow
  cve: CVE-2024-37059
  summary: M<PERSON><PERSON> 不安全的反序列化
  details: |
    在运行版本为0.5.0或更新的MLflow平台中，可能会发生对不受信任数据的反序列化，这使得恶意上传的PyTorch模型在与最终用户的系统交互时能够执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到mlflow版本大于2.14.1以解决此问题，或者应用官方发布的安全补丁来修复不安全的反序列化漏洞。
rule: version >= "0.5.0" && version <= "2.14.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-37059
 - https://github.com/mlflow/mlflow
 - https://hiddenlayer.com/sai-security-advisory/mlflow-june2024