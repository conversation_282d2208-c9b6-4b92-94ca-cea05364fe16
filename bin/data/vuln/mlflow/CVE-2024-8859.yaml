info:
  name: mlflow
  cve: CVE-2024-8859
  summary: mlflow 版本 2.15.1 中的路径遍历漏洞
  details: |
    mlflow/mlflow 版本 2.15.1 中存在路径遍历漏洞。当用户配置和使用 dbfs 服务时，将 URL 直接连接到文件协议会导致任意文件读取漏洞。这个问题发生的原因是只检查了 URL 的路径部分，而没有处理查询和参数部分。如果用户配置了 dbfs 服务，并且在使用过程中，该服务被挂载到本地目录，就会触发漏洞。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: 高
  security_advise: |
    1. 升级到 mlflow >= 2.15.2
    2. 审查并修改 dbfs 服务的配置以正确处理 URL，确保查询和参数部分被清理。
    3. 在文件操作周围实施额外的安全检查，以防止任意文件读取。
rule: version == "2.15.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8859
  - https://github.com/mlflow/mlflow/commit/7791b8cdd595f21b5f179c7b17e4b5eb5cbbe654
  - https://huntr.com/bounties/2259b88b-a0c6-4c7c-b434-6aacf6056dcb