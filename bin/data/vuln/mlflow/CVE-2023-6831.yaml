info:
  name: mlflow
  cve: CVE-2023-6831
  summary: MLflow 路径遍历漏洞
  details: |
    在 GitHub 仓库 mlflow/mlflow 版本 2.9.2 之前，存在路径遍历漏洞，攻击者可以利用 '\\..\\filename' 进行路径遍历。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 mlflow >= 2.9.2 版本以修复此漏洞。
rule: version < "2.9.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6831
 - https://github.com/mlflow/mlflow/commit/1da75dfcecd4d169e34809ade55748384e8af6c1
 - https://github.com/mlflow/mlflow
 - https://github.com/pypa/advisory-database/tree/main/vulns/mlflow/PYSEC-2023-253.yaml
 - https://huntr.com/bounties/0acdd745-0167-4912-9d5c-02035fe5b314