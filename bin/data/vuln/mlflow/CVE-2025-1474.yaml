info:
  name: mlflow
  cve: CVE-2025-1474
  summary: 管理员可以在mlflow版本2.18中创建用户帐户而不设置密码
  details: |
    在mlflow/mlflow版本2.18中，管理员能够创建一个新的用户帐户而不设置密码。
    此漏洞可能导致安全风险，因为没有密码的帐户可能容易受到未经授权的访问。
    此外，此问题违反了安全用户帐户管理的最佳实践。该问题在版本2.19.0中得到修复。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:L/I:L/A:N
  severity: LOW
  security_advise: |
    1. 升级至mlflow >= 2.19.0
    2. 确保所有用户帐户都有强密码
    3. 审查并执行用户帐户创建的密码策略
rule: version >= "2.18.0" && version < "2.19.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1474
  - https://github.com/mlflow/mlflow/commit/149c9e18aa219bc47e86b432e130e467a36f4a17
  - https://huntr.com/bounties/e79f7774-10fe-46b2-b522-e73b748e3b2d