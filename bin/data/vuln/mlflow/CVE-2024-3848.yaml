info:
  name: mlflow
  cve: CVE-2024-3848
  summary: MLflow存在本地文件读取/路径遍历绕过漏洞
  details: |
    mlflow/mlflow版本2.11.0中存在一个路径遍历漏洞，该漏洞被识别为之前已解决的CVE-2023-6909的绕过。漏洞源于应用程序对artifact URL的处理，其中'#'字符可用于在片段中插入路径，从而跳过验证。这允许攻击者构造一个URL，在处理时会忽略协议方案，并使用提供的路径进行文件系统访问。结果，攻击者可以通过利用应用程序将URL转换为文件系统路径的方式读取任意文件，包括SSH和云密钥等敏感信息。该问题源于对URL片段部分验证不足，导致通过路径遍历进行任意文件读取。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到mlflow>=2.12.1版本以解决此问题。
rule: version >= "2.9.2" && version < "2.12.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3848
 - https://github.com/mlflow/mlflow/commit/f8d51e21523238280ebcfdb378612afd7844eca8
 - https://github.com/mlflow/mlflow
 - https://huntr.com/bounties/8d5aadaa-522f-4839-b41b-d7da362dd610