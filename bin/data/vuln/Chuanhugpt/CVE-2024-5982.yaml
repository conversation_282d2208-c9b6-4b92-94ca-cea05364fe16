info:
  name: Chuanhugpt
  cve: CVE-2024-5982
  summary: Chuanhugpt 路径遍历漏洞
  details: |
    在最新版本的 gaizhenbiao/chuanhuchatgpt 中存在路径遍历漏洞。该漏洞源于多个功能中未经处理的输入处理，包括用户上传、目录创建和模板加载。具体来说，modules/models/base_model.py 中的 load_chat_history 函数允许任意文件上传，可能导致远程代码执行（RCE）。utils.py 中的 get_history_names 函数允许任意目录创建。此外，utils.py 中的 load_template 函数可以被利用来泄露 CSV 文件的第一列。这些问题源于使用 os.path.join 连接用户输入和目录路径时未进行适当的清理。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: 建议升级到修复了此漏洞的 chuanhuchatgpt 版本，或者应用官方发布的安全补丁来解决此问题。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-5982
 - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/952fc8c3cbacead858311747cddd4bedcb4721d7
 - https://huntr.com/bounties/5d5c5356-e893-44d1-b5ca-642aa05d96bb