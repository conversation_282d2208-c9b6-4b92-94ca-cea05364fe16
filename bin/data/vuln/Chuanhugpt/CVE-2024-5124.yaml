info:
  name: Chuanhugpt
  cve: CVE-2024-5124
  summary: Chuanhugpt 密码比较逻辑中的定时攻击漏洞
  details: |
    在 gaizhenbiao/chuanhuchatgpt 代码仓库中存在一个定时攻击漏洞，具体位于密码比较逻辑中。该漏洞存在于软件版本 20240310 中，密码使用 Python 中的 '=' 操作符进行比较。这种比较方法允许攻击者根据每个字符比较的时间来猜测密码。问题出现在检查特定用户名的密码的代码段，可能导致敏感信息泄露给未授权的行为者。利用此漏洞的攻击者可能会猜测用户密码，从而危害系统的安全。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 chuanhugpt 版本号大于 20240310 以解决此问题，或者修改密码比较逻辑，采用不可预测的比较时间来防止定时攻击。
rule: version < "20240310"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-5124
 - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/e46ec4ecd896bc3c88eb9a2f44e8593f3c6761b4
 - https://huntr.com/bounties/e85ec077-930a-4597-975f-9341d2805641