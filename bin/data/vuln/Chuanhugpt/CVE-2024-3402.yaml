info:
  name: Chuanhugpt
  cve: CVE-2024-3402
  summary: Chuanhugpt 存储型跨站脚本（XSS）漏洞
  details: |
    在 gaizhenbiao/chuanhuchatgpt 版本 (20240121) 中存在存储型跨站脚本（XSS）漏洞，由于对模型输出数据的消毒和验证不足。尽管进行了用户输入验证工作，但应用程序未能正确消毒或验证模型的输出，允许在用户浏览器的上下文中注入和执行恶意 JavaScript 代码。此漏洞可能导致在其他用户的浏览器上下文中执行任意 JavaScript 代码，可能会导致受害者的浏览器被劫持。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 chuanhugpt 的最新版本以解决此问题，确保对模型输出数据进行适当的消毒和验证。
rule: version = "20240121"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3402
 - https://huntr.com/bounties/389570c4-0bf2-4bc3-84f5-2e7afdba8ed1