info:
  name: Chuanhugpt
  cve: CVE-2024-9107
  summary: Chuanhugpt聊天GPT中存在存储型跨站脚本（XSS）漏洞
  details: |
    Chuanhugpt（gaizhenbiao/chuanhuchatgpt 仓库）中存在存储型跨站脚本（XSS）漏洞，影响版本为 git 20b2e02。
    该漏洞是由于聊天记录上传中对 HTML 标签的不当清理引起的。具体来说，清理逻辑未能正确处理代码块内的 HTML 标签，允许攻击者注入恶意脚本。
    这可能导致在用户浏览器的上下文中执行任意 JavaScript 代码，有可能造成身份盗窃或其他恶意行为。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到Chuanhugpt聊天GPT的最新版本以获取修复。
    2. 对所有用户生成的内容，尤其是在聊天记录上传中进行严格的输入验证和清理。
    3. 定期审查和更新安全措施以防止类似漏洞。
rule: version < ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9107
  - https://huntr.com/bounties/a2972c51-4780-4f60-afbf-a7a8ee4066ea