info:
  name: Chuanhugpt
  cve: CVE-2024-8143
  summary: Chuanhugpt 用户聊天记录泄露漏洞
  details: |
    在最新版本（20240628）的gaizhenbiao/chuanhuchatgpt中，/file端点存在一个问题，允许认证用户访问其他用户的聊天记录。当用户登录时，会在历史文件夹中创建一个以用户名命名的目录。通过操纵/file端点，认证用户可以枚举并访问其他用户目录中的文件，导致未经授权的私人聊天记录访问。此漏洞可被利用来读取任何用户的私人聊天记录。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到修复此问题的Chuanhugpt版本或应用官方发布的安全补丁。同时，建议加强用户权限管理，限制对聊天记录文件的访问。
rule: version == "20240628"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8143
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/ccc7479ace5c9e1a1d9f4daf2e794ffd3865fc2b
  - https://huntr.com/bounties/71c5ea4b-524a-4173-8fd4-2fbabd69502e