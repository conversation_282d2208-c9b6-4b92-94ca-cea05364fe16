info:
  name: Chuanhugpt
  cve: CVE-2024-6255
  summary: Chuanhuchatgpt JSON文件处理漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt版本20240410中存在JSON文件处理漏洞，允许任何用户删除服务器上的任何JSON文件，包括关键的配置文件如`config.json`和`ds_config_chatbot.json`。此问题是由于文件路径验证不当，导致目录遍历攻击成为可能。攻击者可以利用此漏洞破坏系统的正常运行，操纵设置，或可能导致数据丢失或损坏。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:H
  severity: HIGH
  security_advise: 升级到修复了此漏洞的Chuanhuchatgpt版本，确保对文件路径进行严格的验证，防止目录遍历攻击。
rule: version == "20240410"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-6255
 - https://huntr.com/bounties/48f3e370-6dcd-4f38-9350-d0419b3a7f82