info:
  name: Chuanhugpt
  cve: CVE-2024-9159
  summary: gaizhenbiao/chuanhuchatgpt中存在不正确的授权漏洞，允许任何用户重启服务器。
  details: |
    该漏洞是由于负责重启服务器的函数中授权检查不当引起的。
    这使得任何用户都可以随意重启服务器，可能导致完全丧失可用性。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: 中等
  security_advise: |
    1. 审查并更新服务器重启功能的授权逻辑，以确保只有授权用户可以执行此操作。
    2. 实施适当的管理员检查以保护敏感功能。
    3. 定期审计和测试授权机制，以防止类似漏洞。
rule: version == "git c91dbfc"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-9159
  - https://huntr.com/bounties/ab0f8fbb-c17a-45a7-8dab-7d4c8b90490a