info:
  name: Chuanhugpt
  cve: CVE-2024-4321
  summary: Chuanhugpt 本地文件包含漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt 应用程序中存在本地文件包含（LFI）漏洞，具体存在于上传聊天记录的功能中。漏洞产生是由于在处理聊天记录上传过程中的文件路径时输入验证不当。攻击者可通过拦截请求并操纵“name”参数指定任意文件路径来利用此漏洞。这允许攻击者读取服务器上的敏感文件，导致信息泄露，包括 API 密钥和私人信息。此问题影响应用程序版本 20240310。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 chuanhugpt 版本大于 20240310 以解决此问题。
rule: version = "20240310"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-4321
 - https://huntr.com/bounties/19a16f8e-3d92-498f-abc9-8686005f067e