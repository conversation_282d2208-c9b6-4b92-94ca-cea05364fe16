info:
  name: Chuanhugpt
  cve: CVE-2024-6036
  summary: Chuanhugpt 任意重启服务器漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt 版本 20240410 存在一个漏洞，允许任何用户通过向 `/queue/join?` 端点发送带有 `\"fn_index\":66` 的特定请求来随意重启服务器。这种不受限制的服务器重启能力可能会严重干扰服务可用性，导致数据丢失或损坏，并可能危及系统完整性。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: 升级到 chuanhugpt 的最新版本以解决此问题，并检查服务器的安全配置，确保只有授权用户可以执行重启操作。
rule: version == "20240410"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-6036
 - https://huntr.com/bounties/e9eaaea9-5750-4955-9142-2f12ad4b06db