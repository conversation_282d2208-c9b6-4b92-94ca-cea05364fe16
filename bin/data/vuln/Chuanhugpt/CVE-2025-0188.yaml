info:
  name: Chuanhugpt
  cve: CVE-2025-0188
  summary: gaizhenbiao/chuanhuchatgpt中的服务器端请求伪造（SSRF）漏洞
  details: |
    在gaizhenbiao/chuanhuchatgpt版本20240914中发现了一个服务器端请求伪造（SSRF）漏洞。
    该漏洞允许攻击者通过将响应保存在以目标URL的SHA-1哈希命名的文件夹中来构造响应链接。
    这使攻击者能够直接访问响应，可能导致未经授权的内部系统访问、数据盗窃、服务中断或进一步的攻击，如端口扫描和访问元数据端点。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: 中等
  security_advise: |
    1. 升级到gaizhenbiao/chuanhuchatgpt的最新版本。
    2. 实施严格的输入验证以防止SSRF攻击。
    3. 监控和限制对内部系统和服务的访问。
rule: version < "20240915"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0188
  - https://huntr.com/bounties/879d2470-eca5-49c0-b3d1-57469cfff412