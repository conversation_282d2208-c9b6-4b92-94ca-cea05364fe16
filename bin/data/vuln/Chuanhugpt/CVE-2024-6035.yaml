info:
  name: Chuanhugpt
  cve: CVE-2024-6035
  summary: Chuanhuchatgpt 存储型跨站脚本（XSS）漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt 版本 20240410 存在存储型跨站脚本（XSS）漏洞。此漏洞允许攻击者将恶意 JavaScript 代码注入到聊天历史文件中。当受害者上传此文件时，恶意脚本将在受害者的浏览器中执行。这可能导致用户数据盗窃、会话劫持、恶意软件分发和网络钓鱼攻击。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: 升级到 chuanhuchatgpt 的最新版本以解决此问题，同时确保对用户输入进行严格的验证和过滤，防止恶意脚本的注入。
rule: version == "20240410"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-6035
 - https://huntr.com/bounties/e4e8da71-53a9-4540-8d70-6b670b076987