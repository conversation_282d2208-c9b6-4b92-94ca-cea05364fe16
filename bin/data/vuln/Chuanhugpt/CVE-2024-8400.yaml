info:
  name: Chuanhugpt
  cve: CVE-2024-8400
  summary: gaizhenbiao/chuanhuchatgpt中存在存储型跨站脚本（XSS）漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt的最新版本中存在一个存储型跨站脚本（XSS）漏洞。
    该漏洞允许攻击者上传包含JavaScript代码的恶意HTML文件，
    当文件被访问时，JavaScript代码将被执行。这可能导致在用户浏览器的上下文中执行任意JavaScript。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: 中等
  security_advise: |
    1. 更新到解决此漏洞的gaizhenbiao/chuanhuchatgpt的最新版本。
    2. 实施输入验证以防止恶意HTML和JavaScript上传。
    3. 定期审查并修补依赖项中发现的任何安全漏洞。
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8400
  - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/2cca68e34f029babbe4eaa5a77d220dad68fdd49
  - https://huntr.com/bounties/405f16b8-848e-427d-a61a-ea7d3fd6f0e3