info:
  name: Chuanhugpt
  cve: CVE-2024-2217
  summary: Chuanhugpt 访问控制不当漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt 存在访问控制不当的问题，允许未经授权访问 `config.json` 文件。该漏洞存在于应用程序的认证和未认证版本中，使攻击者能够获取敏感信息，如 API 密钥（`openai_api_key`、`google_palm_api_key`、`xmchat_api_key` 等）、配置详情和用户凭据。此问题源于应用程序对 `config.json` 文件的 HTTP 请求处理不当，未基于用户认证进行适当的访问限制。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 请及时关注官方发布的安全更新，并按照相关指引进行修复，以确保应用程序的安全性。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-2217
 - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/c5ae3b5ae6b47259e0ce8730e0a47e85121f4a7d
 - https://huntr.com/bounties/e4df74bf-b2ee-490f-a9c9-e5c8010b8b29