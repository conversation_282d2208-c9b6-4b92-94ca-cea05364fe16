info:
  name: Chuanhugpt
  cve: CVE-2024-3404
  summary: Chuanhugpt 访问控制漏洞
  details: |
    在 gaizhenbiao/chuanhuchatgpt 项目中，特别是标记为 20240121 的版本中，存在由于不恰当的访问控制机制导致的漏洞。该漏洞允许经过身份验证的攻击者绕过预期的访问限制并读取其他用户的 `history` 文件，可能导致未经授权访问敏感信息。该漏洞存在于应用程序对 `history` 路径的访问控制处理中，没有足够的机制来防止经过身份验证的用户访问其他用户的聊天历史文件。此问题构成了重大风险，因为它可能允许攻击者从其他用户的聊天历史中获得敏感信息。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 chuanhugpt >= 20240122 版本以解决此问题，或者修改应用程序的访问控制机制，确保经过身份验证的用户无法访问其他用户的聊天历史文件。
rule: version = "20240121"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-3404
 - https://huntr.com/bounties/ed32fc32-cb8f-4fbd-8209-cc835d279699