info:
  name: Chuanhugpt
  cve: CVE-2024-10955
  summary: gaizhenbiao/chuanhuchatgpt中的正则表达式拒绝服务（ReDoS）漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt中存在正则表达式拒绝服务（ReDoS）漏洞，截至提交20b2e02。服务器使用正则表达式模式`r'<[^>]+>'`来解析用户输入。在Python的默认正则表达式引擎中，此模式匹配某些特制的输入可能需要多项式时间。攻击者可以通过上传恶意JSON有效载荷来利用这一点，导致服务器在较长时间内消耗100%的CPU。这可能导致拒绝服务（DoS）情况，可能影响整个服务器。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:N/A:H
  severity: MEDIUM
  security_advise: |
    1. 将gaizhenbiao/chuanhuchatgpt更新到解决此漏洞的最新版本。
    2. 审查并修改用于用户输入解析的正则表达式模式以防止ReDoS攻击。
    3. 实施输入验证和速率限制以减轻潜在攻击的影响。
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10955
  - https://huntr.com/bounties/8291f8d0-5060-47e7-9986-1f411310fb7b