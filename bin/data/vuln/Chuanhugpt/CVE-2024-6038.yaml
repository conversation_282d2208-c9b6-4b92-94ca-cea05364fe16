info:
  name: Chuanhugpt
  cve: CVE-2024-6038
  summary: Chuanhugpt的正则表达式拒绝服务（ReDoS）漏洞
  details: |
    在gaizhenbiao/chuanhuchatgpt的最新版本中存在正则表达式拒绝服务（ReDoS）漏洞。该漏洞位于utils.py模块中的filter_history函数。此函数接收用户提供的关键字，并尝试使用正则表达式搜索与聊天历史文件名匹配。由于缺乏对关键字参数的消毒或验证，攻击者可以注入特制的正则表达式，导致拒绝服务情况。这可能会导致服务性能严重下降和潜在的系统不可用。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: 建议更新至修复了该漏洞的Chuanhugpt版本或应用官方发布的安全补丁。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-6038
 - https://huntr.com/bounties/d41cca0a-82bc-4cbf-a52a-928d304fb42d