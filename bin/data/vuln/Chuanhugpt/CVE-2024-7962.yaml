info:
  name: Chuanhugpt
  cve: CVE-2024-7962
  summary: Chuanhugpt 任意文件读取漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt 版本 20240628 存在任意文件读取漏洞，这是由于加载提示模板文件时验证不足导致的。攻击者可以使用绝对路径读取符合特定条件的任何文件。文件不能有 .json 扩展名，并且除了第一行之外，每行都必须包含逗号。此漏洞允许读取符合格式的文件的部分内容，包括代码和日志文件，这些文件可能包含高度敏感的信息，例如帐户凭据。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 chuanhuchatgpt 的最新版本以解决此问题。
rule: version == "20240628"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-7962
 - https://github.com/gaizhenbiao/chuanhuchatgpt/commit/2836fd1db3efcd5ede63c0e7fbbdf677730dbb51
 - https://huntr.com/bounties/83f0a8e1-490c-49e7-b334-02125ee0f1b1