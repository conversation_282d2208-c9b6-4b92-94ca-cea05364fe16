info:
  name: Chuanhugpt
  cve: CVE-2024-6090
  summary: Chuanhugpt 存在路径遍历漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt 版本 20240410 存在路径遍历漏洞，允许任何用户删除其他用户的聊天记录。此漏洞还可以被利用来删除目标系统上任何以 `.json` 结尾的文件，导致服务拒绝，因为用户无法进行身份验证。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: 升级到最新版本的 chuanhuchatgpt 以解决此问题，并检查系统中是否有未授权的 `.json` 文件删除情况，确保系统的完整性和服务的可用性。
rule: version == "20240410"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-6090
 - https://huntr.com/bounties/bd0f8f89-5c8a-4662-89aa-a6861d84cf4c