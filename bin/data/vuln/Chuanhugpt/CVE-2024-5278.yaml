info:
  name: Chuanhugpt
  cve: CVE-2024-5278
  summary: Chuanhugpt 存在不受限制的文件上传漏洞
  details: |
    gaizhenbiao/chuanhuchatgpt 在其 `/upload` 端点由于对上传文件类型的验证不足而存在不受限制的文件上传漏洞。具体来说，`handle_file_upload` 函数未对上传文件的扩展名或内容类型进行消毒或验证，允许攻击者上传具有任意扩展名的文件，包括包含 XSS 有效载荷的 HTML 文件和 Python 文件。此漏洞存在于截至 20240310 的最新版本中，可能导致存储型 XSS 攻击，并可能在托管应用程序的服务器上导致远程代码执行 (RCE)。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到最新版本或修改 `/upload` 端点的文件验证逻辑，确保对上传文件的扩展名和内容类型进行严格的验证和消毒。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-5278
 - https://huntr.com/bounties/ea821d86-941b-40f3-a857-91f758848e05