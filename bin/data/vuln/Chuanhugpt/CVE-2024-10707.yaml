info:
  name: Chuanhugpt
  cve: CVE-2024-10707
  summary: 由于gradio组件问题，gaizhenbiao/chuanhuchatgpt中存在本地文件包含漏洞
  details: |
    该漏洞源于gaizhenbiao/chuanhuchatgpt版本git d4ec6a3中使用了gradio组件gr.JSON。
    它允许未经身份验证的用户通过上传特制的JSON文件并利用handle_dataset_selection函数中不恰当的输入验证来访问服务器上的任意文件。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: 中等
  security_advise: |
    1. 升级到解决此漏洞的gaizhenbiao/chuanhuchatgpt版本。
    2. 审查并增强文件上传和JSON处理的输入验证。
    3. 监控与文件访问和上传功能相关的任何可疑活动。
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10707
  - https://huntr.com/bounties/98fdedea-6ad0-4157-b7d2-ae71c9786ee8