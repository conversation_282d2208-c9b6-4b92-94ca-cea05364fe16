info:
  name: jupyter-notebook
  cve: CVE-2019-9644
  summary: Jupyter Notebook 中网页生成时输入的不当中和
  details: |
    Jupyter Notebook 在 5.7.6 之前的版本中存在一个跨站脚本包含（XSSI）漏洞，允许在用户通过 Jupyter 服务器认证的情况下，恶意页面包含资源。通过 Internet Explorer 捕获错误消息可以展示资源内容，尽管在其他浏览器中未重现。这是因为 Internet Explorer 的错误消息可以包含遇到的任何无效 JavaScript 的内容。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 jupyter-notebook >= 5.7.6 版本以解决此漏洞。
rule: version < "5.7.6"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2019-9644
 - https://github.com/jupyter/notebook
 - https://github.com/jupyter/notebook/compare/f3f00df...05aa4b2
 - https://github.com/pypa/advisory-database/tree/main/vulns/notebook/PYSEC-2019-159.yaml
 - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/UP5RLEES2JBBNSNLBR65XM6PCD4EMF7D
 - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/VMDPJBVXOVO6LYGAT46VZNHH6JKSCURO