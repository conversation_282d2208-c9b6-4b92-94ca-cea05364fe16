info:
  name: jupyter-server
  cve: CVE-2024-28179
  summary: Jupyter Server Proxy的Websocket代理不需要身份验证
  details: |
    `jupyter-server-proxy`用于将本地Jupyter服务器监听的网络流量通过代理暴露给经过身份验证的Jupyter服务器用户。但是，`jupyter-server-proxy`在代理Websocket时没有适当地检查用户身份验证，允许任何有网络访问权限的人未经身份验证即可访问Jupyter服务器端点。
    
    这个漏洞可能允许未经身份验证的远程访问任何通过`jupyter-server-proxy`设置的Websocket端点。在许多情况下（例如通过`jupyter-rsession-proxy`暴露RStudio或通过`jupyter-remote-desktop-proxy`暴露远程Linux桌面/VNC时），这会导致**远程未经身份验证的任意代码执行**。
    
    修复建议包括升级`jupyter-server-proxy`到修补版本并重启任何运行的Jupyter服务器。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级`jupyter-server-proxy`到修补版本（>=3.2.3且!=4.0.0且!=4.1.0）并重启任何运行的Jupyter服务器。
rule: version < "3.2.3" || (version >= "4.0.0" && version < "4.1.1")
references:
  - https://github.com/jupyterhub/jupyter-server-proxy/security/advisories/GHSA-w3vc-fx9p-wp4v
  - https://nvd.nist.gov/vuln/detail/CVE-2024-28179
  - https://github.com/jupyterhub/jupyter-server-proxy