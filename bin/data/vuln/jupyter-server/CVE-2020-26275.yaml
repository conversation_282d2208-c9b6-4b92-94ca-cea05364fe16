info:
  name: jupyter-server
  cve: CVE-2020-26275
  summary: Jupyter Server 开放重定向漏洞
  details: 开放重定向漏洞 - 恶意制作的链接到 Jupyter Server 可能会将浏览器重定向到不同的网站。所有在没有 base_url 前缀的情况下运行的 Jupyter Server 技术上都会受到影响，然而，这些恶意制作的链接只能为已知的 Jupyter Server 主机合理制作。指向您的 Jupyter Server 的链接可能看起来是安全的，但最终会重定向到公共互联网上的伪造服务器。此漏洞已在上游 notebook v5.7.8 中修复。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 jupyter_server >= 1.1.1 以解决此问题，或者如果升级不可用，可以通过在 URL 前缀上运行服务器来解决：`jupyter server --ServerApp.base_url=/jupyter/`。
rule: version < "1.1.1"
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-9f66-54xg-pc2c
  - https://nvd.nist.gov/vuln/detail/CVE-2020-26275
  - https://github.com/jupyter-server/jupyter_server/commit/85e4abccf6ea9321d29153f73b0bd72ccb3a6bca
  - https://advisory.checkmarx.net/advisory/CX-2020-4291
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2020-50.yaml
  - https://pypi.org/project/jupyter-server