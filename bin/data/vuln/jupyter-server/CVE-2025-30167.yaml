info:
  name: jupyter-server
  cve: CVE-2025-30167
  summary: Jupyter Core在Windows上存在不受控搜索路径元素本地权限提升漏洞
  details: |
    在Windows上，会在共享的`%PROGRAMDATA%`目录中搜索配置文件（`SYSTEM_CONFIG_PATH`和`SYSTEM_JUPYTER_PATH`），这可能允许用户创建影响其他用户的配置文件。
    
    仅受影响的共享Windows系统是具有多个用户且`%PROGRAMDATA%`未受保护的系统。
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. 升级到`jupyter_core>=5.8.1`（5.8.0已修复但会破坏`jupyter-server`）
    2. 以管理员身份修改`%PROGRAMDATA%`目录的权限，使其不可被未经授权的用户写入
    3. 以管理员身份创建`%PROGRAMDATA%\\jupyter`目录，并设置适当的限制性权限
    4. 以用户或管理员身份将`%PROGRAMDATA%`环境变量设置为具有适当限制性权限的目录（例如，由管理员或当前用户控制）
rule: version < "5.8.1"
references:
  - https://github.com/jupyter/jupyter_core/security/advisories/GHSA-33p9-3p43-82vq
  - https://nvd.nist.gov/vuln/detail/CVE-2025-30167
  - https://github.com/jupyter/jupyter_core