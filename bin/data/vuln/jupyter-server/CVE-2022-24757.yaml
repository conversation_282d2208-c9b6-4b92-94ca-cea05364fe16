info:
  name: jupyter-server
  cve: CVE-2022-24757
  summary: <PERSON><PERSON><PERSON> notebook 中敏感信息插入日志文件
  details: 当触发 5xx 错误时，默认情况下 Jupyter Server 日志会记录 auth cookie 和其他头部值。考虑到这些日志不需要 root 访问权限，攻击者可以监控这些日志，窃取敏感的 auth/cookie 信息，并获得对 Jupyter Server 的访问权限。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 Jupyter Server 版本 1.15.4 以解决此问题。
rule: version > "0" && version < "1.15.4"
references:
 - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-p737-p57g-4cpr
 - https://nvd.nist.gov/vuln/detail/CVE-2022-24757
 - https://github.com/jupyter-server/jupyter_server/commit/a5683aca0b0e412672ac6218d09f74d44ca0de5a
 - https://github.com/jupyter-server/jupyter_server
 - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2022-179.yaml