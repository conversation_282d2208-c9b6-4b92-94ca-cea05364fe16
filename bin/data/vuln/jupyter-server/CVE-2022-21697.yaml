info:
  name: jupyter-server
  cve: CVE-2022-21697
  summary: jupyter-server-proxy中的SSRF漏洞
  details: |
    ### 影响
    **这是什么类型的漏洞？** 服务器端请求伪造（SSRF）
    **谁受影响？** 部署了启用jupyter-proxy-server扩展的Jupyter Server或Notebook的任何用户。
    缺乏输入验证允许经过身份验证的客户端将请求代理到其他主机，绕过了`allowed_hosts`检查。因为需要身份验证，这已经授予通过内核或终端执行发出相同请求的权限，所以这被认为是低到中等严重程度。
    ### 补丁
    _问题已经修补了吗？用户应该升级到哪些版本？_
    升级到3.2.1版本，或者应用补丁https://github.com/jupyterhub/jupyter-server-proxy/compare/v3.2.0...v3.2.1.patch
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:U/C:H/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到jupyter-server-proxy版本3.2.1或更高。
    2. 如果无法立即升级，请应用可在https://github.com/jupyterhub/jupyter-server-proxy/compare/v3.2.0...v3.2.1.patch获取的补丁。
rule: version <= "3.2.0"
references:
  - https://github.com/jupyterhub/jupyter-server-proxy/security/advisories/GHSA-gcv9-6737-pjqw
  - https://nvd.nist.gov/vuln/detail/CVE-2022-21697
  - https://github.com/jupyterhub/jupyter-server-proxy/commit/fd31930bacd12188c448c886e0783529436b99eb
  - https://github.com/jupyterhub/jupyter-server-proxy
  - https://github.com/jupyterhub/jupyter-server-proxy/compare/v3.2.0...v3.2.1.patch
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server-proxy/PYSEC-2022-16.yaml