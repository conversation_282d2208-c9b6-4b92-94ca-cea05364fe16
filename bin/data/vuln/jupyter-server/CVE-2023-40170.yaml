info:
  name: jupyter-server
  cve: CVE-2023-40170
  summary: jupyter-server中的跨站包含(XSSI)文件漏洞
  details: Improper cross-site credential checks on `/files/` URLs could allow exposure of certain file contents, or accessing files when opening untrusted files via "Open image in new tab".
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到jupyter-server>=2.7.2以解决此问题，或者使用较低性能的`--ContentsManager.files_handler_class=jupyter_server.files.handlers.FilesHandler`，它实现了正确的检查。
rule: version >= "0" && version < "2.7.2"
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-64x5-55rw-9974
  - https://nvd.nist.gov/vuln/detail/CVE-2023-40170
  - https://github.com/jupyter-server/jupyter_server/commit/87a4927272819f0b1cae1afa4c8c86ee2da002fd
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2023-157.yaml
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/NRP7DNZYVOIA4ZB3U3ZWKTFZEPYWNGCQ
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/XDKQAWQN6SQTOVACZNXYKEHWQXGG4DOF