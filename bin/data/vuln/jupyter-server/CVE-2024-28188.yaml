info:
  name: jupyter-server
  cve: CVE-2024-28188
  summary: jupyter-scheduler 的端点缺少身份验证
  details: |
    `jupyter_scheduler` 在 Jupyter Server 的一个 API 端点 (`GET /scheduler/runtime_environments`) 上缺少身份验证检查，该端点列出了服务器上的 Conda 环境名称。在受影响的版本中，`jupyter_scheduler` 允许未经身份验证的用户获取服务器上 Conda 环境名称的列表。这会泄露可能存在于 Conda 环境名称中的任何信息。
    此问题 **不** 允许未经身份验证的第三方读取、修改或进入运行 `jupyter_scheduler` 的服务器上的 Conda 环境。此问题仅泄露 Conda 环境名称的列表。
    受影响的版本: `>=1.0.0,<=1.1.5 ; ==1.2.0 ; >=1.3.0,<=1.8.1 ; >=2.0.0,<=2.5.1`
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到以下版本的 jupyter-scheduler 以解决此问题：`jupyter-scheduler==1.1.6`、`jupyter-scheduler==1.2.1`、`jupyter-scheduler==1.8.2` 或 `jupyter-scheduler==2.5.2`。如果无法升级，服务器操作员可以通过以下命令禁用 `jupyter-scheduler` 扩展：
    ```
    jupyter server extension disable jupyter-scheduler
    ```
rule: (version >= "1.0.0" && version <= "1.1.5") || version == "1.2.0" || (version >= "1.3.0" && version <= "1.8.1") || (version >= "2.0.0" && version <= "2.5.1")
references:
  - https://github.com/jupyter-server/jupyter-scheduler/security/advisories/GHSA-v9g2-g7j4-4jxc
  - https://nvd.nist.gov/vuln/detail/CVE-2024-28188
  - https://github.com/jupyter-server/jupyter_server/pull/1392
  - https://github.com/jupyter-server/jupyter-scheduler/commit/06435a2277bb2b8f441ec9cedafa474572b92c5d
  - https://github.com/jupyter-server/jupyter-scheduler/commit/f4137a779fdf0cc4a9688a42dd8c6e7ade60f044
  - https://github.com/jupyter-server/jupyter-scheduler