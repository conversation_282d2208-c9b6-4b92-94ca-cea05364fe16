info:
  name: jupyter-server
  cve: CVE-2024-22421
  summary: <PERSON><PERSON><PERSON><PERSON>ab 可能存在身份验证和 CSRF 令牌泄露漏洞
  details: JupyterLab 用户点击恶意链接时，如果运行旧版本的 `jupyter-server`，其 `Authorization` 和 `XSRFToken` 令牌可能会泄露给第三方。已修复的版本包括 JupyterLab 4.1.0b2、4.0.11 和 3.6.7。建议用户升级 `jupyter-server` 至 2.7.2 或更新版本以修复重定向漏洞。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: 建议升级 `jupyter-server` 至 2.7.2 或更新版本以解决此问题。
rule: version < "2.7.2"
references:
  - https://github.com/jupyterlab/jupyterlab/security/advisories/GHSA-44cc-43rp-5947
  - https://nvd.nist.gov/vuln/detail/CVE-2024-22421
  - https://github.com/jupyterlab/jupyterlab
  - https://app.intigriti.com/programs/jupyter/jupyter/detail
  - https://commission.europa.eu/news/european-commissions-open-source-programme-office-starts-bug-bounties-2022-01-19_en
  - https://www.intigriti.com/
  - https://lists.fedoraproject.org/archives/list/<EMAIL>/message/UQJKNRDRFMKGVRIYNNN6CKMNJDNYWO2H