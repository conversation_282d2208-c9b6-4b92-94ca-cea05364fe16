info:
  name: jupyter-server
  cve: CVE-2022-29241
  summary: Jupyter server Token暴力破解
  details: | 
    影响范围 Notebook 和 Lab 在 6.4.0（可能更早）到 6.4.11（目前最新版本）。Jupyter Server <=1.16.0。如果我对负责代码的判断正确，它还会影响 Jupyter-Server 1.17.0 和 2.0.0a0。描述: 如果 notebook server 启动时 `root_dir` 的值包含起始用户的 home 目录，那么底层的 REST API 可以通过猜测/暴力破解 jupyter server 的 PID 来泄露启动时分配的访问令牌。虽然这需要一个经过身份验证的用户会话，但这个 URL 可以从 XSS 有效载荷（如 CVE-2021-32798）或从被挂钩或以其他方式受损的浏览器中使用，以将此访问令牌泄露给恶意第三方。此令牌可以与 REST API 一起使用，与 Jupyter 服务/笔记本进行交互，例如修改或覆盖关键文件，如 .bashrc 或 .ssh/authorized_keys，允许恶意用户读取潜在的敏感数据并可能获得对受影响系统的控制。
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 jupyter-server >=1.17.1 或 >=2.0.0a1 以解决此问题。
rule: (version >= "6.4.0" && version <= "6.4.11") || (version >= "1.16.0" && version < "1.17.1") || (version == "2.0.0a0")
references:
  - https://github.com/jupyter-server/jupyter_server/security/advisories/GHSA-q874-g24w-4q9g
  - https://nvd.nist.gov/vuln/detail/CVE-2022-29241
  - https://github.com/jupyter-server/jupyter_server/commit/3485007abbb459585357212dcaa20521989272e8
  - https://github.com/jupyter-server/jupyter_server/commit/877da10cd0d7ae45f8b1e385fa1f5a335e7adf1f
  - https://github.com/jupyter-server/jupyter_server
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server/PYSEC-2022-211.yaml