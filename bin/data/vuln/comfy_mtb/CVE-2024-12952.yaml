info:
  name: comfy_mtb
  cve: CVE-2024-12952
  summary: comfy_mtb代码注入漏洞
  details: 腾讯朱雀实验室发现，在melMass comfy_mtb版本0.1.4及之前版本中发现了一个被归类为严重的漏洞。受影响的是组件Dependency Handler中文件comfy_mtb/endpoint.py的run_command函数。该漏洞可导致代码注入，攻击者可远程发起攻击。该漏洞的利用方法已公开，建议应用补丁来解决此问题。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: CRITICAL
  security_advise: 建议应用名为d6e004cce2c32f8e48b868e66b89f82da4887dc3的补丁来修复此问题。
rule: version <= "0.1.4"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-12952
 - https://github.com/melMass/comfy_mtb/issues/224
 - https://github.com/melMass/comfy_mtb/issues/224#issuecomment-2552664778
 - https://github.com/melMass/comfy_mtb/issues/224#issuecomment-2553432365
 - https://github.com/melMass/comfy_mtb/commit/d6e004cce2c32f8e48b868e66b89f82da4887dc3
 - https://vuldb.com/?ctiid.289315
 - https://vuldb.com/?id.289315
 - https://vuldb.com/?submit.468683