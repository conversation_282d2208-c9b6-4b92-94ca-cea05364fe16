info:
  name: kubepi
  cve: CVE-2023-22463
  summary: KubePi 允许恶意行为者通过硬编码的 Jwtsigkeys 使用伪造的 JWT 令牌登录
  details: KubePi <= v1.6.2 的 jwt 认证功能使用硬编码的 Jwtsigkeys，导致所有在线项目使用相同的 Jwtsigkeys。这意味着攻击者可以伪造任何 jwt 令牌来接管任何在线项目的管理员账户。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 建议升级到 [v1.6.3](https://github.com/KubeOperator/KubePi/releases/tag/v1.6.3) 版本以解决此问题。
rule: version <= "1.6.2"
references:
  - https://github.com/KubeOperator/KubePi/security/advisories/GHSA-vjhf-8vqx-vqpq
  - https://nvd.nist.gov/vuln/detail/CVE-2023-22463
  - https://github.com/KubeOperator/KubePi/commit/3be58b8df5bc05d2343c30371dd5fcf6a9fbbf8b
  - https://github.com/KubeOperator/KubePi/releases/tag/v1.6.3
  - https://github.com/KubeOperator/KubePi/blob/da784f5532ea2495b92708cacb32703bff3a45a3/internal/api/v1/session/session.go#L35