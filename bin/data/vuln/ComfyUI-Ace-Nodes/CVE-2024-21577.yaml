info:
  name: ComfyUI-Ace-Nodes
  cve: CVE-2024-21577
  summary: ComfyUI-Ace-Nodes 存在代码注入漏洞
  details: 腾讯朱雀实验室发现:ComfyUI-Ace-Nodes 中的 ACE_ExpressionEval 节点在其入口函数中包含 eval()，该函数接受任意用户控制的数据。用户可以创建一个工作流，导致在服务器上执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 建议立即更新 ComfyUI 至最新版本或应用官方发布的安全补丁，以修复此代码注入漏洞。同时，应避免在生产环境中使用 eval() 函数处理用户输入，以减少潜在的安全风险。
rule: ""
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-21577
 - https://github.com/hay86/ComfyUI_AceNodes/blob/5ba01db8a3b7afb8e4aecfaa48823ddeb132bbbb/nodes.py#L1193