info:
  name: ollama
  cve: CVE-2024-39719
  summary: Ollama 文件存在泄露漏洞
  details: 在 Ollama 版本 0.3.14 及之前版本中，通过 api/create 接口可以发现文件存在泄露的问题。当调用 CreateModel 路由并传入一个不存在的路径参数时，会向攻击者返回“文件不存在”的错误信息，从而为攻击者提供了服务器上文件存在的线索。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 Ollama 版本 0.3.15 或更高版本以解决此漏洞。
rule: version <= "0.3.14"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-39719
 - https://oligosecurity.webflow.io/blog/more-models-more-probllms
 - https://www.oligo.security/blog/more-models-more-probllms