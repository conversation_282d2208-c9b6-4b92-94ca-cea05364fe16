info:
  name: ollama
  cve: CVE-2024-28224
  summary: Ollama DNS rebinding漏洞
  details: Ollama在0.1.29版本之前存在DNS rebinding漏洞，该漏洞可能无意中允许远程访问完整的API，从而使未经授权的用户能够与大语言模型聊天、删除模型或导致服务拒绝（资源耗尽）。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到ollama>=0.1.29以解决此问题。
rule: version < "0.1.29"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-28224
 - https://github.com/ollama/ollama
 - https://github.com/ollama/ollama/releases
 - https://pkg.go.dev/vuln/GO-2024-2699
 - https://research.nccgroup.com/2024/04/08/technical-advisory-ollama-dns-rebinding-attack-cve-2024-28224
 - https://www.nccgroup.trust/us/our-research/?research=Technical+advisories