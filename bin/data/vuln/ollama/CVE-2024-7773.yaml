info:
  name: ollama
  cve: CVE-2024-7773
  summary: ollama 中由于 ZipSlip 漏洞导致的远程代码执行（RCE）
  details: |
    ollama/ollama 版本 0.1.37 中存在一个漏洞，由于在处理 zip 文件时输入验证不当，允许远程代码执行（RCE）。
    该漏洞被称为 ZipSlip，出现在 server/model.go 的 parseFromZipFile 函数中。
    代码未检查 zip 存档中文件名内的目录遍历序列（../），允许攻击者将任意文件写入文件系统。
    这可以被利用来创建诸如 /etc/ld.so.preload 和恶意共享库之类的文件，从而导致 RCE。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到 ollama >= 0.1.38
    2. 对 zip 存档内的文件名实施严格的输入验证
    3. 定期更新和修补所有软件依赖项
rule: version < "0.1.38"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7773
  - https://github.com/ollama/ollama/commit/123a722a6f541e300bc8e34297ac378ebe23f527
  - https://huntr.com/bounties/aeb82e05-484f-4431-9ede-25a3478d8dbb