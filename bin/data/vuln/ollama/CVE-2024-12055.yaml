info:
  name: ollama
  cve: CVE-2024-12055
  summary: Ollama版本<=0.3.14中的漏洞允许通过恶意gguf模型文件上传导致DoS
  details: |
    Ollama版本<=0.3.14中的一个漏洞允许恶意用户创建一个定制的gguf模型文件，该文件可上传到公共Ollama服务器。
    当服务器处理这个恶意模型时，它会崩溃，导致拒绝服务（DoS）攻击。问题的根本原因是gguf.go文件中的越界读取。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: 高
  security_advise: |
    1. 升级到Ollama版本>0.3.14
    2. 实施服务器端验证，以防止上传的模型文件包含恶意内容
    3. 监控服务器日志，以便发现可能表明发生攻击的不寻常活动
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12055
  - https://huntr.com/bounties/7b111d55-8215-4727-8807-c5ed4cf1bfbe