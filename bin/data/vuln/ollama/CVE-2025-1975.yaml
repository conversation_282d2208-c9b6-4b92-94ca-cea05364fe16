info:
  name: ollama
  cve: CVE-2025-1975
  summary: Ollama 服务器易受拒绝服务 (DoS) 攻击
  details: |
    Ollama 服务器版本 0.5.11 中的一个漏洞允许恶意用户通过自定义清单内容并伪造服务来发起拒绝服务 (DoS) 攻击。这是由于在通过 /api/pull 端点下载模型时，数组索引访问验证不当，可能导致服务器崩溃。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到 ollama >= 0.5.12
    2. 在 /api/pull 端点中实施对数组索引访问的正确验证
    3. 监控服务器日志以发现可能表明 DoS 尝试的不寻常活动
rule: version >= "0" && version < "0.5.12"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-1975
  - https://github.com/ollama/ollama
  - https://huntr.com/bounties/921ba5d4-f1d0-4c66-9764-4f72dffe7acd