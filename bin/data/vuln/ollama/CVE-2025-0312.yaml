info:
  name: ollama
  cve: CVE-2025-0312
  summary: ollama中的漏洞允许通过GGUF模型文件中的空指针解引用导致DoS
  details: |
    ollama/ollama版本<=0.3.14中的一个漏洞允许恶意用户创建一个定制的GGUF模型文件，当该文件上传到Ollama服务器并创建时，可能会因未检查的空指针解引用而导致崩溃。这可能导致通过远程网络的拒绝服务（DoS）攻击。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到ollama >=0.3.15
    2. 对GGUF模型文件实施输入验证以防止空指针解引用
    3. 监控服务器日志以发现可能表明攻击的不寻常活动
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0312
  - https://huntr.com/bounties/522c87b6-a7ac-41b2-84f3-62fd58921f21