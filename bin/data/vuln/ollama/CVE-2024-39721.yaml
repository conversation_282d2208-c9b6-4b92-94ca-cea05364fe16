info:
  name: ollama
  cve: CVE-2024-39721
  summary: Ollama CreateModelHandler 函数文件读取漏洞
  details: 在 Ollama 0.1.34 之前的版本中，CreateModelHandler 函数使用 os.Open 读取文件直至完成。req.Path 参数由用户控制，可以被设置为 /dev/random，这会导致阻塞，使得 goroutine 无限运行（即使在客户端中止 HTTP 请求后）。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: 升级到 ollama >= 0.1.34 版本以解决此问题。
rule: version < "0.1.34"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-39721
 - https://github.com/ollama/ollama/blob/9164b0161bcb24e543cba835a8863b80af2c0c21/server/routes.go#L557
 - https://github.com/ollama/ollama/blob/adeb40eaf29039b8964425f69a9315f9f1694ba8/server/routes.go#L536
 - https://oligosecurity.webflow.io/blog/more-models-more-probllms
 - https://www.oligo.security/blog/more-models-more-probllms