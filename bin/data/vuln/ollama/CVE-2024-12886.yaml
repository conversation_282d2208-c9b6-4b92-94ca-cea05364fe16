info:
  name: ollama
  cve: CVE-2024-12886
  summary: ollama服务器中的内存溢出（OOM）漏洞
  details: |
    `ollama` 服务器版本 0.3.14 存在内存溢出（OOM）漏洞。
    当恶意 API 服务器响应 gzip 恐怖分子 HTTP 响应时，可触发此漏洞，
    导致 `ollama` 服务器崩溃。该漏洞存在于 `makeRequestWithRetry` 
    和 `getAuthorizationToken` 函数中，这些函数使用 `io.ReadAll` 读取响应体。
    这可能导致过度的内存使用和拒绝服务（DoS）条件。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: 高
  security_advise: |
    1. 升级到 ollama >= 0.3.15
    2. 实施输入验证以防止 gzip 恐怖分子响应
    3. 监控服务器内存使用情况，防止异常峰值
rule: version < "0.3.15"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12886
  - https://huntr.com/bounties/f115fe52-58af-4844-ad29-b1c25f7245df