info:
  name: ollama
  cve: CVE-2024-39722
  summary: Ollama API 路由路径遍历漏洞
  details: 在 Ollama 版本低于 0.1.46 中，发现了通过 api/push 路由的路径遍历问题，该漏洞可暴露部署服务器上的文件存在情况。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 ollama >= 0.1.46 版本以修复此漏洞。
rule: version < "0.1.46"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-39722
 - https://oligosecurity.webflow.io/blog/more-models-more-probllms
 - https://www.oligo.security/blog/more-models-more-probllms