info:
  name: ollama
  cve: CVE-2024-39720
  summary: Ollama 越界读取漏洞
  details: 在 Ollama 版本 0.1.46 之前存在一个问题。攻击者可以使用两个 HTTP 请求上传一个仅包含 4 字节且以 GGUF 自定义魔数头开始的畸形 GGUF 文件。通过利用一个包含指向攻击者控制的 blob 文件的 FROM 语句的自定义 Modelfile，攻击者可以通过 CreateModel 路由使应用程序崩溃，导致段错误（信号 SIGSEGV：段违规）。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:H
  severity: HIGH
  security_advise: 升级到 ollama >= 0.1.46 版本以解决此问题。
rule: version < "0.1.46"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-39720
 - https://github.com/ollama/ollama
 - https://github.com/ollama/ollama/compare/v0.1.45...v0.1.46#diff-782c2737eecfa83b7cb46a77c8bdaf40023e7067baccd4f806ac5517b4563131L417
 - https://oligo.security/blog/more-models-more-probllms
 - https://oligosecurity.webflow.io/blog/more-models-more-probllms
 - https://pkg.go.dev/vuln/GO-2024-3245