info:
  name: ollama
  cve: CVE-2025-0315
  summary: ollama中的漏洞允许通过定制的GGUF模型文件上传导致DoS
  details: |
    ollama/ollama <=0.3.14中的一个漏洞允许恶意用户创建一个定制的GGUF模型文件，将其上传到Ollama服务器并进行创建。这可能会导致服务器分配无限内存，从而导致拒绝服务（DoS）攻击。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到ollama >=0.3.15
    2. 对GGUF模型文件实施服务器端验证，以防止无限内存分配
    3. 监控服务器资源使用情况，以便发现可能表明发生攻击的异常峰值
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0315
  - https://huntr.com/bounties/da414d29-b55a-496f-b135-17e0fcec67bc