info:
  name: ollama
  cve: CVE-2025-0313
  summary: ollama中的漏洞允许通过GGUF模型进行DoS攻击
  details: |
    ollama/ollama版本<=0.3.14中的一个漏洞允许恶意用户创建一个GGUF模型，该模型可以导致拒绝服务（DoS）攻击。
    该漏洞是由于在GGUF模型处理代码中不正确地验证数组索引边界造成的，可以通过远程网络利用。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: 高
  security_advise: |
    1. 升级到ollama >=0.3.15
    2. 在GGUF模型处理代码中实施正确的数组索引边界验证
    3. 监控可能表明发生攻击的不寻常网络活动
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0313
  - https://huntr.com/bounties/450c90f9-bc02-4560-afd4-d0aa057ac82c