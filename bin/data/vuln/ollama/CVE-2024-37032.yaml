info:
  name: ollama
  cve: CVE-2024-37032
  summary: Ollama未验证摘要格式（64位十六进制sha256）
  details: Ollama在0.1.34版本之前，获取模型路径时未验证摘要格式（64位十六进制sha256），因此错误处理了如少于64位十六进制、多于64位十六进制或以`../`开头的测试用例。
  cvss: 
  severity: MEDIUM
  security_advise: 升级Ollama至0.1.34版本或更高以解决此问题。
rule: version < "0.1.34"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-37032
 - https://github.com/ollama/ollama/pull/4175
 - https://github.com/ollama/ollama/commit/2a21363bb756a7341d3d577f098583865bd7603f
 - https://github.com/advisories/GHSA-8hqg-whrw-pv92
 - https://github.com/ollama/ollama
 - https://github.com/ollama/ollama/blob/adeb40eaf29039b8964425f69a9315f9f1694ba8/server/modelpath_test.go#L41-L58
 - https://github.com/ollama/ollama/compare/v0.1.33...v0.1.34
 - https://pkg.go.dev/vuln/GO-2024-2901
 - https://www.vicarius.io/vsociety/posts/probllama-in-ollama-a-tale-of-a-yet-another-rce-vulnerability-cve-2024-37032