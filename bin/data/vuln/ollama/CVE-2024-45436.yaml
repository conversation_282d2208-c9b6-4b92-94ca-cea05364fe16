info:
  name: ollama
  cve: CVE-2024-45436
  summary: Ollama 可将 ZIP 存档成员提取到父目录之外
  details: 在 Ollama 的 `model.go` 中的 `extractFromZipFile` 函数在 0.1.47 版本之前，可以将 ZIP 存档成员提取到父目录之外。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N
  severity: HIGH
  security_advise: 升级到 ollama >= 0.1.47 版本以解决此问题。
rule: version < "0.1.47"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-45436
 - https://github.com/ollama/ollama/pull/5314
 - https://github.com/ollama/ollama/commit/123a722a6f541e300bc8e34297ac378ebe23f527
 - https://github.com/ollama/ollama
 - https://github.com/ollama/ollama/compare/v0.1.46...v0.1.47