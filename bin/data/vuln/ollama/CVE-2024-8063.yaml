info:
  name: ollama
  cve: CVE-2024-8063
  summary: ollama/ollama版本v0.3.3中的除零漏洞
  details: |
    ollama/ollama版本v0.3.3中存在除零漏洞。
    当导入具有特制的`block_count`类型的GGUF模型到Modelfile时，就会发生这个漏洞。
    这可能会导致服务器在处理模型时出现拒绝服务（DoS）情况，从而导致服务器崩溃。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到ollama/ollama版本v0.3.4或更高版本
    2. 对Modelfile中的`block_count`实施输入验证以防止特制类型
    3. 监控服务器日志以发现可能表明攻击尝试的不寻常活动
rule: version == "0.3.3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8063
  - https://huntr.com/bounties/fd8e1ed6-21d2-4c9e-8395-2098f11b7db9