info:
  name: ollama
  cve: CVE-2025-0317
  summary: ollama中的漏洞允许通过恶意GGUF模型上传导致DoS
  details: |
    ollama/ollama版本<=0.3.14中的一个漏洞允许恶意用户在Ollama服务器上上传并创建定制的GGUF模型文件。这可能导致ggufPadding函数中的除零错误，使服务器崩溃并导致拒绝服务（DoS）攻击。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到ollama >=0.3.15
    2. 实施服务器端验证GGUF模型文件，以防止恶意上传
    3. 监控服务器日志以发现可能表明攻击的不寻常活动
rule: version <= "0.3.14"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-0317
  - https://huntr.com/bounties/a9951bca-9bd8-49b2-b143-4cd4219f9fa0