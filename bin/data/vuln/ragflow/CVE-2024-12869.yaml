info:
  name: ragflow
  cve: CVE-2024-12869
  summary: RagFlow 中的不当身份验证漏洞允许未经授权查看邀请列表。
  details: |
    在 infiniflow/ragflow 版本 v0.12.0 中，存在一个不当身份验证漏洞，允许用户查看另一个用户的邀请列表。这可能导致隐私泄露，例如邀请列表中的电子邮件地址或用户名等用户的个人或私人信息可能在未经他们同意的情况下被暴露。这种数据泄露可能有助于进一步的攻击，例如网络钓鱼或垃圾邮件，并导致信任丧失和潜在的监管问题。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到 RagFlow 版本 v0.12.1 或更高版本。
    2. 实施适当的身份验证检查，以确保用户只能访问自己的邀请列表。
    3. 监控任何未经授权的访问尝试，并及时响应潜在的安全事件。
rule: version == "0.12.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12869
  - https://huntr.com/bounties/768b1a56-1e79-416a-8445-65953568b04a