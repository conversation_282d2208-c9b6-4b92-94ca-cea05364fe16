info:
  name: ragflow
  cve: CVE-2024-12870
  summary: RagFlow中存在存储型XSS漏洞，允许未经授权的JavaScript执行。
  details: |
    infiniflow/ragflow中存在一个存储型跨站脚本（XSS）漏洞，影响主分支（cec2080）上的最新提交。
    该漏洞允许攻击者上传可以承载任意JavaScript有效载荷的HTML/XML文件。
    这些文件以“application/xml”内容类型提供，浏览器会自动渲染。
    这可能导致在用户浏览器的上下文中执行任意JavaScript，
    有可能允许攻击者窃取cookie并获得对用户文件和资源的未经授权的访问。
    该漏洞不需要身份验证，使任何具有网络访问权限的人都可以访问该实例。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. 立即应用RagFlow发布的最新安全补丁。
    2. 实施严格的文件上传验证，以防止包含JavaScript的HTML/XML文件。
    3. 监控和审查与文件上传相关的可疑活动日志。
    4. 考虑部署Web应用程序防火墙（WAF）来检测和阻止恶意请求。
rule: version = "cec2080" || version < "main"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12870
  - https://huntr.com/bounties/d6b497d2-5c95-4abc-8033-04b8068fed65