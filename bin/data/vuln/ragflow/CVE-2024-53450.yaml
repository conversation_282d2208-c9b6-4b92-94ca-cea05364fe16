info:
  name: ragflow
  cve: CVE-2024-53450
  summary: RAGFlow 文档钩子访问控制不当
  details: RAGFlow 0.13.0 在 document-hooks.ts 中存在不当的访问控制，允许未经授权的用户访问用户文档。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 RAGFlow 的最新版本以解决此问题，确保访问控制得到正确实施。
rule: version = "0.13.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-53450
 - https://github.com/infiniflow/ragflow/blob/cec208051f6f5996fefc8f36b6b71231b1807533/web/src/hooks/document-hooks.ts#L23
 - https://github.com/thanhtung4102/Unauthentication-in-Ragflow