info:
  name: ragflow
  cve: CVE-2024-12433
  summary: RagFlow中由于硬编码的AuthKey和pickle反序列化导致的远程代码执行漏洞
  details: |
    infiniflow/ragflow版本v0.12.0中的一个漏洞允许远程代码执行。
    RagFlow中的RPC服务器使用硬编码的AuthKey，攻击者可以轻松获取该密钥以加入组通信而不受限制。
    此外，服务器使用pickle反序列化处理传入数据，使其容易受到远程代码执行的攻击。
    此问题在版本0.14.0中得到修复。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级至ragflow>=0.14.0
    2. 避免对不可信数据使用pickle进行反序列化
    3. 实施适当的身份验证和授权机制
rule: version >= "0" && version < "0.14.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12433
  - https://github.com/infiniflow/ragflow/commit/49494d4e3c8f06a5e52cf1f7cce9fa03cadcfbf6
  - https://huntr.com/bounties/8a1465af-09e4-42af-9e54-0b70e7c87499