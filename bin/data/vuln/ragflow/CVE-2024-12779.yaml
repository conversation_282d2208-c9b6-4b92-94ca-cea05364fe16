info:
  name: ragflow
  cve: CVE-2024-12779
  summary: infiniflow/ragflow 版本 0.12.0 中的 SSRF 漏洞
  details: |
    infiniflow/ragflow 版本 0.12.0 的 `POST /v1/llm/add_llm` 和 `POST /v1/conversation/tts` 端点存在服务器端请求伪造（SSRF）漏洞。攻击者在添加 `OPENAITTS` 模型时可以指定任意 URL 作为 `api_base`，然后访问 `tts` REST API 端点以从指定 URL 读取内容，可能导致未经授权访问内部网络资源。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到解决此漏洞的 infiniflow/ragflow 版本。
    2. 对 API 请求中提供的 URL 实施严格验证。
    3. 监控和限制来自外部来源对内部网络资源的访问。
rule: version == "0.12.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12779
  - https://huntr.com/bounties/3cc748ba-2afb-4bfe-8553-10eb6d6dd4f0