info:
  name: ragflow
  cve: CVE-2024-12880
  summary: 通过RAGFlow中不安全的数据查询实现部分账户接管
  details: |
    infiniflow/ragflow版本RAGFlow-0.13.0中的一个漏洞允许通过不安全的数据查询实现部分账户接管。该问题源于应用程序中处理租户ID的方式。如果用户可以访问多个租户，他们可以操纵其租户访问权限以查询和访问其他租户的API令牌。此漏洞影响以下端点：/v1/system/token_list、/v1/system/new_token、/v1/api/token_list、/v1/api/new_token和/v1/api/rm。攻击者可以利用此漏洞访问其他租户的API令牌，代表其他租户执行操作并访问其数据。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到RAGFlow版本RAGFlow-0.14.0或更高版本。
    2. 实施严格的租户ID验证，以防止未经授权访问其他租户的数据。
    3. 监控和审核API令牌的使用情况，以发现任何可疑活动。
rule: version < "0.14.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12880
  - https://huntr.com/bounties/c41c7eaa-554a-408c-96be-9dba56113970