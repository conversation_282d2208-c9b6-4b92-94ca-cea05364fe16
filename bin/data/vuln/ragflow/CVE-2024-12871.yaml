info:
  name: ragflow
  cve: CVE-2024-12871
  summary: Ragflow中的XSS漏洞允许会话劫持和数据泄露
  details: |
    infiniflow/ragflow版本0.12.0中的一个XSS漏洞允许攻击者将恶意PDF文件上传到知识库。
    当该文件在Ragflow中查看时，有效载荷将在用户浏览器的上下文中执行。这可能导致会话劫持、
    数据泄露或代表受害者执行未经授权的操作，从而危害敏感用户数据并影响整个应用程序的完整性。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到Ragflow版本0.12.1或更高版本。
    2. 实施严格的文件上传验证，以防止恶意PDF上传。
    3. 定期审查并修补Ragflow应用程序中发现的任何漏洞。
rule: version < "0.12.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12871
  - https://huntr.com/bounties/7903945c-2839-4dd5-9d40-9ef47fe53118