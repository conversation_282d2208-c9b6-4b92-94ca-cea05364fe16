info:
  name: ragflow
  cve: CVE-2024-10131
  summary: Ragflow `add_llm` 函数远程代码执行漏洞
  details: 在infiniflow/ragflow版本0.11.0中，`llm_app.py`文件的`add_llm`函数存在远程代码执行（RCE）漏洞。该函数使用用户提供的输入`req['llm_factory']`和`req['llm_name']`从各种模型字典中动态实例化类。由于缺乏全面的输入验证或消毒，这种方法允许攻击者可能执行任意代码。攻击者可以为' llm_factory '提供一个恶意值，当用作这些模型字典的索引时，会导致执行任意代码。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到ragflow版本大于0.11.0以解决此问题，确保对用户输入进行严格的验证和消毒。
rule: version = "0.11.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-10131
 - https://huntr.com/bounties/42ae0b27-e851-4b58-a991-f691a437fbaa