info:
  name: ragflow
  cve: CVE-2024-12450
  summary: infiniflow/ragflow 版本 0.12.0 中的多个漏洞
  details: |
    `document_app.py`中的`web_crawl`函数包含多个漏洞：
    - 未过滤 URL 参数，通过访问内部网络地址允许完全读取 SSRF。
    - 缺乏对文件协议的限制，可实现任意文件读取。
    - 使用过时的启用 --no-sandbox 模式的 Chromium 无头版本，使应用程序容易受到已知 Chromium v8 漏洞的远程代码执行 (RCE) 影响。
    这些问题在版本 0.14.0 中得到解决。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. 升级到 ragflow>=0.14.0
    2. 审查并限制 `web_crawl`函数中的 URL 参数处理
    3. 实施适当的文件协议限制
    4. 将 Chromium 无头更新到最新版本，并在可能的情况下禁用 --no-sandbox 模式
rule: version >= "0.12.0" && version < "0.14.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12450
  - https://github.com/infiniflow/ragflow/commit/3faae0b2c2f8a26233ee1442ba04874b3406f6e9
  - https://huntr.com/bounties/da06360c-87c3-4ba9-be67-29f6eff9d44a