info:
  name: pyload-ng
  cve: CVE-2024-22416
  summary: pyLoad 中任何 API 调用的跨站请求伪造可能导致管理员权限提升
  details: |
    ### 摘要
    `pyload` API 允许使用 GET 请求进行任何 API 调用。由于会话 cookie 没有设置为 `SameSite: strict`，这使得该库容易受到严重的跨站请求伪造（CSRF）攻击。此概念验证展示了如何使未认证的用户诱骗管理员的浏览器创建新的管理员用户。
    ### 影响
    任何 API 调用都可以通过 CSRF 攻击由未认证的用户发起。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev78 以解决此问题。
rule: version < "0.5.0b3.dev78"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-pgpj-v85q-h5fm
  - https://nvd.nist.gov/vuln/detail/CVE-2024-22416
  - https://github.com/pyload/pyload/commit/1374c824271cb7e927740664d06d2e577624ca3e
  - https://github.com/pyload/pyload/commit/c7cdc18ad9134a75222974b39e8b427c4af845fc
  - https://github.com/pyload/pyload
  - https://github.com/pypa/advisory-database/tree/main/vulns/pyload-ng/PYSEC-2024-17.yaml