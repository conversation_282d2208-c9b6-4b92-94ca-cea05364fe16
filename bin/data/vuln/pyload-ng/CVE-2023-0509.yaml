info:
  name: pyload-ng
  cve: CVE-2023-0509
  summary: pyload-ng 中的不当证书验证
  details: GitHub 仓库 pyload/pyload 在 0.5.0b3.dev44 之前的版本中存在不当证书验证问题。
  cvss: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:N
  severity: HIGH
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev44 以解决此问题。
rule: version < "0.5.0b3.dev44"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-0509
 - https://github.com/pyload/pyload/commit/a9098bdf7406e6faf9df3da6ff2d584e90c13bbb
 - https://github.com/pyload/pyload
 - https://huntr.dev/bounties/a370e0c2-a41c-4871-ad91-bc6f31a8e839