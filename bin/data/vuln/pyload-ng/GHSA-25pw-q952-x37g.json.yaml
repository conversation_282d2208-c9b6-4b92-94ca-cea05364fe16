info:
  name: pyload-ng
  cve: CVE-2024-39205
  summary: pyload-ng 可通过 js2py 沙箱逃逸实现远程代码执行（RCE）
  details: |
    pyload-ng v0.5.0b3.dev85 在 python3.11 或更低版本下运行时存在一个问题，允许攻击者通过特制的 HTTP 请求执行任意代码。
  cvss: UNKNOWN
  severity: HIGH
  security_advise: |
    1. 升级到 pyload-ng 的最新稳定版本。
    2. 关注 pyload-ng 维护者的任何进一步公告或更新。
rule: version == "0.5.0b3.dev85"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-r9pp-r4xf-597r
  - https://nvd.nist.gov/vuln/detail/CVE-2024-39205
  - https://github.com/Marven11/CVE-2024-39205-Pyload-RCE/tree/main
  - https://github.com/pyload/pyload