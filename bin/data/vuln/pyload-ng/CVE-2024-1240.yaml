info:
  name: pyload-ng
  cve: CVE-2024-1240
  summary: Pyload-ng 登录功能开放重定向漏洞
  details: Pyload/pyload 版本 0.5.0 中存在一个开放重定向漏洞，该漏洞是由于登录功能中 'next' 参数处理不当引起的。攻击者可以利用此漏洞将用户重定向到恶意网站，可用于网络钓鱼或其他恶意活动。该问题已在 pyload-ng 0.5.0b3.dev79 中修复。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev79 以解决此问题。
rule: version < "0.5.0b3.dev79"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1240
  - https://github.com/pyload/pyload/commit/fe94451dcc2be90b3889e2fd9d07b483c8a6dccd
  - https://huntr.com/bounties/eef9513d-ccc3-4030-b574-374c5e7b887e