info:
  name: pyload-ng
  cve: CVE-2023-0434
  summary: pyload-ng 输入验证不当
  details: GitHub 仓库 pyload/pyload 在 0.5.0b3.dev40 之前的版本中存在输入验证不当的问题。
  cvss: CVSS:3.0/AV:P/AC:L/PR:H/UI:R/S:U/C:N/I:H/A:H
  severity: MEDIUM
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev40 以解决此问题。
rule: version < "0.5.0b3.dev40"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-0434
 - https://github.com/pyload/pyload/commit/a2b1eb1028f45ac58dea5f58593c1d3db2b4a104
 - https://github.com/pyload/pyload
 - https://huntr.dev/bounties/7d9332d8-6997-483b-9fb9-bcf2ae01dad4