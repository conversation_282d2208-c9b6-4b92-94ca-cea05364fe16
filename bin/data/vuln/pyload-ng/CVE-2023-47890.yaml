info:
  name: pyload-ng
  cve: CVE-2023-47890
  summary: 下载到任意文件夹可能导致远程代码执行
  details: 用户可以通过滥用脚本在pyLoad服务器上的任意位置存储文件并获得命令执行权限。在创建新包时，会在/downloads文件夹内创建一个子目录以存储文件，但编辑包时没有防止措施，用户可以选择文件系统中的任意目录。
  cvss: CVSS:3.1/AV:A/AC:H/PR:H/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到pyload-ng版本0.5.0b3.dev75或更高以解决此问题。
rule: version < "0.5.0b3.dev75"
references:
 - https://github.com/pyload/pyload/security/advisories/GHSA-h73m-pcfw-25h2
 - https://nvd.nist.gov/vuln/detail/CVE-2023-47890
 - https://github.com/pyload/pyload/commit/695bb70cd88608dc4fee18a6a7ecb66722ebfd8f
 - https://github.com/pyload/pyload
 - http://pyload.com