info:
  name: pyload-ng
  cve: CVE-2024-21645
  summary: pyload Log Injection 漏洞
  details: |
    ### 概述
    在 `pyload` 中发现了一个日志注入漏洞。此漏洞允许任何未认证的行为者将任意消息注入到 `pyload` 收集的日志中。
    ### 详情
    当尝试使用错误凭据登录时，`pyload` 会生成一条日志条目，格式为 `<PERSON><PERSON> failed for user 'USERNAME'`。然而，当提供的用户名包含换行符时，这个换行符没有被正确转义。换行符也是日志条目之间的分隔符。这允许攻击者将新的日志条目注入到日志文件中。
    ### 影响
    伪造或损坏的日志文件可以用来掩盖攻击者的踪迹，甚至可以将另一方的行为归咎于恶意行为。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev77 以解决此问题。
rule: version < "0.5.0b3.dev77"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-ghmw-rwh8-6qmr
  - https://nvd.nist.gov/vuln/detail/CVE-2024-21645
  - https://github.com/pyload/pyload/commit/4159a1191ec4fe6d927e57a9c4bb8f54e16c381d
  - https://github.com/pyload/pyload