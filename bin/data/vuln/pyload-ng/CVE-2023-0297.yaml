info:
  name: pyload-ng
  cve: CVE-2023-0297
  summary: pyload-ng代码注入漏洞
  details: 在GitHub仓库pyload/pyload的0.5.0b3.dev31之前的版本中存在代码注入漏洞。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到pyload-ng版本0.5.0b3.dev31或更高版本以解决此漏洞。
rule: version < "0.5.0b3.dev31"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-0297
 - https://github.com/pyload/pyload/commit/7d73ba7919e594d783b3411d7ddb87885aea782d
 - https://huntr.dev/bounties/3fd606f7-83e1-4265-b083-2e1889a05e65
 - http://packetstormsecurity.com/files/171096/pyLoad-js2py-Python-Execution.html
 - http://packetstormsecurity.com/files/172914/PyLoad-0.5.0-Remote-Code-Execution.html