info:
  name: pyload-ng
  cve: CVE-2024-24808
  summary: pyLoad 开放重定向漏洞，由于 is_safe_url 函数验证不当
  details: 登录后重定向用户时，由于输入值的验证不正确导致的开放重定向漏洞。pyload 在登录时通过 `get_redirect_url` 函数验证 URL。`next` 变量中输入的 URL 经过 `is_safe_url` 函数时，由于缺乏验证，可能会将用户重定向到任意域。例如，输入像 `https:///example.com` 这样异常的 URL 时，`urlparse` 将其解释为相对路径，但实际请求中由于 URL 规范化会转换为 `https://example.com` 。攻击者可利用此漏洞将用户重定向到恶意网站，用于网络钓鱼等攻击。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev79 版本以修复此漏洞。
rule: version < "0.5.0b3.dev79"
references:
 - https://github.com/pyload/pyload/security/advisories/GHSA-g3cm-qg2v-2hj5
 - https://nvd.nist.gov/vuln/detail/CVE-2024-24808
 - https://github.com/pyload/pyload/commit/fe94451dcc2be90b3889e2fd9d07b483c8a6dccd
 - https://github.com/pyload/pyload