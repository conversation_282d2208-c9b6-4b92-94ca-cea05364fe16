info:
  name: pyload-ng
  cve: CVE-2024-47821
  summary: pyLoad 通过 /flashgot API 下载到 /.pyload/scripts 存在远程代码执行漏洞
  details: 此漏洞是由于 `/.pyload/scripts` 文件夹中的脚本在某些操作完成时会运行。通过将可执行文件下载到 `/scripts` 路径下的文件夹，并执行相应操作，可以实现远程代码执行。攻击者可以通过更改下载文件夹到 `/scripts` 路径下，并使用 `/flashgot` API 下载文件来实现这一目标。尽管 pyload 尝试通过检查请求的 Host 头和 Referer 头来防止非本地请求访问此 API，但这些检查可以被攻击者绕过。
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev87 以解决此问题。确保下载文件夹和文件权限设置正确，以防止未经授权的脚本执行。
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-w7hq-f2pj-c53g
  - https://nvd.nist.gov/vuln/detail/CVE-2024-47821
  - https://github.com/pyload/pyload/commit/48f59567393a19263c8a0285256a7537dc9ce109
  - https://github.com/pyload/pyload
rule: version < "0.5.0b3.dev87"