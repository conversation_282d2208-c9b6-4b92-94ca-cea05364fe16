info:
  name: pyload-ng
  cve: CVE-2023-0057
  summary: pyLoad存在渲染UI层或框架限制不当的漏洞
  details: 在GitHub仓库pyload/pyload的0.5.0b3.dev33之前版本中存在渲染UI层或框架限制不当的问题。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到pyload-ng版本0.5.0b3.dev33或以上以解决此问题。
rule: version < "0.5.0b3.dev33"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-0057
 - https://github.com/pyload/pyload/commit/bd2a31b7de54570b919aa1581d486e6ee18c0f64
 - https://github.com/pyload/pyload
 - https://huntr.dev/bounties/12b64f91-d048-490c-94b0-37514b6d694d