info:
  name: pyload-ng
  cve: CVE-2023-0055
  summary: Pyload 在 HTTPS 会话中包含没有 'Secure' 属性的敏感 Cookie
  details: 在 GitHub 仓库 pyload/pyload 的 0.5.0b3.dev32 之前的版本中，HTTPS 会话中的敏感 Cookie 没有设置 Secure 属性，这可能会导致用户代理在 HTTP 会话中以明文形式发送这些 Cookie。该问题已在版本 0.5.0b3.dev32 中修复。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev32 以解决此问题。
rule: version < "0.5.0b3.dev32"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2023-0055
  - https://github.com/pyload/pyload/commit/7b53b8d43c2c072b457dcd19c8a09bcfc3721703
  - https://github.com/pyload/pyload
  - https://huntr.dev/bounties/ed88e240-99ff-48a1-bf32-8e1ef5f13cce