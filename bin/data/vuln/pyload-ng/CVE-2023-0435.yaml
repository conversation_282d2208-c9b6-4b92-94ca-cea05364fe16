info:
  name: pyload-ng
  cve: CVE-2023-0435
  summary: pyload-ng 过度攻击面
  details: GitHub 仓库 pyload/pyload 在 0.5.0b3.dev41 之前存在过度攻击面。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev41 以解决此问题。
rule: version < "0.5.0b3.dev41"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-0435
 - https://github.com/pyload/pyload/commit/431ea6f0371d748df66b344a05ca1a8e0310cff3
 - https://github.com/pyload/pyload
 - https://huntr.dev/bounties/a3e32ad5-caee-4f43-b10a-4a876d4e3f1d