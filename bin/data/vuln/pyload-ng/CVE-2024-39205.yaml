info:
  name: pyload-ng
  cve: CVE-2024-39205
  summary: pyload-ng 存在 js2py 沙箱逃逸导致的 RCE 漏洞
  details: 任何在 python3.11 或以下版本运行的 pyload-ng 都存在远程命令执行（RCE）漏洞。攻击者可以发送包含任意 shell 命令的请求，受害服务器将立即执行它。该漏洞利用了 js2py 的沙箱逃逸漏洞（CVE-2024-28397），通过 `/flash/addcrypted2` API 端点实现 RCE。尽管此端点设计为仅接受 localhost 连接，但可以通过 HTTP 头部绕过此限制。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev86 或更高版本，此版本已修复该漏洞。同时，考虑升级到 python3.12 或以上版本，因为 pyload-ng 在这些版本中不再使用 js2py。
rule: version <= "0.5.0b3.dev85"
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-r9pp-r4xf-597r
  - https://nvd.nist.gov/vuln/detail/CVE-2024-39205
  - https://github.com/Marven11/CVE-2024-28397-js2py-Sandbox-Escape
  - https://github.com/advisories/GHSA-h95x-26f3-88hr
  - https://github.com/pyload/pyload