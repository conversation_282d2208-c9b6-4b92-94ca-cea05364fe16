info:
  name: pyload-ng
  cve: CVE-2024-21644
  summary: pyload 未认证 Flask 配置泄露漏洞
  details: 任何未认证的用户都可以浏览到特定 URL 以暴露 Flask 配置，包括 `SECRET_KEY` 变量。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev77 以解决此问题。
references:
  - https://github.com/pyload/pyload/security/advisories/GHSA-mqpq-2p68-46fv
  - https://nvd.nist.gov/vuln/detail/CVE-2024-21644
  - https://github.com/pyload/pyload/commit/bb22063a875ffeca357aaf6e2edcd09705688c40
  - https://github.com/pyload/pyload
rule: version < "0.5.0"