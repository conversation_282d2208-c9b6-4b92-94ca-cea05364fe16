info:
  name: pyload-ng
  cve: CVE-2023-0488
  summary: pyload-ng 存储型跨站脚本漏洞
  details: 此漏洞为存储型跨站脚本 (XSS) 漏洞，存在于 GitHub 仓库 pyload/pyload 的 0.5.0b3.dev42 之前版本。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 pyload-ng >= 0.5.0b3.dev42 版本以修复此漏洞。
rule: version < "0.5.0b3.dev42"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-0488
 - https://github.com/pyload/pyload/commit/46d75a3087f3237d06530d55998938e2e2bda6bd
 - https://github.com/pyload/pyload
 - https://huntr.dev/bounties/4311d8d7-682c-4f2a-b92c-3f9f1a36255a