info:
  name: pyload-ng
  cve: CVE-2024-32880
  summary: pyLoad允许上传到任意文件夹导致远程代码执行
  details: 认证用户可以更改下载文件夹并将制作的模板上传到指定文件夹，从而导致远程代码执行。在最新版本中，如果可以控制“pyload/webui/app/templates”路径下的文件，并且路径为“module/web/media/js”（旧版本0.4.20仅渲染扩展名为“.js”的文件），则render_template函数在渲染我们控制的恶意文件时将像SSTI（服务器端模板注入）一样工作。
  cvss: CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到pyload-ng的最新版本以解决此问题，确保不要将下载文件夹更改为模板路径，并限制对关键文件夹的访问权限。
rule: version < "0.5.0"
references:
 - https://github.com/pyload/pyload/security/advisories/GHSA-3f7w-p8vr-4v5f
 - https://nvd.nist.gov/vuln/detail/CVE-2024-32880
 - https://github.com/pyload/pyload