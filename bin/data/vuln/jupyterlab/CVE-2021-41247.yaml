info:
  name: jupyterlab
  cve: CVE-2021-41247
  summary: 不完整的JupyterHub注销与同时进行的JupyterLab会话
  details: |
    使用JupyterHub的JupyterLab用户在同一浏览器会话中打开多个JupyterLab标签页时，可能会看到从单用户服务器注销不完整的情况，如果在注销发生时有另一个活动的JupyterLab会话处于打开状态，则会在注销后重新建立新的凭证（仅针对单用户服务器，而非Hub）。
    
    ### 影响
    在同一浏览器会话中同时打开多个JupyterLab标签页的用户可能会遇到注销不完整的问题，因为如果在注销时有其他活动的JupyterLab会话，新的凭证会在注销后重新建立。
    
    ### 修补程序
    升级到JupyterHub 1.5。对于分布式部署，需要修补的是_user_环境中的jupyterhub，Hub环境不需要修补。
    
    ### 变通方法
    注销时确保只有一个JupyterLab标签页处于打开状态。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到JupyterHub 1.5版本以解决此问题。对于分布式部署，需要修补用户环境中的jupyterhub。没有必要在Hub环境中进行修补。作为变通方法，确保在注销时只打开一个JupyterLab标签页。
rule: version >= "1.0.0" && version < "1.5.0"
references:
 - https://github.com/jupyterhub/jupyterhub/security/advisories/GHSA-cw7p-q79f-m2v7
 - https://nvd.nist.gov/vuln/detail/CVE-2021-41247
 - https://github.com/jupyterhub/jupyterhub/commit/5ac9e7f73a6e1020ffddc40321fc53336829fe27
 - https://github.com/jupyterhub/jupyterhub
 - https://github.com/pypa/advisory-database/tree/main/vulns/jupyterhub/PYSEC-2021-386.yaml