info:
  name: jupyterlab
  cve: CVE-2021-41134
  summary: Jupyter nbdime 存储型 XSS 漏洞
  details: |
    不当处理用户控制输入导致存储型跨站脚本（XSS）漏洞。所有先前版本的 nbdime 均受到影响。
    ### 修补程序
    将为 nbdime 包的每个主要版本发布安全补丁，自 nbdime python 包 1.x 版本起。
    #### Python
    - nbdime 1.x：在 v. 1.1.1 中修补
    - nbdime 2.x：在 v. 2.1.1 中修补
    - nbdime 3.x：在 v. 3.1.1 中修补
    #### npm
    - nbdime 6.x 版本：在 6.1.2 中修补
    - nbdime 5.x 版本：在 5.0.2 中修补
    - nbdime-jupyterlab 1.x 版本：在 1.0.1 中修补
    - nbdime-jupyterlab 2.x 版本：在 2.1.1 中修补
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:N
  severity: MEDIUM
  security_advise: 请升级到以下版本以解决此问题：
    - Python 包 nbdime：
      - 1.x 版本升级到 1.1.1 或更高
      - 2.x 版本升级到 2.1.1 或更高
      - 3.x 版本升级到 3.1.1 或更高
    - npm 包 nbdime：
      - 5.x 版本升级到 5.0.2 或更高
      - 6.x 版本升级到 6.1.2 或更高
    - npm 包 nbdime-jupyterlab：
      - 1.x 版本升级到 1.0.1 或更高
      - 2.x 版本升级到 2.1.1 或更高
rule: version < "1.1.1" || (version >= "2.0.0" && version < "2.1.1") || (version >= "3.0.0" && version < "3.1.1") || version < "5.0.2" || version >= "6.0.0" && version < "6.1.2" || version < "1.0.1" || version >= "2.0.0" && version < "2.1.1"
references: 
  - https://github.com/jupyter/nbdime/security/advisories/GHSA-p6rw-44q7-3fw4
  - https://nvd.nist.gov/vuln/detail/CVE-2021-41134
  - https://github.com/jupyter/nbdime/commit/e44a5cc7677f24b45ebafc756db49058c2f750ea
  - https://github.com/jupyter/nbdime
  - https://github.com/pypa/advisory-database/tree/main/vulns/nbdime/PYSEC-2021-428.yaml