info:
  name: jupyterlab
  cve: CVE-2025-30370
  summary: jupyterlab-git 在“在终端中打开 Git 仓库”中存在命令注入漏洞
  details: |
    在许多平台上，第三方可以创建一个包含 shell 命令替换字符串名称的 Git 仓库。如果用户在此不当命名的 Git 仓库的父目录中启动 `jupyter-lab` 并点击“Git > 在终端中打开 Git 仓库”，则注入的命令将在用户的 shell 中无权限运行。此问题允许通过命令注入执行任意代码，可能导致修改文件、泄露数据、停止服务或危害服务器的安全规则。
  cvss: CVSS:3.1/AV:L/AC:H/PR:L/UI:R/S:C/C:L/I:H/A:H
  severity: HIGH
  security_advise: |
    1. 升级到 jupyterlab-git >= 0.51.1
    2. 在 `jupyter-server` 层面禁用终端：
       ```python
       c.ServerApp.terminals_enabled = False
       ```
    3. 禁用终端服务器扩展：
       ```bash
       jupyter server extension disable jupyter_server_terminals
       ```
    4. 禁用实验室扩展：
       ```bash
       jupyter labextension disable @jupyterlab/terminal-extension
       ```
rule: version >= "0" && version < "0.51.1"
references:
  - https://github.com/jupyterlab/jupyterlab-git/security/advisories/GHSA-cj5w-8mjf-r5f8
  - https://nvd.nist.gov/vuln/detail/CVE-2025-30370
  - https://github.com/jupyterlab/jupyterlab-git/pull/1196
  - https://github.com/jupyterlab/jupyterlab-git/commit/b46482993f76d3a546015c6a94ebed8b77fc2376
  - https://github.com/jupyterlab/jupyterlab-git
  - https://github.com/jupyterlab/jupyterlab-git/blob/7eb3b06f0092223bd5494688ec264527bbeb2195/src/commandsAndMenu.tsx#L175-L184