info:
  name: jupyterlab
  cve: CVE-2024-35225
  summary: Jupyter Server Proxy 在主机参数中存在反射型 XSS 问题
  details: |
    `jupyter-server-proxy` 中存在反射型跨站脚本（XSS）问题。`/proxy` 端点接受格式为 `/proxy/<host>` 的 `host` 路径段。当此端点使用无效的 `host` 值调用时，`jupyter-server-proxy` 会回复包含未经消毒的 `host` 值的响应。第三方行为者可以通过向用户发送包含自定义 JavaScript 的无效 `host` 值的网络钓鱼链接来利用此漏洞。当用户点击此网络钓鱼链接时，浏览器会呈现 `GET /proxy/<host>` 的响应，从而运行行为者设置的 `host` 中包含的自定义 JavaScript。
    由于用户点击网络钓鱼链接后可以运行任意 JavaScript，因此此问题允许行为者广泛访问用户的 JupyterLab 实例。此问题存在于 `jupyter-server-proxy` 的最新版本中，当前为 `v4.1.2`。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到 jupyter-server-proxy >=4.2.0 或 >=3.2.4
    2. 如果无法立即升级，请使用以下命令禁用 `jupyter-server-proxy` 扩展：
       ```
       jupyter server extension disable jupyter-server-proxy
       ```
rule: version >= "3.0.0" && version <= "4.1.2"
references:
  - https://github.com/jupyterhub/jupyter-server-proxy/security/advisories/GHSA-fvcq-4x64-hqxr
  - https://nvd.nist.gov/vuln/detail/CVE-2024-35225
  - https://github.com/jupyterhub/jupyter-server-proxy/commit/7abc9dc5bbb0b4b440548a5375261b8b8192fc22
  - https://github.com/jupyterhub/jupyter-server-proxy/commit/ff78128087e73fb9d0909e1366f8bf051e8ea878
  - https://github.com/jupyterhub/jupyter-server-proxy
  - https://github.com/jupyterhub/jupyter-server-proxy/blob/62a290f08750f7ae55a0c29ca339c9a39a7b2a7b/jupyter_server_proxy/handlers.py#L328
  - https://github.com/pypa/advisory-database/tree/main/vulns/jupyter-server-proxy/PYSEC-2024-236.yaml