info:
  name: jupyterlab
  cve: CVE-2024-43805
  summary: Jupyter Notebook 和 JupyterLab 中的 HTML 注入导致 DOM 操作劫持
  details: |
    ### 影响
    此漏洞依赖于用户通过打开包含 Markdown 单元的恶意笔记本，或使用 JupyterLab 预览功能打开 Markdown 文件来进行交互。
    恶意用户可以访问被攻击用户有权访问的任何数据，并可以执行任意请求，冒充被攻击用户。
    ### 修补程序
    JupyterLab v3.6.8、v4.2.5 和 Jupyter Notebook v7.2.2 已修补。
    ### 变通方法
    对于底层的 DOM 操作劫持易受攻击性没有解决方法。然而，在无法及时更新的部署中可以禁用选定的插件以最小化风险。这些插件包括：
    - `@jupyterlab/mathjax-extension:plugin` - 用户将失去预览数学方程的能力
    - `@jupyterlab/markdownviewer-extension:plugin` - 用户将失去打开 Markdown 预览的能力
    - `@jupyterlab/mathjax2-extension:plugin`（如果使用可选的 `jupyterlab-mathjax2` 包安装）- JupyterLab 4.x 的旧版本 mathjax 插件
    要禁用这些扩展，请运行：
    ```bash
    jupyter labextension disable @jupyterlab/markdownviewer-extension:plugin
    jupyter labextension disable @jupyterlab/mathjax-extension:plugin
    jupyter labextension disable @jupyterlab/mathjax2-extension:plugin
    ```
    要确认插件已禁用，请运行：
    ```bash
    jupyter labextension list
    ```
    ### 参考
    无
    ### 注释
    此更改可能会破坏某些 Markdown 的渲染。Sanitizer 中有一个设置允许恢复到以前的 sanitizer 设置（`allowNamedProperties`）。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: 升级到 JupyterLab >= 3.6.8 或 >= 4.2.5 版本以解决此问题。如果无法及时更新，可以通过禁用特定的插件来最小化风险。
rule: (version <= "3.6.7" && version >= "0") || (version <= "4.2.4" && version >= "4.0.0")
references:
 - https://github.com/jupyterlab/jupyterlab/security/advisories/GHSA-9q39-rmj3-p4r2
 - https://nvd.nist.gov/vuln/detail/CVE-2024-43805
 - https://github.com/jupyterlab/jupyterlab/commit/06ad9de836f155add7d3d651ef936cc4c5ea8093
 - https://github.com/jupyterlab/jupyterlab/commit/88e24baac551196f9cb3de16bd060a7ab1597674
 - https://github.com/jupyterlab/jupyterlab