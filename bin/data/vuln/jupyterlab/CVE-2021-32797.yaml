info:
  name: jupyterlab
  cve: CVE-2021-32797
  summary: Ju<PERSON>terLab XSS 由于缺乏对 html <form> 的 action 属性的消毒
  details: |
    不受信任的笔记本可以在加载时执行代码。这是一种远程代码执行，但需要用户操作打开笔记本。
    已在以下版本中修复：3.1.4、3.0.17、2.3.2、2.2.10、1.2.21。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 jupyterlab >= 3.1.4 或以下版本以解决此问题：3.0.17、2.3.2、2.2.10、1.2.21。
rule: version < "1.2.21" || (version >= "2.0.0a0" && version < "2.2.10") || (version >= "2.3.0a0" && version < "2.3.2") || (version >= "3.0.0a0" && version < "3.0.17") || (version >= "3.1.0a0" && version < "3.1.4")
references:
 - https://github.com/google/security-research/security/advisories/GHSA-c469-p3jp-2vhx
 - https://github.com/jupyterlab/jupyterlab/security/advisories/GHSA-4952-p58q-6crx
 - https://nvd.nist.gov/vuln/detail/CVE-2021-32797
 - https://github.com/jupyterlab/jupyterlab/commit/504825938c0abfa2fb8ff8d529308830a5ae42ed
 - https://github.com/pypa/advisory-database/tree/main/vulns/jupyterlab/PYSEC-2021-130.yaml