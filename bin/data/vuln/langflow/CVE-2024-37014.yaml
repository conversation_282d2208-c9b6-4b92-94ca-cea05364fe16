info:
  name: langflow
  cve: CVE-2024-37014
  summary: Langflow 远程代码执行漏洞
  details: |
    如果不受信任的用户能够到达“POST /api/v1/custom_component”端点并提供 Python 脚本，则 Langflow 允许远程代码执行。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: 高
  security_advise: |
    1. 升级到 langflow>=1.0.15
    2. 确保只有受信任的用户可以访问“POST /api/v1/custom_component”端点
    3. 实施输入验证以防止执行任意 Python 脚本
rule: version < "1.0.15"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-37014
  - https://github.com/langflow-ai/langflow/issues/1973
  - https://github.com/langflow-ai/langflow
  - https://github.com/pypa/advisory-database/tree/main/vulns/langflow/PYSEC-2024-177.yaml