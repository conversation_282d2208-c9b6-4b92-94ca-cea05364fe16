info:
  name: langflow
  cve: CVE-2025-3248
  summary: Langflow 在 `/api/v1/validate/code` 端点易受代码注入攻击
  details: |
    Langflow 1.3.0 之前的版本在 `/api/v1/validate/code` 端点易受代码注入攻击。
    远程且未经身份验证的攻击者可以发送特制的 HTTP 请求来执行任意代码。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到 langflow>=1.3.0
    2. 审查并清理 `/api/v1/validate/code` 端点的所有输入
    3. 实施额外的安全措施，如输入验证和输出编码
rule: version < "1.3.0"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-3248
  - https://github.com/langflow-ai/langflow/pull/6911
  - https://github.com/langflow-ai/langflow
  - https://github.com/langflow-ai/langflow/releases/tag/1.3.0
  - https://www.horizon3.ai/attack-research/disclosures/unsafe-at-any-speed-abusing-python-exec-for-unauth-rce-in-langflow-ai