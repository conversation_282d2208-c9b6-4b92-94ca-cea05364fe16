info:
  name: open-webui
  cve: CVE-2024-7033
  summary: open-webui中的任意文件写入漏洞
  details: |
    在open-webui/open-webui的0.3.8版本中，download_model端点存在任意文件写入漏洞。
    当部署在Windows上时，应用程序不正确地处理文件路径，允许攻击者操纵文件路径
    将文件写入服务器文件系统的任意位置。这可能导致覆盖关键系统或
    应用程序文件，导致服务拒绝，或可能实现远程代码执行（RCE）。RCE可以允许攻击者
    以运行应用程序的用户的权限执行恶意代码，导致整个系统
    受到妥协。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到open-webui >= 0.3.9
    2. 在download_model端点实施严格的文件路径验证
    3. 定期审计和监控文件系统更改以发现未经授权的访问
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7033
  - https://huntr.com/bounties/7078261f-8414-4bb7-9d72-a2a4d8bfd5d1