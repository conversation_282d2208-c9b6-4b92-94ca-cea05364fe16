info:
  name: open-webui
  cve: CVE-2024-7038
  summary: open-webui 允许通过观察错误消息枚举文件名和遍历目录
  details: open-webui 版本 0.3.8 存在信息泄露漏洞。该漏洞与管理员设置下的嵌入模型更新功能有关。当用户更新模型路径时，系统会检查文件是否存在，并根据文件的存在和配置提供不同的错误消息。这种行为允许攻击者通过观察错误消息来枚举文件名和遍历目录，可能导致敏感信息的泄露。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:L/I:N/A:N
  severity: LOW
  security_advise: 升级到 open-webui >= 0.3.9 版本以解决此问题。
rule: version < "0.3.9"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-7038
 - https://github.com/open-webui/open-webui
 - https://github.com/open-webui/open-webui/blob/eff736acd2e0bbbdd0eeca4cc209b216a1f23b6a/backend/apps/rag/main.py#L199
 - https://huntr.com/bounties/f42cf72a-8015-44a6-81a9-c6332ef05afc