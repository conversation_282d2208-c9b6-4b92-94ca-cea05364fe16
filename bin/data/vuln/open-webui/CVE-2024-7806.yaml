info:
  name: open-webui
  cve: CVE-2024-7806
  summary: open-webui版本<=0.3.8中存在通过CSRF实现的远程代码执行漏洞
  details: |
    open-webui/open-webui版本<=0.3.8中存在一个漏洞，允许非管理员用户通过跨站请求伪造（CSRF）实现远程代码执行。该应用程序使用SameSite属性设置为lax的cookie进行身份验证，并且缺乏CSRF令牌。这使得攻击者可以制作恶意HTML，当受害者访问时，可以修改现有管道的Python代码并以受害者的权限执行任意代码。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. 升级到open-webui>=0.3.9
    2. 为所有状态更改操作实施CSRF令牌
    3. 确保用于身份验证的cookie的SameSite属性设置为Strict或Lax
rule: version<= "0.3.8"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7806
  - https://huntr.com/bounties/9350a68d-5f33-4b3d-988b-81e778160ab8