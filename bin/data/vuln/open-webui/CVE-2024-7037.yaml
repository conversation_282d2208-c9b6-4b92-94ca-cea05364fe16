info:
  name: open-webui
  cve: CVE-2024-7037
  summary: open-webui 允许写入和删除任意文件
  details: 在 open-webui/open-webui 的 v0.3.8 版本中，/api/pipelines/upload 端点由于未对 file.filename 进行消毒处理并与 CACHE_DIR 进行拼接，导致存在任意文件写入和删除漏洞。此漏洞允许攻击者覆盖和删除系统文件，可能导致远程代码执行。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:H/A:H
  severity: MEDIUM
  security_advise: 升级到 open-webui >= 0.3.9 版本以解决此问题，该版本已修复了文件上传端点的安全漏洞。
rule: version < "0.3.9"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-7037
 - https://github.com/open-webui/open-webui
 - https://github.com/open-webui/open-webui/blob/main/backend/main.py#L1513
 - https://huntr.com/bounties/8508db68-9c99-4b1c-828c-e1bfcacfb847