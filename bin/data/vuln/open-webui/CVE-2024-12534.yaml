info:
  name: open-webui
  cve: CVE-2024-12534
  summary: open-webui中缺乏字符长度验证导致DoS
  details: |
    在open-webui/open-webui的v0.3.32版本中，由于缺乏对这些输入的字符长度验证，应用程序允许用户在登录过程中在电子邮件和密码字段中提交大型有效载荷。当用户提交过大的字符串时，此漏洞可能导致拒绝服务（DoS）情况，耗尽服务器资源，如CPU、内存和磁盘空间，并使服务对合法用户不可用。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到open-webui >= v0.3.33
    2. 为电子邮件和密码字段实施字符长度验证
    3. 监控服务器资源以发现可能表明攻击的不寻常活动
rule: version < "0.3.33"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12534
  - https://huntr.com/bounties/c7c0a4e6-acd3-49b4-8684-2c2c27014b76