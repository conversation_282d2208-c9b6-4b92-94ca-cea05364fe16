info:
  name: open-webui
  cve: CVE-2024-7035
  summary: open-webui中敏感操作的GET方法不安全
  details: |
    在open-webui/open-webui的v0.3.8版本中，诸如删除和重置之类的敏感操作是使用GET方法执行的。此漏洞允许攻击者执行跨站请求伪造（CSRF）攻击，不知情的用户只需访问恶意网站或通过顶级导航即可无意中执行敏感操作。受影响的端点包括/rag/api/v1/reset、/rag/api/v1/reset/db、/api/v1/memories/reset和/rag/api/v1/reset/uploads。这影响了应用程序的可用性和完整性。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:R/S:C/C:N/I:H/A:L
  severity: MEDIUM
  security_advise: |
    1. 升级到open-webui >= v0.3.9
    2. 为所有敏感操作实施CSRF保护
    3. 确保敏感操作仅使用POST或其他安全的HTTP方法执行
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7035
  - https://huntr.com/bounties/2ac81740-410b-467a-9244-75d82a6f9e11