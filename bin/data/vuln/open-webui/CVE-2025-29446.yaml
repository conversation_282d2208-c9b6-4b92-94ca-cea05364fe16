info:
  name: open-webui
  cve: CVE-2025-29446
  summary: open-webui v0.5.16中的SSRF漏洞
  details: |
    open-webui v0.5.16在`routers/ollama.py`中的`verify_connection`函数存在服务器端请求伪造（SSRF）漏洞。
    这使得攻击者可通过操纵此函数的输入，向内部或外部服务发起未经授权的请求。
  cvss: CVSS:3.x/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H  # 占位符，需要实际分数
  severity: 高
  security_advise: |
    1. 升级到open-webui >=0.5.17（假设此版本解决了该问题）
    2. 为`verify_connection`函数的所有输入实施严格的输入验证
    3. 考虑实施网络级限制，以限制`verify_connection`函数可访问的目的地
rule: version == "0.5.16"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-29446
  - https://github.com/jcxj/jcxj/blob/master/source/_posts/open-webui-ssrf%E6%BC%8F%E6%B4%9E.md
  - https://github.com/l1uyi/cve-list/blob/main/cve-list/open-webui-ssrf.md