info:
  name: open-webui
  cve: CVE-2024-7039
  summary: open-webui 中的权限管理不当漏洞
  details: |
    在 open-webui/open-webui 版本 v0.3.8 中，存在权限管理不当的漏洞。
    该应用程序允许攻击者以管理员身份通过 API 端点 `http://0.0.0.0:8080/api/v1/users/{uuid_administrator}` 删除其他管理员。此操作在用户界面中受到限制，但可以通过直接的 API 调用来执行。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:L
  severity: HIGH
  security_advise: |
    1. 升级到 open-webui >= v0.3.9
    2. 实施严格的 API 访问控制，以防止未经授权删除管理员
    3. 审查并增强用户角色管理，以确保适当的权限分离
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7039
  - https://huntr.com/bounties/27fc8a5a-546e-4cf2-8edb-df42e36518fc