info:
  name: open-webui
  cve: CVE-2024-7990
  summary: open-webui中存在的存储型跨站脚本（XSS）漏洞
  details: |
    open-webui/open-webui版本0.3.8中存在存储型跨站脚本（XSS）漏洞。
    该漏洞存在于`/api/v1/models/add`端点，其中模型描述字段在在聊天中渲染之前未进行适当的清理。这允许攻击者注入恶意脚本，
    任何用户（包括管理员）都可以执行这些脚本，可能导致任意代码执行。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:R/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: |
    1. 升级到open-webui >= 0.3.9
    2. 为所有用户生成的内容实施适当的输入清理
    3. 定期审查和更新安全措施以防止类似漏洞
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7990
  - https://huntr.com/bounties/2256e336-0f67-449e-a82d-7fc57081a21c