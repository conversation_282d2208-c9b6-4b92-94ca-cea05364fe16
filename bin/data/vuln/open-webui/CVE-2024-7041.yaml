info:
  name: open-webui
  cve: CVE-2024-7041
  summary: open-webui 不安全的直接对象引用（IDOR）漏洞
  details: 在 open-webui/open-webui 版本 v0.3.8 中存在不安全的直接对象引用（IDOR）漏洞。该漏洞出现在 API 端点 `http://0.0.0.0:3000/api/v1/memories/{id}/update`，其中去中心化设计存在缺陷，允许攻击者在未经适当授权的情况下编辑其他用户的记忆。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:N
  severity: MEDIUM
  security_advise: 升级到 open-webui 版本高于 v0.3.8 以解决此问题。
rule: version < "0.3.8"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-7041
 - https://github.com/open-webui/open-webui
 - https://github.com/open-webui/open-webui/blob/main/backend/apps/webui/routers/memories.py#L71
 - https://huntr.com/bounties/6855227f-1237-47b8-8d37-29aad7ddec3a