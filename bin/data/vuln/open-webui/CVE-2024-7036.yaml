info:
  name: open-webui
  cve: CVE-2024-7036
  summary: open-webui中的漏洞允许未经身份验证的攻击者导致管理面板无响应。
  details: |
    open-webui/open-webui v0.3.8中的一个漏洞允许未经身份验证的攻击者在“姓名”字段中使用过大的文本进行注册，导致管理面板变得无响应。
    这会阻止管理员执行基本的用户管理操作，例如删除、编辑或添加用户。
    低权限的已身份验证用户也可以利用此漏洞，导致管理面板处于相同的无响应状态。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到open-webui >= v0.3.9
    2. 实施输入验证以限制“姓名”字段中的文本大小
    3. 监控管理面板上的异常活动
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7036
  - https://huntr.com/bounties/ba62d093-ab27-48fa-9c53-0602c8cdc48a