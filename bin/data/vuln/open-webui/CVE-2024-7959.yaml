info:
  name: open-webui
  cve: CVE-2024-7959
  summary: open-webui/open-webui版本0.3.8的`/openai/models`端点存在SSRF漏洞
  details: |
    open-webui/open-webui版本0.3.8中的`/openai/models`端点易受服务器端请求伪造（SSRF）攻击。
    攻击者可以将OpenAI URL更改为任意URL而不进行检查，导致端点向指定的URL发送请求并返回输出。
    此漏洞允许攻击者访问内部服务，并可能通过访问实例密钥获得命令执行权限。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    1. 升级到open-webui/open-webui >=0.3.9
    2. 为`/openai/models`端点实施严格的URL验证
    3. 监控并限制应用程序的出站请求
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7959
  - https://huntr.com/bounties/3c8bea0a-d678-4d67-bb9c-2b5b610a2193