info:
  name: open-webui
  cve: CVE-2024-53981
  summary: Open WebUI 未认证多部分边界拒绝服务（DoS）漏洞存在于api/chat/file中
  details: |
    open-webui/open-webui版本79778fa中的一个漏洞允许攻击者通过上传具有畸形多部分边界的文件来造成拒绝服务（DoS）。通过在多部分边界的末尾追加大量字符，服务器会持续处理每个字符，从而使应用程序无法访问。此问题可能会阻止所有用户访问应用程序，直到服务器恢复。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: 高
  security_advise: |
    1. 升级到open-webui >= 0.4.7
    2. 对多部分边界实施输入验证以防止过长输入
    3. 监控服务器日志以发现可能表明DoS尝试的不寻常活动
rule: version < "0.4.7"
references:
  - https://github.com/Kludex/python-multipart/security/advisories/GHSA-59g5-xgcq-4qw3
  - https://nvd.nist.gov/vuln/detail/CVE-2024-53981
  - https://github.com/open-webui/open-webui/commit/f311c03a21dc09e279a0cad7482a68b273517baf
  - https://github.com/open-webui/open-webui
  - https://huntr.com/bounties/15eb4fbe-70d4-420e-806a-ec6f4ecb7202