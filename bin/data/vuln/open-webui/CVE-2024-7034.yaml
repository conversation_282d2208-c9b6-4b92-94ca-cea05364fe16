info:
  name: open-webui
  cve: CVE-2024-7034
  summary: open-webui中的任意文件写入漏洞
  details: |
    在open-webui版本0.3.8中，端点`/models/upload`由于对用户提供的文件名处理不当，存在任意文件写入漏洞。该漏洞源于使用`file_path = f\"{UPLOAD_DIR}/{file.filename}\"`时缺乏适当的输入验证或消毒。攻击者可以通过操纵`file.filename`参数包含目录遍历序列，导致生成的`file_path`逃逸出预期的`UPLOAD_DIR`，并可能覆盖系统上的任意文件。这可能导致未经授权的系统二进制文件、配置文件或敏感数据的修改，潜在地启用远程命令执行。
  cvss: CVSS:3.0/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. 升级到open-webui >= 0.3.9
    2. 对用户提供的文件名实施适当的输入验证和消毒
    3. 审查并更新文件处理逻辑以防止目录遍历攻击
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7034
  - https://huntr.com/bounties/711beada-10fe-4567-9278-80a689da8613