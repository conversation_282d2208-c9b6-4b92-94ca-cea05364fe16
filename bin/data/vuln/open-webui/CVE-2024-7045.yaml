info:
  name: open-webui
  cve: CVE-2024-7045
  summary: open-webui中存在不当访问控制漏洞，允许未经授权的提示查看。
  details: |
    在open-webui/open-webui的v0.3.8版本中，不当访问控制漏洞允许攻击者查看任何提示。
    应用程序不验证攻击者是否为管理员，允许攻击者直接调用/api/v1/prompts/接口
    来检索管理员创建的所有提示信息，其中包括ID值。随后，攻击者可以利用
    /api/v1/prompts/command/{command_id}接口获取任意提示信息。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到open-webui >= v0.3.9
    2. 为/api/v1/prompts/接口实施严格的访问控制检查
    3. 在允许访问敏感数据之前验证用户角色和权限
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7045
  - https://huntr.com/bounties/03ea0826-af7b-4717-b63e-90fd19675ab2