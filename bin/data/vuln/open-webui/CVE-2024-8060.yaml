info:
  name: open-webui
  cve: CVE-2024-8060
  summary: Open WebUI 允许通过任意文件上传实现远程代码执行
  details: |
    OpenWebUI 版本 0.3.0 在音频 API 端点 `/audio/api/v1/transcriptions` 中存在漏洞，允许任意文件上传。该应用程序对 `file.content_type` 的验证不足，并允许用户控制文件名，导致路径遍历漏洞。这可以被认证用户利用来覆盖 Docker 容器内的关键文件，可能导致以根用户身份远程执行代码。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:N/I:H/A:H
  severity: HIGH
  security_advise: |
    1. 升级到 open-webui>=0.5.17
    2. 实施严格的文件上传验证
    3. 审查并强制执行 Docker 容器内的适当文件权限
rule: version >= "0" && version < "0.5.17"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8060
  - https://github.com/open-webui/open-webui/commit/613a087387c094e71ee91d29c015195ef401e160
  - https://github.com/open-webui/open-webui
  - https://huntr.com/bounties/a3b1a4b7-c723-496d-842c-844cc0988fe9