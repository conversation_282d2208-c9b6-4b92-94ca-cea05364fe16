info:
  name: open-webui
  cve: CVE-2024-7999
  summary: open-webui中由于多部分边界格式错误导致的拒绝服务（DoS）漏洞
  details: |
    open-webui/open-webui版本79778fa中的一个漏洞允许攻击者通过上传具有格式错误的多部分边界的文件来造成拒绝服务（DoS）。通过在多部分边界的末尾追加大量字符，服务器会持续处理每个字符，使应用程序无法访问。此问题可能会阻止所有用户访问应用程序，直到服务器恢复。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到open-webui的最新版本以减轻此漏洞。
    2. 实施服务器端验证以检测并拒绝具有过长多部分边界的文件。
    3. 监控服务器日志以发现可能表明攻击尝试的不寻常活动。
rule: version < "79778fa"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7999
  - https://huntr.com/bounties/15eb4fbe-70d4-420e-806a-ec6f4ecb7202