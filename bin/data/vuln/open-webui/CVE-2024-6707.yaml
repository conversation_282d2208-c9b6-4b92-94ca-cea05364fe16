info:
  name: open-webui
  cve: CVE-2024-6707
  summary: Open WebUI 路径遍历漏洞
  details: |
    攻击者可通过滥用路径遍历漏洞，将受控文件上传到Web服务器文件系统的任意位置。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: |
    - 升级Open WebUI至最新版本以修复此漏洞。
    - 检查并更新服务器配置，确保不允许执行不受信任的文件上传。
    - 实施严格的文件上传验证和路径遍历防护措施。
rule: ""
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-6707
  - https://korelogic.com/Resources/Advisories/KL-001-2024-006.txt