info:
  name: open-webui
  cve: CVE-2024-12537
  summary: open-webui中缺乏身份验证机制，允许未经身份验证的访问，从而导致拒绝服务。
  details: |
    在open-webui/open-webui的0.3.32版本中，缺乏身份验证机制允许任何未经身份验证的攻击者访问`api/v1/utils/code/format`端点。如果恶意行为者发送带有过高内容量的POST请求，服务器可能会变得完全无响应。这可能导致严重的性能问题，导致服务器变得无响应或经历显著降级，最终导致合法用户的服务中断。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 为所有端点实施身份验证机制。
    2. 对POST请求进行速率限制，以防止内容量过大。
    3. 监控服务器性能以发现异常活动。
rule: version < "0.3.33"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12537
  - https://huntr.com/bounties/edabd06c-acc0-428c-a481-271f333755bc