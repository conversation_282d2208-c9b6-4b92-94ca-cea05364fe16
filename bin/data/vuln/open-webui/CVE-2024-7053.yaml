info:
  name: open-webui
  cve: CVE-2024-7053
  summary: open-webui/open-webui 版本 0.3.8 中的会话固定漏洞
  details: |
    open-webui/open-webui 版本 0.3.8 中的一个漏洞允许拥有用户级别账户的攻击者执行会话固定攻击。所有用户的会话 Cookie 都以默认的 `SameSite=Lax` 设置，并且没有启用 `Secure` 标志，允许会话 Cookie 通过 HTTP 发送到跨域域。攻击者可以通过在聊天中嵌入恶意的 Markdown 图像来利用这一点，当管理员查看时，会将管理员的会话 Cookie 发送到攻击者的服务器。这可能导致隐蔽的管理员账户接管，由于管理员账户的权限提升，可能会导致远程代码执行 (RCE)。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:L/A:N
  severity: HIGH
  security_advise: |
    1. 升级到 open-webui/open-webui >= 0.3.9
    2. 确保会话 Cookie 设置了 `Secure` 标志
    3. 实施严格的 SameSite Cookie 属性以防止跨站请求伪造
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7053
  - https://huntr.com/bounties/947f8191-0abf-4adf-b7c4-d4c19683aba2