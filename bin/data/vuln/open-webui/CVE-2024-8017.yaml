info:
  name: open-webui
  cve: CVE-2024-8017
  summary: open-webui版本<=0.3.8中的XSS漏洞
  details: |
    open-webui/open-webui版本<=0.3.8中存在一个XSS漏洞，具体存在于构建工具提示HTML的函数中。该漏洞允许攻击者以受害者的权限执行操作，例如窃取聊天记录、删除聊天记录，如果受害者是管理员，则将他们自己的帐户提升为管理员。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到open-webui>=0.3.9
    2. 审查并清理所有用户生成的内容以防止XSS攻击
    3. 实施严格的内容安全策略（CSP）以减轻XSS的风险
rule: version<= "0.3.8"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8017
  - https://huntr.com/bounties/ef06c7c8-1cb2-42a7-a6e6-17b2e1c744f7