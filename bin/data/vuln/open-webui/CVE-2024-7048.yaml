info:
  name: open-webui
  cve: CVE-2024-7048
  summary: Open WebUI 权限管理不当漏洞
  details: 在 open-webui 版本 v0.3.8 中，API 端点 GET /api/v1/documents/ 和 POST /rag/api/v1/doc 存在权限管理不当的漏洞。该漏洞允许低权限用户访问和覆盖高权限管理员管理的文件。通过利用此漏洞，攻击者可以查看管理员上传文件的元数据并覆盖这些文件，从而危害 RAG 模型的完整性和可用性。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:L/A:L
  severity: MEDIUM
  security_advise: 升级到 open-webui 版本 v0.3.9 或更高版本以解决此问题。
rule: version < "0.3.9"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-7048
 - https://huntr.com/bounties/acd0b2dd-61eb-4712-82d3-a4e35d6ee560