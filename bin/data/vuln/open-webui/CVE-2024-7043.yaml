info:
  name: open-webui
  cve: CVE-2024-7043
  summary: open-webui中存在不当访问控制漏洞，允许未经授权的文件操作
  details: |
    open-webui/open-webui v0.3.8中存在不当访问控制漏洞，允许攻击者查看和删除任何文件。
    该应用程序不验证攻击者是否为管理员，允许攻击者直接调用GET /api/v1/files/接口检索用户上传的所有文件的信息，其中包括ID值。
    然后攻击者可以使用GET /api/v1/files/{file_id}接口获取任何文件的信息，并使用DELETE /api/v1/files/{file_id}接口删除任何文件。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N
  severity: HIGH
  security_advise: |
    1. 升级到open-webui >= v0.3.9
    2. 实施适当的访问控制检查，确保只有管理员可以执行文件操作
    3. 定期审计和监控文件访问日志以发现任何可疑活动
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7043
  - https://huntr.com/bounties/c01e0c7f-68d8-45cf-91d2-521c97f33b00