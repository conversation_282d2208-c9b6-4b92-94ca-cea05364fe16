info:
  name: open-webui
  cve: CVE-2024-8053
  summary: open-webui中PDF生成端点缺乏身份验证
  details: |
    在open-webui/open-webui的v0.3.10版本中，`api/v1/utils/pdf`端点缺乏身份验证机制。
    攻击者可以通过发送带有超大负载的POST请求来利用这一点，可能导致服务器资源耗尽和服务拒绝（DoS）。
    此外，未经授权的用户可以滥用该端点在不进行验证的情况下生成PDF，导致服务滥用以及潜在的操作和财务影响。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到open-webui >= v0.3.11
    2. 为`api/v1/utils/pdf`端点实施身份验证机制
    3. 验证并限制PDF生成请求的有效负载大小
rule: version < "0.3.11"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8053
  - https://huntr.com/bounties/ebe8c1fa-113b-4df9-be03-a406b9adb9f4