info:
  name: open-webui
  cve: CVE-2024-7044
  summary: open-webui 聊天文件上传中的存储型跨站脚本（XSS）漏洞
  details: |
    open-webui/open-webui 版本 0.3.8 的聊天文件上传功能中存在存储型跨站脚本（XSS）漏洞。
    攻击者可以将恶意内容注入文件中，当受害者通过 URL 或共享聊天访问该文件时，会在受害者的浏览器中执行 JavaScript。
    这可能会导致用户数据盗窃、会话劫持、恶意软件分发和网络钓鱼攻击。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到 open-webui >= 0.3.9
    2. 对所有上传的文件实施服务器端验证以防止恶意内容
    3. 定期更新和修补 open-webui 应用以减轻未来的漏洞
rule: version < "0.3.9"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-7044
  - https://huntr.com/bounties/c25a885c-d6e2-4169-9ee8-4d33bcbb5ef6