info:
  name: gradio
  cve: CVE-2024-2206
  summary: gradio 服务器端请求伪造漏洞
  details: gradio-app/gradio 中存在一个 SSRF 漏洞，这是由于在 `/proxy` 路由中对用户提供的 URL 验证不足所致。攻击者可以通过在请求 `/` 和 `/config` 路由时操纵 `X-Direct-Url` 头中的 `self.replica_urls` 集合，添加任意 URL 进行代理。此缺陷允许未经授权的请求代理和潜在访问 Hugging Face 空间内的内部端点。该问题源于应用程序在 `build_proxy_request` 函数中对安全 URL 的检查不足。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: HIGH
  security_advise: 升级到 gradio >= 4.18.0 版本以解决此问题。
rule: version >= "0" && version < "4.18.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-2206
 - https://github.com/gradio-app/gradio/commit/49d9c48537aa706bf72628e3640389470138bdc6
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/2286c1ed-b889-45d6-adda-7014ea06d98e