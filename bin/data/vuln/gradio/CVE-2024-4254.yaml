info:
  name: gradio
  cve: CVE-2024-4254
  summary: gradio工作流中的秘密泄露漏洞
  details: |
    gradio-app/gradio仓库中的'deploy-website.yml'工作流，在'main'分支中，由于授权不当，存在秘密泄露漏洞。该漏洞源于工作流明确检出并执行来自分叉的代码，这是不安全的，因为它允许在具有推送至基础仓库和访问秘密权限的环境中运行不受信任的代码。此缺陷可能导致敏感秘密的泄露，例如GITHUB_TOKEN、HF_TOKEN、VERCEL_ORG_ID、VERCEL_PROJECT_ID、COMMENT_TOKEN、AWSACCESSKEYID、AWSSECRETKEY和VERCEL_TOKEN。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:L/A:N
  severity: HIGH
  security_advise: |
    1. 审查并更新'deploy-website.yml'工作流，确保其不执行来自不受信任分叉的代码。
    2. 为处理敏感信息的任何工作流实施严格的授权检查。
    3. 定期审计并将依赖项更新至最新的安全版本。
rule: version > "0" && version < "4.44.0" # 占位符规则，因为未提供受影响的具体版本
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-4254
  - https://huntr.com/bounties/59873fbd-5698-4ec3-87f9-5d70c6055d01