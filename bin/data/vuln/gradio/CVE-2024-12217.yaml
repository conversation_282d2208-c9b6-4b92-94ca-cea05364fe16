info:
  name: gradio
  cve: CVE-2024-12217
  summary: Gradio在Windows操作系统中的路径遍历漏洞
  details: |
    gradio-app/gradio存储库的版本git 67e4044中存在一个漏洞，该漏洞允许在Windows操作系统上进行路径遍历。
    blocked_path功能旨在禁止用户读取某些文件，但存在缺陷。
    具体来说，虽然应用程序正确阻止了对诸如'C:/tmp/secret.txt'之类的路径的访问，但当使用NTFS备用数据流（ADS）语法时，例如'C:/tmp/secret.txt::$DATA'，它未能阻止访问。
    这个缺陷可能导致未经授权读取被阻止的文件路径。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: |
    1. 升级到解决此漏洞的最新版本的gradio。
    2. 实施额外的验证检查，以确保阻止所有形式的路径遍历尝试，包括使用NTFS备用数据流（ADS）语法的尝试。
    3. 定期审查和更新blocked_path功能，以涵盖任何新的或新兴的路径遍历技术。
rule: version < "67e4044" # 假设在git 67e4044之后的提交中修复了该漏洞
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-12217
  - https://huntr.com/bounties/0439bf3d-cb38-43a5-8314-0fadf85cc5a0