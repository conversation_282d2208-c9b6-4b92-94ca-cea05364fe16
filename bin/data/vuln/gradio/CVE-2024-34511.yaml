info:
  name: gradio
  cve: CVE-2024-34511
  summary: Gradio的组件服务器未正确考虑`_is_server_fn`函数
  details: Gradio在4.13之前的版本中，组件服务器未正确考虑`_is_server_fn`对于函数的处理。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到gradio>=4.13.0以解决此问题。
rule: version >= "0" && version < "4.13.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-34511
 - https://github.com/gradio-app/gradio/commit/24a583688046867ca8b8b02959c441818bdb34a2
 - https://github.com/gradio-app/gradio
 - https://www.gradio.app/changelog#4-13-0