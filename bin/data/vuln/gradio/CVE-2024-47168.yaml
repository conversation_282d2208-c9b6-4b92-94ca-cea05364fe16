info:
  name: gradio
  cve: CVE-2024-47168
  summary: Gradio的`enable_monitoring`标志设置为`False`时未能禁用监控
  details: 此漏洞涉及由于`enable_monitoring`标志未正确禁用监控而导致的数据暴露。即使监控被假定为禁用状态，攻击者或未经授权的用户仍然可以通过直接请求/monitoring端点访问监控仪表板。这意味着敏感的应用程序分析数据可能仍然会暴露，特别是在预期监控被禁用的环境中。设置`enable_monitoring=False`以防止未经授权访问监控数据的用户会受到影响。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:L/I:N/A:N
  severity: LOW
  security_advise: 升级到gradio>=4.44以解决此问题。
rule: version >= "0" && version < "4.44.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-hm3c-93pg-4cxw
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47168
 - https://github.com/gradio-app/gradio