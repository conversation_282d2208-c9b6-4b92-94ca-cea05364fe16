info:
  name: gradio
  cve: CVE-2024-51751
  summary: Gradio File和UploadButton组件存在任意文件读取漏洞
  details: 如果Gradio应用程序中使用File或UploadButton组件来预览文件内容，攻击者可能会滥用这些组件从应用程序服务器读取任意文件。该漏洞是由于Gradio在处理文件路径时未能正确验证文件路径，导致攻击者可以读取服务器上的任意文件。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到gradio>=5.5.0以解决此问题。在升级之前，可以通过自定义文件上传处理逻辑来增强安全性，确保所有文件路径都经过严格验证。
rule: version >= "5.0.0" && version < "5.5.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-rhm9-gp5p-5248
  - https://nvd.nist.gov/vuln/detail/CVE-2024-51751
  - https://github.com/gradio-app/gradio