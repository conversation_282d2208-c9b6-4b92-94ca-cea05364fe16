info:
  name: gradio
  cve: CVE-2024-0964
  summary: Gradio 路径遍历漏洞
  details: 由于 API 请求中用户提供的 JSON 值存在漏洞，Gradio 中可能会远程触发本地文件包含。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 gradio >= 4.9.0 版本以修复此漏洞。
rule: version > "0" && version < "4.9.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-0964
 - https://github.com/gradio-app/gradio/commit/d76bcaaaf0734aaf49a680f94ea9d4d22a602e70
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/25e25501-5918-429c-8541-88832dfd3741