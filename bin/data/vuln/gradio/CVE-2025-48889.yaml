info:
  name: gradio
  cve: CVE-2025-48889
  summary: Gradio 允许通过路径操作进行未经授权的文件复制
  details: |
    Gradio 的标记功能中存在一个任意文件复制漏洞，允许未经身份验证的攻击者从服务器的文件系统中复制任何可读文件。虽然攻击者无法读取这些复制的文件，但他们可以通过复制大文件（如 /dev/urandom）来填满磁盘空间，从而导致 DoS。
    标记组件在复制文件之前没有正确验证文件路径。攻击者可以向 `/gradio_api/run/predict` 端点发送特制的请求来触发这些文件复制。
    **来源**：标记功能 JSON 有效负载中用户控制的 `path` 参数  
    **接收点**：`FileData._copy_to_dir()` 方法中的 `shutil.copy` 操作
    易受攻击的代码流程：
    1. 向 `/gradio_api/run/predict` 端点发送 JSON 有效负载
    2. `FileData` 对象内的 `path` 字段可以引用系统上的任何文件
    3. 处理此请求时，`Component.flag()` 方法会创建一个 `GradioDataModel` 对象
    4. `FileData._copy_to_dir()` 方法在未经适当验证的情况下使用此路径：
    ```python
    def _copy_to_dir(self, dir: str) -> FileData:
        pathlib.Path(dir).mkdir(exist_ok=True)
        new_obj = dict(self)
        if not self.path:
            raise ValueError("源文件路径未设置")
        new_name = shutil.copy(self.path, dir)  # 易受攻击的接收点
        new_obj["path"] = new_name
        return self.__class__(**new_obj)
    ```
    5. 缺乏验证允许复制 Gradio 进程可以读取的任何文件
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L
  severity: MEDIUM
  security_advise: |
    1. 升级到 gradio>=5.31.0
    2. 为标记功能中的文件操作实施严格的路径验证
    3. 监控和限制可以复制的文件大小以防止 DoS 攻击
rule: version >= "0" && version < "5.31.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-8jw3-6x8j-v96g
  - https://nvd.nist.gov/vuln/detail/CVE-2025-48889
  - https://github.com/gradio-app/gradio