info:
  name: gradio
  cve: CVE-2024-1540
  summary: Gradio的CI存在命令注入漏洞
  details: gradio-app/gradio仓库的deploy+test-visual.yml工作流中存在命令注入漏洞，由于未正确中和命令中使用的特殊元素导致。该漏洞允许攻击者执行未经授权的命令，可能导致对基础仓库的未经授权修改或秘密泄露。问题源于在动作定义内不安全地处理GitHub上下文信息，在脚本执行前进行评估和替换。修复措施包括将不受信任的输入值设置为中间环境变量，以防止直接影响脚本生成。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: 升级到gradio>=4.18.0版本以解决此问题。
rule: version >= "0" && version < "4.18.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1540
 - https://github.com/gradio-app/gradio/commit/d56bb28df80d8db1f33e4acf4f6b2c4f87cb8b28
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/0e39e974-9a66-476f-91f5-3f37abb03d77