info:
  name: gradio
  cve: CVE-2024-47869
  summary: Gradio 在比较哈希时执行非恒定时间比较
  details: 此漏洞涉及Gradio在`analytics_dashboard`函数中比较哈希时的**定时攻击**。由于比较不是在恒定时间内完成的，攻击者可以通过测量不同请求的响应时间来逐字节推断正确的哈希值。这可能导致未经授权访问分析仪表板，特别是如果攻击者能够使用不同的密钥反复查询系统。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到gradio>4.44以缓解此问题。作为临时解决方案，开发者可以手动修补`analytics_dashboard`仪表板，使用**恒定时间比较**函数来比较敏感值，如哈希，或者禁用对分析仪表板的访问。
rule: version >= "0" && version < "4.44.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-j757-pf57-f8r4
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47869
 - https://github.com/gradio-app/gradio