info:
  name: gradio
  cve: CVE-2024-1729
  summary: Gradio 应用易受时间攻击以猜测密码
  details: | 
    ### 影响
    此安全策略与时间攻击有关，允许 Gradio 应用的用户可能猜测受密码保护的 Gradio 应用的密码。这依赖于 Python 中的字符串比较会在出现字符串不匹配时提前终止的事实。因为 Gradio 应用默认不受速率限制，用户可以暴力尝试数百万次猜测以找出正确的用户名和密码。
    ### 补丁
    是的，该问题已在 Gradio 版本 4.19.2 或更高版本中修复。我们不知道此漏洞被用于攻击 Gradio 应用的用户，但我们鼓励所有用户升级到 Gradio 4.19.2 或更高版本。
cvss: CVSS:3.0/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:N/A:N
severity: MEDIUM
security_advise: 升级到 gradio>=4.19.2 以解决此问题。
rule: version < "4.19.2"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-hmx6-r76c-85g9
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1729
  - https://github.com/gradio-app/gradio/commit/e329f1fd38935213fe0e73962e8cbd5d3af6e87b
  - https://github.com/gradio-app/gradio/releases/tag/gradio%404.19.2
  - https://huntr.com/bounties/f6a10a8d-f538-4cb7-9bb2-85d9f5708124