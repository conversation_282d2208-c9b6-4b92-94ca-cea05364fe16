info:
  name: gradio
  cve: CVE-2024-1728
  summary: Gradio 本地文件包含漏洞
  details: gradio-app/gradio 存在本地文件包含漏洞，这是由于在 UploadButton 组件中对用户提供的输入验证不当。攻击者可以通过操纵发送到 `/queue/join` 端点的请求中的文件路径来利用此漏洞读取文件系统上的任意文件，例如私有的 SSH 密钥。此问题可能导致远程代码执行。漏洞存在于文件上传路径的处理中，允许攻击者将文件上传重定向到服务器上的意外位置。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 gradio >= "4.19.2" 版本以解决此问题。
rule: version > "0" && version < "4.19.2"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1728
 - https://github.com/gradio-app/gradio/commit/16fbe9cd0cffa9f2a824a0165beb43446114eec7
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/9bb33b71-7995-425d-91cc-2c2a2f2a068a