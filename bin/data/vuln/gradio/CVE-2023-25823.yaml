info:
  name: gradio
  cve: CVE-2023-25823
  summary: 更新分享链接以使用FRP代替SSH隧道
  details: |
    ### 影响
    此漏洞影响任何使用旧版本Gradio（低于3.13.1）的分享链接（即创建Gradio应用并设置`share=True`）的用户。在这些旧版本的Gradio中，私钥会被发送给任何连接到Gradio机器的用户，这意味着用户可以访问其他用户共享的Gradio演示。从那里开始，根据Gradio应用提供的访问/暴露级别，其他利用方式是可能的。
    ### 修补
    该问题已经得到修补。理想情况下，用户应升级到`gradio==3.19.1`或更高版本，其中FRP解决方案已得到适当测试。
    ### 致谢
    感谢Greg <PERSON>和Samuel Tremblay-Cossette提醒团队。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:N/I:L/A:L
  severity: MEDIUM
  security_advise: 升级到gradio==3.19.1或更高版本以解决此问题。
rule: version > "0" && version < "3.13.1"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-3x5j-9vwr-8rr5
 - https://nvd.nist.gov/vuln/detail/CVE-2023-25823
 - https://github.com/gradio-app/gradio
 - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2023-16.yaml