info:
  name: gradio
  cve: CVE-2024-10624
  summary: Gradio易受通过特制HTTP请求的拒绝服务（DoS）攻击
  details: |
    gradio-app/gradio存储库中存在正则表达式拒绝服务（ReDoS）漏洞，影响gr.Datetime组件。受影响的版本是git commit 98cbcae。该漏洞源于使用正则表达式处理用户输入，这可能需要多项式时间来匹配某些特制输入，可能导致服务器出现拒绝服务（DoS）状况。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到gradio >= 5.0.0-beta.2
    2. 应用gradio维护者发布的最新安全补丁
    3. 监控服务器性能，留意可能表明DoS攻击的异常活动
rule: version >= "4.38.0" && version < "5.0.0-beta.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10624
  - https://github.com/gradio-app/gradio
  - https://github.com/gradio-app/gradio/blob/98cbcaef827de7267462ccba180c7b2ffb1e825d/gradio/components/datetime.py#L133-L136
  - https://huntr.com/bounties/e8d0b248-8feb-4c23-9ef9-be4d1e868374