info:
  name: gradio
  cve: CVE-2024-1727
  summary: Gradio中的跨站请求伪造
  details: |
    Gradio中存在一个跨站请求伪造漏洞，允许攻击者诱骗用户将许多大文件上传到其本地Gradio实例。这个问题是由于CORS配置不足引起的。已提交拉取请求以加强CORS规则，确保在适当头存在时只接受来自localhost或其别名的请求。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:N/A:L
  severity: 中等
  security_advise: |
    1. 升级到gradio >= 4.19.2以应用修复。
    2. 在您的Gradio应用程序中审查并执行严格的CORS策略。
    3. 升级后监控任何可疑的文件上传活动。
rule: version >= "0" && version < "4.19.2"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1727
  - https://github.com/gradio-app/gradio/pull/7503
  - https://github.com/gradio-app/gradio/commit/84802ee6a4806c25287344dce581f9548a99834a
  - https://github.com/gradio-app/gradio
  - https://huntr.com/bounties/a94d55fb-0770-4cbe-9b20-97a978a2ffff