info:
  name: gradio
  cve: CVE-2024-48052
  summary: gradio 服务器端请求伪造漏洞
  details: 在 gradio <=4.42.0 中，gr.DownloadButton 函数存在一个隐藏的服务器端请求伪造（SSRF）漏洞。原因是在 save_url_to_cache 函数中，对 URL 没有限制，这允许访问本地目标资源。这可能导致下载本地资源和敏感信息。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 gradio >=4.42.1 以解决此问题，该版本已修复了 SSRF 漏洞。
rule: version <= "4.42.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-48052
 - https://gist.github.com/AfterSnows/45ffc23797f9127e00755376cc610e12
 - https://github.com/gradio-app/gradio
 - https://rumbling-slice-eb0.notion.site/FULL-SSRF-in-gr-DownloadButton-in-gradio-app-gradio-870b21e0908b48cbafd914719ac1a4e6?pvs=4