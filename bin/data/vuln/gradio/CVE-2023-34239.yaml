info:
  name: gradio
  cve: CVE-2023-34239
  summary: Gradio 存在任意文件读取和代理任意 URL 的漏洞
  details: |
    ### 影响
    这里有两个单独的安全漏洞：(1) 允许用户读取运行共享 Gradio 应用的机器上的任意文件的安全漏洞 (2) 用户能够使用共享 Gradio 应用的机器代理任意 URL
    ### 修复
    这两个问题都已解决，请将 `gradio` 升级到 `3.34.0` 或更高版本
    ### 变通方法
    除了关闭任何共享的 Gradio 应用之外，无法采取变通方法
    ### 参考
    相关 PR：
    * https://github.com/gradio-app/gradio/pull/4406
    * https://github.com/gradio-app/gradio/pull/4370
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L
  severity: MEDIUM
  security_advise: 升级到 gradio>=3.34.0 以解决此问题。
rule: version < "3.34.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-3qqg-pgqq-3695
 - https://nvd.nist.gov/vuln/detail/CVE-2023-34239
 - https://github.com/gradio-app/gradio/pull/4370
 - https://github.com/gradio-app/gradio/pull/4406
 - https://github.com/gradio-app/gradio