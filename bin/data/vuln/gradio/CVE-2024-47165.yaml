info:
  name: gradio
  cve: CVE-2024-47165
  summary: Gradio接受空origin的CORS验证漏洞
  details: 此漏洞与CORS来源验证接受空origin有关。当Gradio服务器在本地部署时，`localhost_aliases`变量将\"null\"视为有效来源。这允许攻击者从沙盒iframe或其他具有空origin的来源发起未经授权的请求，可能导致数据盗窃，例如用户身份验证令牌或上传的文件。这会影响本地运行Gradio的用户，尤其是那些使用基本身份验证的用户。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到gradio>=5.0以解决此问题，或者作为临时解决方案，用户可以手动修改本地Gradio部署中的`localhost_aliases`列表，排除\"null\"作为有效来源。
rule: version >= "0" && version < "5.0.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-89v2-pqfv-c5r9
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47165
 - https://github.com/gradio-app/gradio