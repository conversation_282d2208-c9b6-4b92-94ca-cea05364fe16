info:
  name: gradio
  cve: CVE-2024-8021
  summary: Gradio中的开放重定向漏洞允许恶意重定向
  details: |
    gradio-app/gradio的最新版本中存在一个开放重定向漏洞。
    该漏洞允许攻击者通过URL编码将用户重定向到恶意网站。
    可以通过向应用程序发送精心制作的请求来利用此漏洞，这将导致302重定向到攻击者控制的站点。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:L/I:L/A:N
  severity: 中等
  security_advise: |
    1. 监控和过滤传入的请求以防止恶意URL编码。
    2. 对重定向URL实施严格验证。
    3. 定期更新Gradio到最新版本以受益于安全补丁。
rule: version > "0" && version < "4.44.0" # 假设4.44.0是修复此漏洞的版本
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-8021
  - https://huntr.com/bounties/adc23067-ec04-47ef-9265-afd452071888