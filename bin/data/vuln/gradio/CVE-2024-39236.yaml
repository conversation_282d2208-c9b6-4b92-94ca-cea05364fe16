info:
  name: gradio
  cve: CVE-2024-39236
  summary: Gradio 组件存在代码注入漏洞
  details: Gradio v4.36.1 的组件 /gradio/component_meta.py 存在代码注入漏洞，该漏洞可通过特制的输入触发。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: 升级到 gradio 版本大于 4.36.1 以解决此问题。
rule: version = "4.36.1"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-39236
 - https://github.com/gradio-app/gradio/issues/8853
 - https://github.com/Aaron911/PoC/blob/main/Gradio.md
 - https://github.com/advisories/GHSA-9v2f-6vcg-3hgv