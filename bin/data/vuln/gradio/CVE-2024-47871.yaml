info:
  name: gradio
  cve: CVE-2024-47871
  summary: Gradio 在 FRP 客户端和服务器之间使用不安全的通信
  details: 此漏洞涉及当 Gradio 的 `share=True` 选项被使用时，FRP（快速反向代理）客户端和服务器之间的不安全通信。连接上未强制使用 HTTPS，允许攻击者截获和读取上传到 Gradio 服务器的文件，以及修改客户端和服务器之间发送的响应或数据。这影响了使用 `share=True` 在互联网上公开共享 Gradio 演示文稿的用户，而没有适当的加密，使敏感数据暴露于潜在的窃听者面前。
  cvss: CVSS:3.1/AV:A/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N
  severity: HIGH
  security_advise: 升级到 `gradio>=5` 以解决此问题。作为临时解决方案，用户可以避免在生产环境中使用 `share=True`，而是将他们的 Gradio 应用程序托管在启用了 HTTPS 的服务器上以确保安全通信。
rule: version > "0" && version < "5.0.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-279j-x4gx-hfrh
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47871
 - https://github.com/gradio-app/gradio