info:
  name: gradio
  cve: CVE-2025-5320
  summary: Gradio CORS 域名验证绕过漏洞
  details: |
    在 gradio-app gradio 版本 5.29.1 及之前版本中发现了一个被归类为有问题的漏洞。
    这影响了组件 CORS Handler 的函数 is_valid_origin。对参数 localhost_aliases 的操纵导致了域名验证错误。可以远程发起攻击。攻击的复杂性相当高。可利用性被认为很困难。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:L/A:N
  severity: 低
  security_advise: |
    1. 升级到 gradio>=5.29.2
    2. 审查并强制执行 CORS Handler 中严格的域名验证
    3. 监控与 CORS 相关的任何可疑活动
rule: version >= "5.0.0" && version <= "5.29.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-5320
  - https://gist.github.com/superboy-zjc/aa3dfa161d7b19d8a53ab4605792f2fe
  - https://gist.github.com/superboy-zjc/aa3dfa161d7b19d8a53ab4605792f2fe#proof-of-concept-poc
  - https://github.com/gradio-app/gradio
  - https://vuldb.com/?ctiid.310491
  - https://vuldb.com/?id.310491
  - https://vuldb.com/?submit.580250