info:
  name: gradio
  cve: CVE-2024-47867
  summary: Gradio缺乏对下载的FRP客户端的完整性检查
  details: |
    此漏洞是**缺乏对下载的FRP客户端的完整性检查**，这可能允许攻击者引入恶意代码。如果攻击者获取到FRP客户端下载的远程URL，他们可以在不被检测到的情况下修改二进制文件，因为Gradio服务器不验证文件的校验和或签名。
    **谁受影响？**
    任何使用Gradio服务器共享机制下载FRP客户端的用户都可能受到此漏洞的影响，尤其是那些依赖可执行二进制文件进行安全数据隧道的用户。
    ### 修补程序
    是的，请升级到`gradio>=5.0`，其中包括修复以验证下载的二进制文件的完整性。
    ### 变通方法
    如果不升级，没有直接的解决方法。但是，用户可以通过在自己的环境中实现校验和或签名验证来手动验证下载的FRP客户端的完整性，以确保二进制文件未被篡改。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到gradio>=5.0以解决此问题，该版本包括修复以验证下载的二进制文件的完整性。
rule: version > "0" && version < "5.0.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-8c87-gvhj-xm8m
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47867
 - https://github.com/gradio-app/gradio