info:
  name: gradio
  cve: CVE-2024-4253
  summary: Gradio 命令注入漏洞
  details: gradio-app/gradio 仓库中的 'test-functional.yml' 工作流存在命令注入漏洞。由于未对命令中使用的特殊元素进行适当中和，允许未经授权修改基础仓库或泄露秘密。该问题影响版本截至 '@gradio/video@0.6.12'。缺陷存在于工作流处理 GitHub 上下文信息时，未充分消毒就回显了头仓库的全名、头分支和工作流引用。这可能会导致敏感秘密如 'GITHUB_TOKEN'、'COMMENT_TOKEN' 和 'CHROMATIC_PROJECT_TOKEN' 的泄露。
  cvss: CVSS:3.0/AV:N/AC:H/PR:L/UI:N/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 @gradio/video@0.6.13 或更高版本以解决此问题。
rule: version <= "0.6.12"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-4253
 - https://github.com/gradio-app/gradio/commit/a0e70366a8a406fdd80abb21e8c88a3c8e682a2b
 - https://huntr.com/bounties/23cb3749-8ae9-4e1a-9023-4a20ca6b675e