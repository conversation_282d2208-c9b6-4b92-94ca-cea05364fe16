info:
  name: gradio
  cve: CVE-2024-4941
  summary: Gradio 本地文件包含漏洞
  details: gradio-app/gradio 的 JSON 组件中存在本地文件包含漏洞，在版本 4.25 中被发现。漏洞源于 `gradio/components/json_component.py` 中 `postprocess()` 函数的不当输入验证，用户控制的字符串被解析为 JSON。如果解析的 JSON 对象包含 `path` 键，指定的文件会被移动到临时目录，之后可以通过 `/file=..` 端点检索。此问题是由于 `processing_utils.move_files_to_cache()` 函数遍历任何传入的对象，寻找包含 `path` 键的字典，然后将指定的文件复制到临时目录。攻击者可利用此漏洞读取远程系统上的文件，带来重大安全风险。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 gradio>=4.31.3 以解决此问题。
rule: version > "0" && version < "4.31.3"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-4941
 - https://github.com/gradio-app/gradio/commit/ee1e2942e0a1ae84a08a05464e41c8108a03fa9c
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/39889ce1-298d-4568-aecd-7ae40c2ca58e