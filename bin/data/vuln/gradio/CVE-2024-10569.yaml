info:
  name: gradio
  cve: CVE-2024-10569
  summary: Gradio易受通过特制Zip炸弹实现的拒绝服务（DoS）攻击
  details: |
    gradio-app/gradio（版本git 98cbcae）的数据帧组件存在漏洞，允许进行zip炸弹攻击。
    该组件使用pd.read_csv处理输入值，可以接受压缩文件。
    攻击者可以通过上传恶意制作的zip炸弹来利用这一点，导致服务器崩溃并造成服务拒绝。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
  severity: HIGH
  security_advise: |
    1. 升级到gradio >= 5.0.0b3
    2. 实施输入验证以拒绝zip炸弹文件
    3. 监控服务器日志中可能表明攻击的不寻常活动
rule: version >= "4.0.0" && version < "5.0.0b3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10569
  - https://github.com/gradio-app/gradio
  - https://github.com/gradio-app/gradio/blob/98cbcaef827de7267462ccba180c7b2ffb1e825d/gradio/components/dataframe.py#L263
  - https://huntr.com/bounties/7192bcbb-08a3-4d22-a321-9c6d19dbfc74