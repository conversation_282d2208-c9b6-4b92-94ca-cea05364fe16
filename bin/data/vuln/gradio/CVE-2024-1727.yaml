info:
  name: gradio
  cve: CVE-2024-1727
  summary: Gradio 应用程序本地运行时易受第三方网站访问路由和上传文件的攻击
  details: 此漏洞涉及第三方网站能够访问本地运行的 Gradio 应用程序的路由并上传文件。例如，[www.dontvisitme.com](http://www.dontvisitme.com/) 的恶意所有者可以在其网站上放置一个脚本，该脚本将大文件上传到 http://localhost:7860/upload，任何访问该网站且有 Gradio 应用程序的用户现在将在其计算机上上传该大文件。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:N/I:N/A:L
  severity: MEDIUM
  security_advise: 升级到 gradio>=4.19.2 以解决此问题。
rule: version >= "0" && version < "4.19.2"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-48cq-79qq-6f7x
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1727
 - https://github.com/gradio-app/gradio/pull/7503
 - https://github.com/gradio-app/gradio/commit/84802ee6a4806c25287344dce581f9548a99834a
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/a94d55fb-0770-4cbe-9b20-97a978a2ffff