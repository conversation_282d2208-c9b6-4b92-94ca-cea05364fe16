info:
  name: gradio
  cve: CVE-2024-47084
  summary: Gradio的CORS来源验证在请求包含cookie时未执行
  details: 此漏洞与CORS来源验证有关，Gradio服务器在请求包含cookie时未能验证请求来源。这允许攻击者的网站向本地Gradio服务器发出未经授权的请求。如果受害者在登录Gradio时访问恶意网站，攻击者可能会上传文件、窃取身份验证令牌并访问用户数据。这会影响已在本地部署Gradio并使用基本身份验证的用户。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到gradio>=4.44以解决此问题，或通过修改本地Gradio服务器代码中的CustomCORSMiddleware类来手动强制执行更严格的CORS来源验证。具体来说，可以绕过跳过包含Cookie的请求的CORS验证的条件，以防止潜在的漏洞利用。
rule: version > "0" && version < "4.44.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-3c67-5hwx-f6wx
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47084
 - https://github.com/gradio-app/gradio