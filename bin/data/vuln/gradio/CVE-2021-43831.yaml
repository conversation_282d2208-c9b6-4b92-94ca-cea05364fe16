info:
  name: gradio
  cve: CVE-2021-43831
  summary: 主机上的文件可以通过Gradio界面访问
  details: 此漏洞影响任何使用`gradio<2.4.8`创建并公开分享Gradio界面的人。由于静态文件的提供方式，生成公共Gradio链接并与他人分享的人可能会在链接处于活动状态时暴露生成链接的计算机上的文件。如果攻击者知道确切的相对文件路径，他们将能够查看计算机上文件的内容。我们没有任何证据表明这个问题曾经被利用过，但我们非常重视这个问题，并立即采取了措施来减轻它。
  cvss: CVSS:3.1/AV:A/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:L
  severity: CRITICAL
  security_advise: 升级到gradio>=2.5.0以解决此问题。如果您仍在使用受影响的版本，请立即升级。
rule: version < "2.5.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-rhq2-3vr9-6mcr
 - https://nvd.nist.gov/vuln/detail/CVE-2021-43831
 - https://github.com/gradio-app/gradio/commit/41bd3645bdb616e1248b2167ca83636a2653f781
 - https://github.com/gradio-app/gradio
 - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2021-873.yaml