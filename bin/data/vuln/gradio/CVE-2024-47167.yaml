info:
  name: gradio
  cve: CVE-2024-47167
  summary: Gradio 在 /queue/join 路径参数中存在 SSRF 漏洞
  details: 此漏洞涉及 `/queue/join` 端点中的服务器端请求伪造（SSRF）。Gradio 的 `async_save_url_to_cache` 函数允许攻击者强制 Gradio 服务器向用户控制的 URL 发送 HTTP 请求。这可能使攻击者能够针对本地网络内的内部服务器或服务，并可能泄露数据或引起不必要的内部请求。此外，这些 URL 的内容被存储在本地，使攻击者更容易将潜在的恶意文件上传到服务器。这会影响部署了使用涉及 URL 获取的组件（如视频组件）的 Gradio 服务器的用户。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 gradio>=5 以解决此问题。作为临时解决方案，用户可以在其 Gradio 应用程序中禁用或严格限制基于 URL 的输入，仅允许受信任的域。此外，实施更严格的 URL 验证（例如基于允许列表的验证）并确保无法通过 `/queue/join` 端点请求本地或内部网络地址，可以帮助减轻 SSRF 攻击的风险。
rule: version > "0" && version < "5.0.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-576c-3j53-r9jj
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47167
 - https://github.com/gradio-app/gradio