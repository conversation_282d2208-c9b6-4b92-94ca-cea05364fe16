info:
  name: gradio
  cve: CVE-2024-1561
  summary: gradio 存在路径遍历漏洞
  details: 在 gradio-app/gradio 中发现一个问题，其中 `/component_server` 端点不正确地允许使用攻击者控制的参数调用 `Component` 类的任何方法。具体来说，通过利用 `Block` 类的 `move_resource_to_block_cache()` 方法，攻击者可以将文件系统上的任何文件复制到临时目录，然后检索它。此漏洞使未经授权的本地文件读取访问成为可能，特别是当应用程序通过 `launch(share=True)` 暴露于互联网时，从而允许远程攻击者读取主机上的文件。此外，托管在 `huggingface.co` 上的 gradio 应用程序也受到影响，可能导致存储在环境变量中的 API 密钥和凭据等敏感信息的泄露。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到 gradio >= 4.13.0 版本以解决此问题。
rule: version > "0" && version < "4.13.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1561
 - https://github.com/gradio-app/gradio/commit/24a583688046867ca8b8b02959c441818bdb34a2
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/4acf584e-2fe8-490e-878d-2d9bf2698338
 - https://www.gradio.app/changelog#4-13-0