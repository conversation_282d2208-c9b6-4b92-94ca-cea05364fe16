info:
  name: gradio
  cve: CVE-2024-47872
  summary: Gradio 服务器存在通过上传 HTML、JS 或 SVG 文件导致的跨站脚本（XSS）漏洞
  details: 此漏洞涉及任何允许文件上传的 Gradio 服务器上的跨站脚本（XSS）。认证用户可以上传包含恶意脚本的 HTML、JavaScript 或 SVG 文件。当其他用户下载或查看这些文件时，脚本将在其浏览器中执行，允许攻击者执行未经授权的操作或从其会话中窃取敏感信息。这会影响任何允许文件上传的 Gradio 服务器，特别是那些使用处理或显示用户上传文件的组件的服务器。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到 gradio>=5 以解决此问题。作为临时解决方案，用户可以限制可上传到 Gradio 服务器的文件类型，仅允许上传非可执行文件类型，如图像或文本。此外，开发人员可以实现服务器端验证，对上传的文件进行消毒，确保 HTML、JavaScript 和 SVG 文件在存储或显示给用户之前得到适当处理或拒绝。
rule: version > "0" && version < "5.0.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-gvv6-33j7-884g
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47872
 - https://github.com/gradio-app/gradio