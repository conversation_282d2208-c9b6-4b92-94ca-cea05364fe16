info:
  name: gradio
  cve: CVE-2023-51449
  summary: Gradio 修复了 `/file` 路由的文件遍历和服务器端请求伪造攻击漏洞
  details: 较旧版本的 `gradio` 在 `/file` 路由中存在漏洞，使攻击者能够通过已知文件路径访问运行 Gradio 应用的机器上的任意文件（例如，如果演示是通过 `share=True` 创建的，或在 Hugging Face Spaces 上）。此漏洞无法通过常规浏览器 URL 实现，但可以通过使用 `curl` 等程序工具的 `--pass-as-is` 标志实现。此外，Gradio 应用中的 `/file` 路由还存在 SSRF 攻击漏洞。这两个漏洞已在 `gradio==4.11.0` 中修复。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:L/A:L
  severity: HIGH
  security_advise: 升级到 gradio>=4.11.0 以解决此问题。
rule: version > "0" && version < "4.11.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-6qm2-wpxq-7qh2
  - https://nvd.nist.gov/vuln/detail/CVE-2023-51449
  - https://github.com/gradio-app/gradio/commit/1b9d4234d6c25ef250d882c7b90e1f4039ed2d76
  - https://github.com/gradio-app/gradio/commit/7ba8c5da45b004edd12c0460be9222f5b5f5f055
  - https://github.com/gradio-app/gradio
  - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2023-249.yaml