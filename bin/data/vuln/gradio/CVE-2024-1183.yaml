info:
  name: gradio
  cve: CVE-2024-1183
  summary: gradio 服务器端请求伪造漏洞
  details: gradio-app/gradio 存在一个服务器端请求伪造（SSRF）漏洞，允许攻击者扫描和识别内部网络中的开放端口。通过操纵 GET 请求中的 'file' 参数，攻击者可以根据响应中是否存在 'Location' 头部或 'File not allowed' 错误来判断内部端口的状态。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 gradio >= "4.10.0" 版本以解决此问题。
rule: version >= "0" && version < "4.10.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-1183
 - https://github.com/gradio-app/gradio/commit/2ad3d9e7ec6c8eeea59774265b44f11df7394bb4
 - https://github.com/gradio-app/gradio/commit/7ba8c5da45b004edd12c0460be9222f5b5f5f055
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/103434f9-87d2-42ea-9907-194a3c25007c