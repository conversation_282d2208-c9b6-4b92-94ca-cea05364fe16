info:
  name: gradio
  cve: CVE-2023-6572
  summary: Gradio 向未授权方暴露敏感信息漏洞
  details: 在 GitHub 仓库 gradio-app/gradio 的 main 分支之前，存在向未授权方暴露敏感信息的漏洞。
  cvss: CVSS:3.0/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:N
  severity: CRITICAL
  security_advise: 升级到 gradio >= "4.14.0" 版本以解决此问题。
rule: version > "0" && version < "4.14.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2023-6572
 - https://github.com/gradio-app/gradio/commit/5b5af1899dd98d63e1f9b48a93601c2db1f56520
 - https://github.com/gradio-app/gradio
 - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2023-255.yaml
 - https://huntr.com/bounties/21d2ff0c-d43a-4afd-bb4d-049ee8da5b5c