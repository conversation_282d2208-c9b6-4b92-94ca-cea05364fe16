info:
  name: gradio
  cve: CVE-2024-47166
  summary: Gradio在`/custom_component`中存在一级读取路径遍历漏洞
  details: 此漏洞涉及`/custom_component`端点中的一级读取路径遍历。攻击者可以通过操纵请求中的文件路径来利用此缺陷，访问并泄露自定义Gradio组件的源代码。尽管遍历仅限于单个目录级别，但它可能会暴露开发者打算保密的专有或敏感代码。这会影响在公开可访问的服务器上托管自定义Gradio组件的用户。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到gradio>=4.44以解决此问题，或者作为临时解决方案，开发者可以清理文件路径，并确保组件不存储在公开可访问的目录中。
rule: version > "0" && version < "4.44.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-37qc-qgx6-9xjv
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47166
 - https://github.com/gradio-app/gradio