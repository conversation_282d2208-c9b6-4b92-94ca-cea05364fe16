info:
  name: gradio
  cve: CVE-2024-47164
  summary: Gradio的`is_in_or_equal`函数可能被绕过
  details: |
    ### 影响
    **这是什么类型的漏洞？谁受影响？**
    此漏洞与`is_in_or_equal`函数内的**目录遍历检查绕过**有关。该函数旨在检查文件是否位于给定目录内，但使用某些有效载荷可以通过`..`（父目录）序列操纵文件路径来绕过。如果攻击者能够利用此漏洞，他们可能能够访问受限文件，尽管难度较高。这主要影响依赖Gradio的阻止列表或目录访问验证的用户，特别是在处理文件上传时。
    ### 修复
    是的，请升级到`gradio>=5.0`以解决此问题。
    ### 变通方法
    **用户是否有办法在不升级的情况下修复或缓解漏洞？**
    作为变通方法，用户可以在将文件路径传递给`is_in_or_equal`函数之前，手动清理和规范化Gradio部署中的文件路径。确保所有文件路径都已正确解析和绝对化，可以帮助减轻由于不当处理`..`序列或格式错误的路径而导致的绕过漏洞。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N
  severity: MEDIUM
  security_advise: 升级到gradio>=5.0以解决此问题，或者作为变通方法，用户可以手动清理和规范化Gradio部署中的文件路径，确保所有文件路径都已正确解析和绝对化。
rule: version < "5.0.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-77xq-6g77-h274
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47164
 - https://github.com/gradio-app/gradio/commit/08b51590163b306fd874f543f6fcaf23ac7d2646
 - https://github.com/gradio-app/gradio