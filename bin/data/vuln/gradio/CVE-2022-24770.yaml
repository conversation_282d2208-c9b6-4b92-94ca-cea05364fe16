info:
  name: gradio
  cve: CVE-2022-24770
  summary: Gradio CSV文件中公式元素的不当中和
  details: gradio库具有标记功能，可以将输入/输出数据保存到开发人员计算机上的CSV文件中。这允许用户将任意文本（如命令）保存到CSV文件中。如果像MS Excel这样的程序打开这样的文件，那么它会自动运行这些命令，这可能导致在用户的计算机上运行任意命令。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到gradio>=2.8.11以解决此问题，或者如果使用旧版本，请避免使用Excel或类似程序打开gradio生成的CSV文件。
rule: version > "0" && version < "2.8.11"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-f8xq-q7px-wg8c
 - https://nvd.nist.gov/vuln/detail/CVE-2022-24770
 - https://github.com/gradio-app/gradio/pull/817
 - https://github.com/gradio-app/gradio/commit/80fea89117358ee105973453fdc402398ae20239
 - https://github.com/gradio-app/gradio
 - https://github.com/pypa/advisory-database/tree/main/vulns/gradio/PYSEC-2022-229.yaml