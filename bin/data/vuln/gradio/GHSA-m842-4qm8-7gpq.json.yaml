info:
  name: gradio
  cve: CVE-2024-1728
  summary: Gradio 允许用户访问任意文件
  details: |
    ### 影响
    此漏洞允许使用具有公共链接（例如在 Hugging Face Spaces 上）的 Gradio 应用程序的用户访问托管 Gradio 应用程序的机器上的文件。这涉及拦截和修改 Gradio 应用程序向服务器发出的网络请求。
    ### 补丁
    是的，该问题已在 Gradio 版本 4.19.2 或更高版本中得到修复。我们不知道此漏洞是否被用于针对 Gradio 应用程序的用户，但我们鼓励所有用户升级到 Gradio 4.19.2 或更高版本。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H
  severity: CRITICAL
  security_advise: |
    1. 升级到 gradio>=4.19.2
    2. 监控并限制对具有公共链接的 Gradio 应用程序的访问
    3. 定期检查来自 Gradio 项目的更新和安全公告
rule: version > "0" && version < "4.19.2"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-m842-4qm8-7gpq
  - https://github.com/gradio-app/gradio/commit/16fbe9cd0cffa9f2a824a0165beb43446114eec7
  - https://nvd.nist.gov/vuln/detail/CVE-2024-1728