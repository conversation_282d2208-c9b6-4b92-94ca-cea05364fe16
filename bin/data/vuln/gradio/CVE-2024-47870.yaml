info:
  name: gradio
  cve: CVE-2024-47870
  summary: Gradio在update_root_in_config中的竞态条件可能导致用户流量重定向
  details: 此漏洞涉及`update_root_in_config`函数中的**竞态条件**，允许攻击者修改Gradio前端用于与后端通信的`root` URL。通过利用此漏洞，攻击者可以将用户流量重定向到恶意服务器。这可能导致敏感数据（如身份验证凭据或上传的文件）被截获。这会影响所有连接到Gradio服务器的用户，尤其是那些暴露在互联网上的用户，恶意行为者可能会利用此竞态条件。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:L/A:H
  severity: HIGH
  security_advise: 升级到gradio>=5.0.0以解决此问题。
rule: version >= "0" && version < "5.0.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-xh2x-3mrm-fwqm
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47870
 - https://github.com/gradio-app/gradio