info:
  name: gradio
  cve: CVE-2025-23042
  summary: Gradio Blocked Path ACL绕过漏洞
  details: |
    Gradio的文件路径访问控制列表（ACL）可以通过更改被阻止文件或目录路径的字母大小写来绕过。此漏洞是由于文件路径验证逻辑中缺乏大小写规范化造成的。在Windows和macOS等不区分大小写的文件系统上，此缺陷使攻击者能够绕过安全限制并访问应受保护的敏感文件。
    此问题可能导致未经授权的数据访问，泄露敏感信息并破坏Gradio的安全模型完整性。鉴于Gradio在构建Web应用程序（特别是在机器学习和AI领域）方面的受欢迎程度，如果此漏洞在生产环境中被利用，可能会构成重大威胁。
  cvss: CVSS:4.0/AV:N/AC:L/AT:P/PR:N/UI:N/VC:H/VI:H/VA:N/SC:N/SI:N/SA:N
  severity: CRITICAL
  security_advise: |
    1. **路径大小写规范化**：
       - 在评估ACL之前，将请求路径和被阻止路径的大小写规范化（例如，将所有路径转换为小写）。
       - 示例：
         ```python
         normalized_path = str(path).lower()
         normalized_blocked_paths = [str(p).lower() for p in blocked_paths]
         ```
    2. **更新文档**：
       - 警告开发者在部署Gradio时不区分大小写的文件系统上的潜在风险。
    3. **发布安全补丁**：
       - 通知用户漏洞情况并发布带有修复逻辑的Gradio更新版本。
rule: version <= "5.6.0"
references:
  - https://github.com/gradio-app/gradio/security/advisories/GHSA-j2jg-fq62-7c3h
  - https://nvd.nist.gov/vuln/detail/CVE-2025-23042
  - https://github.com/gradio-app/gradio/commit/6b63fdec441b5c9bf910f910a2505d8defbb6bf8
  - https://github.com/gradio-app/gradio
  - https://github.com/gradio-app/gradio/releases/tag/gradio%405.11.0