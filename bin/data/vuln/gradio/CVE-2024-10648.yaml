info:
  name: gradio
  cve: CVE-2024-10648
  summary: Gradio易受任意文件删除漏洞影响
  details: |
    gradio-app/gradio的Gradio音频组件中存在路径遍历漏洞，自版本git 98cbcae起。
    该漏洞允许攻击者控制音频文件的格式，导致任意文件内容删除。
    通过操纵输出格式，攻击者可以将任何文件重置为空文件，从而导致服务器拒绝服务（DOS）。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:H
  severity: HIGH
  security_advise: |
    1. 升级至gradio >= 5.0.0b3
    2. 审查并修补Gradio音频组件中的路径遍历漏洞
    3. 实施严格的文件输入验证，以防止未经授权的文件操作
rule: version >= "4.0.0" && version < "5.0.0b3"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2024-10648
  - https://github.com/gradio-app/gradio
  - https://github.com/gradio-app/gradio/blame/98cbcaef827de7267462ccba180c7b2ffb1e825d/gradio/processing_utils.py#L234
  - https://huntr.com/bounties/667d664d-8189-458c-8ed7-483fe8f33c76