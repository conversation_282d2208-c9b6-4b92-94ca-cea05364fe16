info:
  name: gradio
  cve: CVE-2024-4325
  summary: gradio中的服务器端请求伪造(SSRF)漏洞
  details: gradio-app/gradio中存在一个服务器端请求伪造(SSRF)漏洞，该漏洞存在于版本4.21.0的`/queue/join`端点和`save_url_to_cache`函数中。当从用户获取并预期为URL的`path`值用于发起HTTP请求时，由于缺乏足够的验证检查，会出现此漏洞。这个缺陷允许攻击者发送精心制作的请求，可能导致未经授权地访问本地网络或AWS元数据端点，从而危害内部服务器的安全。
  cvss: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:N/A:N
  severity: HIGH
  security_advise: 升级到gradio版本4.36.0以上以解决此问题。
rule: version >= "0" && version <= "4.36.0"
references:
 - https://nvd.nist.gov/vuln/detail/CVE-2024-4325
 - https://github.com/gradio-app/gradio/pull/8301
 - https://github.com/gradio-app/gradio
 - https://huntr.com/bounties/b34f084b-7d14-4f00-bc10-048a3a5aaf88