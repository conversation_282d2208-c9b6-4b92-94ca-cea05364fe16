info:
  name: gradio
  cve: CVE-2024-47868
  summary: Gradio 存在多个组件后处理步骤允许任意文件泄露
  details: |
    ### 影响
    **这是什么类型的漏洞？谁受影响？**
    这是一个**数据验证漏洞**，影响 Gradio 的多个组件，该漏洞允许通过后处理步骤任意文件泄露。攻击者可以通过制作绕过预期输入约束的请求来利用这些组件。这个问题可能导致敏感文件暴露给未经授权的用户，特别是当与其他漏洞（如 TOB-GRADIO-15）结合使用时。风险最高的组件是那些返回或处理文件数据的组件。
    ### 易受攻击的组件:
    1. **字符串到 FileData:** DownloadButton, Audio, ImageEditor, Video, Model3D, File, UploadButton.
    2. **复杂数据到 FileData:** Chatbot, MultimodalTextbox.
    3. **预处理中直接读取文件:** Code.
    4. **字典转换为 FileData:** ParamViewer, Dataset.
    ### 利用场景:
    1. 开发者创建一个下拉列表，将值传递给 DownloadButton。攻击者绕过允许的输入，发送任意文件路径（如 `/etc/passwd`），并下载敏感文件。
    2. 攻击者在 ParamViewer 组件中制作恶意有效载荷，通过任意文件泄露从服务器泄露敏感文件。
    ### 补丁
    是的，该问题已在 `gradio>5.0` 中解决。升级到最新版本将缓解此漏洞。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到 gradio 版本大于 5.0.0 以解决此问题。
rule: version < "5.0.0"
references:
 - https://github.com/gradio-app/gradio/security/advisories/GHSA-4q3c-cj7g-jcwf
 - https://nvd.nist.gov/vuln/detail/CVE-2024-47868
 - https://github.com/gradio-app/gradio