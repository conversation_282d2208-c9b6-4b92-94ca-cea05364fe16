info:
  name: dify
  cve: CVE-2025-0185
  summary: Dify 工具 Vanna 模块的 Pandas 查询注入漏洞
  details: |
    Dify 工具的 Vanna 模块中存在 Pandas 查询注入漏洞。该漏洞出现在函数 vn.get_training_plan_generic(df_information_schema) 中，该函数在执行 Pandas 库查询之前未能正确清理用户输入。如果被利用，这可能导致远程代码执行 (RCE)。
    攻击者可以通过绕过查询清理，在系统中执行任意代码。此漏洞风险重大，应尽快解决以防止未经授权的访问或数据泄露。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: 建议升级到最新版本的 Dify 工具或应用官方提供的安全补丁来解决此问题。同时，应检查并更新所有相关的依赖库以确保系统的整体安全性。
references:
    - https://huntr.com/bounties/7d9eb9b2-7b86-45ed-89bd-276c1350db7e
    - https://huntr.com/bounties/ea4bc85e-9639-42be-8db9-c0738025cb32
    - https://nvd.nist.gov/vuln/detail/CVE-2025-0185
rule: ""