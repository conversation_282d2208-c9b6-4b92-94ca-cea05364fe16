info:
  name: dify
  cve: CVE-2024-12776
  summary: Dify 密码重置漏洞导致管理员账号接管
  details: |
    当请求密码重置时，应用程序会为用户提供一个令牌，并将相应的验证码发送到电子邮件收件箱。然后用户会被路由到一个页面，该页面将通过 /forgot-password/validity API 端点检查验证码，如果成功，用户将被转发到 /forgot-password/resets。然而，/forgot-password/resets 不检查验证码，可以通过直接访问 /forgot-password/resets 并重置任意用户的密码轻松绕过检查。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到 dify >= v0.10.2 版本以解决此问题，确保在密码重置过程中对验证码进行严格检查。
rule: version < "0.10.2"
references:
 - https://huntr.com/bounties/00a8b403-7da5-431e-afa3-40339cf734bf
 - https://cwe.mitre.org/data/definitions/305.html
 - https://nvd.nist.gov/vuln/detail/CVE-2024-12776