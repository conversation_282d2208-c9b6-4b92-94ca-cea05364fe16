info:
  name: dify
  cve: CVE-2024-12775
  summary: dify中的非盲SSRF漏洞
  details: |
    dify在创建自定义工具选项的测试功能中存在非盲SSRF漏洞（通过REST API POST /console/api/workspaces/current/tool-provider/api/test/pre）。攻击者可以在OpenAI模式中的服务器字典中设置任意URL目标以进行窃取，从而能够滥用受害dify服务器的凭据来访问网络资源。
    攻击者首先以普通用户身份登录dify网络服务器，然后导航到工具->自定义并点击创建自定义工具按钮，选择示例中的天气（JSON），修改服务器->url为受害者的内部目标地址，点击测试操作后可获取受害者的秘密结果。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 应用一些白名单或黑名单来防止服务器_url的非法值，对创建自定义工具的操作进行更严格的权限验证和URL校验。
rule: version = "0.10.1"
references:
 - https://huntr.com/bounties/e90e929a-9bc9-46ad-a5e5-1f6f124d0f12
 - https://nvd.nist.gov/vuln/detail/CVE-2024-12775