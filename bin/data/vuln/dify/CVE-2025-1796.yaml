info:
  name: dify
  cve: CVE-2025-1796
  summary: Dify 使用弱伪随机数生成器生成密码重置码导致管理员账户接管漏洞
  details: |
    Dify 使用 `random.randint` 来生成其密码重置码。这些伪随机数生成器不适合用于加密目的，可以被破解，这允许拥有使用工作流工具权限的用户（需要获取随机数生成器的输出）入侵任何账户，包括管理员账户。
    攻击者可以通过提取伪随机数生成器的输出来破解种子，然后使用这些种子生成有效的密码重置码，从而重置任何用户的密码，包括管理员账户。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到最新版本的 Dify 以解决此问题，确保使用加密安全的随机数生成器来生成密码重置码。
rule: version < "0.10.2"
references:
 - https://huntr.com/bounties/a60f3039-5394-4e22-8de7-a7da9c6a6e00
 - https://nvd.nist.gov/vuln/detail/CVE-2025-1796
 - https://github.com/langgenius/dify