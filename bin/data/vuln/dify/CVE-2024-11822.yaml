info:
  name: dify
  cve: CVE-2024-11822
  summary: langgenius/dify中的服务器端请求伪造(SSRF)漏洞
  details: |
    此漏洞允许恶意用户通过利用api_endpoint参数直接向内部网络发起请求，从而可能获取内部服务器的访问权限。此外，攻击者还可以访问AWS元数据端点。
    漏洞的证明概念(POC)包括启动一个本地服务，并通过特定的curl命令触发漏洞，该命令向本地服务发送一个包含api_endpoint的请求。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到dify的更新版本以解决此问题，确保对api_endpoint参数进行严格的验证和过滤，防止未经授权的内部网络访问。
rule: version < "0.9.1"
references:
 - https://huntr.com/bounties/f3042029-5d4e-41c6-850d-bbe02fae6592
 - https://nvd.nist.gov/vuln/detail/CVE-2024-11822