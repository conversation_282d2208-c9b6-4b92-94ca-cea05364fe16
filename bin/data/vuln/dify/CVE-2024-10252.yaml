info:
  name: dify
  cve: CVE-2024-10252
  summary: Dify Sandbox逃逸漏洞
  details: |
    此漏洞存在于Dify沙箱服务中，允许攻击者通过内部SSRF请求注入代码，并以root权限执行任意Python代码。这可能会导致攻击者删除整个沙箱服务，造成不可逆的损害。
    漏洞利用步骤包括在Dify网站上安装Dify容器并访问Web界面，创建工作流并在preload部分输入漏洞利用代码，从而允许以root权限执行任意Python代码，可能导致对沙箱服务的破坏性行动。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:H/A:H
  severity: HIGH
  security_advise: 升级到dify-sandbox>=0.2.10版本以解决此问题，该版本已修复了API密钥的默认弱密码问题，并增强了沙箱服务的安全性。
rule: version <= "0.9.1"
references:
 - https://huntr.com/bounties/62c6c958-96cb-426c-aebc-c41f06b9d7b0
 - https://github.com/langgenius/dify/releases/tag/v0.2.10
 - https://nvd.nist.gov/vuln/detail/CVE-2024-10252