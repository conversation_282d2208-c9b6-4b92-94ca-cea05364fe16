info:
  name: dify
  cve: CVE-2025-29720
  summary: Dify v1.0包含一个服务器端请求伪造(SSRF)漏洞。
  details: |
    Dify v1.0被发现包含一个通过组件controllers.console.remote_files.RemoteFileUploadApi的服务器端请求伪造(SSRF)漏洞。
  cvss: CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:U/C:L/I:L/A:L
  severity: MEDIUM
  security_advise: |
    1. 升级到解决此漏洞的最新版本的Dify。
    2. 对远程文件上传实施严格的输入验证。
    3. 监控并限制应用程序的外发网络请求。
rule: version < "1.0.1"
references:
  - https://nvd.nist.gov/vuln/detail/CVE-2025-29720
  - https://github.com/langgenius/dify/issues/15185
  - https://dify.ai