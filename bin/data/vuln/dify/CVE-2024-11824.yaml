info:
  name: dify
  cve: CVE-2024-11824
  summary: Dify聊天机器人日志中存在存储型XSS漏洞
  details: |
    在Dify的聊天日志中，虽然像<script>、<iframe>和事件处理器等标签通常是不允许的，但像<input>和<form>这样的标签仍然被允许。这使得攻击者可以通过提示注入恶意HTML到日志中。当管理员查看包含恶意HTML的日志时，攻击者可能会窃取管理员的凭据或敏感信息，从而造成存储型跨站脚本攻击（XSS）。
  cvss: CVSS:3.1/AV:N/AC:H/PR:L/UI:R/S:C/C:H/I:H/A:N
  severity: MEDIUM
  security_advise: 升级到dify>=0.12.1版本以解决此问题，该版本已禁用了<input>和<form>等可导致XSS的标签。
rule: version < "0.12.1"
references:
 - https://huntr.com/bounties/72387deb-6e64-48ed-a8c3-b50d22a0970f
 - https://nvd.nist.gov/vuln/detail/CVE-2024-11824
 - https://github.com/langgenius/dify