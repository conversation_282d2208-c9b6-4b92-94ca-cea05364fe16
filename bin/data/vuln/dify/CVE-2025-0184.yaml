info:
  name: dify
  cve: CVE-2025-0184
  summary: Dify的WordExtractor中存在服务器端请求伪造(SSRF)漏洞
  details: |
    在Dify的"Create Knowledge"部分上传DOCX文件时，如果文件中存在外部关系，reltype的值会被当作URL请求。这导致攻击者可以利用SSRF漏洞访问内部网络资源。
    攻击者可以通过上传包含外部关系的DOCX文件，利用此漏洞访问内部服务或资源。尽管Dify有内置的ssrf_proxy来防止SSRF，但WordExtractor中的实现缺陷导致了这个漏洞。
  cvss: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到dify>=0.11.0版本以解决此问题。确保所有外部资源的请求都通过ssrf_proxy进行，以防止SSRF攻击。
  references:
    - https://huntr.com/bounties/a7eac4ae-5d5e-4ac1-894b-7a8cce5cba9b
    - https://github.com/langgenius/dify/compare/main...fix/docx-extract-image-ssrf
    - https://drive.google.com/file/d/1EIHnqOVSlEQN9_vP4CxYqA-77xOPKa1p/view?usp=sharing
rule: version < "0.11.0"