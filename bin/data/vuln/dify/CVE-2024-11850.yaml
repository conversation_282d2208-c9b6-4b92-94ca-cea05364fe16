info:
  name: dify
  cve: CVE-2024-11850
  summary: Dify 中的存储型 XSS 漏洞
  details: |
    在 Dify 的聊天机器人中，启用了对用户输入的 SVG 标记支持。用户的输入在插入时未经任何验证或消毒，这允许攻击者插入 HTML 代码而不进行过滤。例如，攻击者可以通过插入恶意的 SVG 代码来执行 JavaScript，如果管理员查看了包含此恶意代码的聊天记录，攻击者可以执行如弹窗或窃取管理员凭据等恶意行为。
  cvss: CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:N/A:N
  severity: MEDIUM
  security_advise: 升级到最新版本的 Dify 或应用补丁来解决此问题，确保对用户输入进行适当的验证和消毒以防止 XSS 攻击。
rule: ""
references:
 - https://huntr.com/bounties/893da115-028d-4718-b586-a2b77897a470
 - https://nvd.nist.gov/vuln/detail/CVE-2024-11850