info:
  name: dify
  cve: CVE-2024-12039
  summary: Dify 密码重置码猜测次数过多导致管理员账号接管漏洞
  details: |
    虽然对发送的电子邮件应用了速率限制，但对密码重置码的猜测次数没有限制。在足够的网络连接下，可以在给定的5分钟间隔内破解六位数的密码重置码。应用程序处理请求的速度是限制因素。在负载均衡器后面部署多个副本的应用程序将比本地安装更容易破解。
    在笔记本电脑上（使用docker安装），能够在5分钟间隔内处理大约8%的总共100万个可能的代码。经过多次尝试新代码后，大约2小时后找到了匹配项。
  cvss: CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:C/C:H/I:H/A:N
  severity: HIGH
  security_advise: 在应用程序内部限制密码重置码的重试次数，而不是依赖外部速率限制器。
rule: version = "0.10.1"
references:
 - https://huntr.com/bounties/61af30d5-6055-4c6c-8a55-3fa43dada512
 - https://nvd.nist.gov/vuln/detail/CVE-2024-12039