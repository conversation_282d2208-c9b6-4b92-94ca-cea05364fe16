info:
  id: "credential_theft"
  name: "Credential Theft Detection"
  description: "Detect credential theft risks in MCP code"
  author: "Zhuque Security Team"
  categories:
    - code

rules:
  - name: "Sensitive Credential Access"
    pattern: '(\.env|config\.(ini|json|yml)|(secrets|credentials|password|token|\.bashrc|\.profile))'
    description: "Sensitive credential access"
  - name: "System File Access"
    pattern: '(\/etc\/passwd|\/etc\/shadow|\/etc\/httpd\/|\/etc\/.*\.conf(\&|\b)|\/usr\/local\/|\/var\/lib\/|~\/\.ssh)'
    description: "System file access"
  - name: "Credential Keywords"
    pattern: '\b(id_rsa|\.cursor\/mcp\.json|\.cursor\/|\bmcp\.json|api[ -_]?key|access[ -_]?key|auth[ -_]?token)\b'
    description: "Credential keywords"

prompt_template: |
  As a professional cybersecurity analyst, you need to precisely detect credential theft risks in MCP code. This detection requires zero false positives - only report when you find concrete evidence of malicious credential acquisition behavior.

  ## Vulnerability Definition
  Credential theft refers to MCP tools maliciously obtaining, transmitting, or leaking users' sensitive credential information through malicious means, including API keys, passwords, tokens, etc.

  ## Detection Criteria (Must simultaneously meet the following conditions)

  ### 1. Sensitive Credential Access
  **Must identify all the following elements:**
  - **Data Source**: Clear access to sensitive files or environment variables
    - ~/.ssh/, ~/.cursor/mcp.json, .env files
    - Environment variables containing API_KEY, TOKEN, PASSWORD, SECRET
    - System credential storage: /etc/passwd, credential stores
  - **Access Method**: Specific read or access code
  - **Malicious Intent**: Credential access not required for normal business operations

  ### 2. Credential Transmission or Leakage
  **Must exhibit one of the following behaviors:**
  - Transmitting credentials to external servers via network interfaces
  - Writing credentials to insecure storage locations
  - Exposing credentials in logs or error messages
  - Leaking sensitive information through API return values

  ### 3. Attack Feasibility Verification
  **Must confirm:**
  - Attack code is executable in the current environment
  - Clear data flow exists from credential acquisition to leakage
  - Ability to bypass existing security protection measures
  - Actual harm capability

  ## Technical Detection Patterns

  ### File System Access Patterns
  **High-Risk Patterns:**
  - open("~/.ssh/id_rsa", "r")
  - os.environ.get("API_KEY")
  - read_file("~/.cursor/mcp.json")
  - Access to system sensitive directories: /etc/, /root/

  ### Network Transmission Patterns
  **Malicious Transmission Patterns:**
  - requests.post(external_url, data=credentials)
  - socket.send(api_key_data)
  - Sending credentials to unauthorized domains

  ### Environment Variable Enumeration
  **Batch Acquisition Patterns:**
  - os.environ traversal
  - Searching for variables containing key, secret, token
  - Batch export of environment variables

  ## Exclusion Conditions (Do not report the following)

  ### Normal Business Scenarios
  - Normal reading of application's own configuration files
  - Standard authentication processes using official SDKs
  - Reasonable configuration management and key rotation
  - Debug code in local development environment

  ### Security Practices
  - Using key management services (like HashiCorp Vault)
  - Transmission over HTTPS/TLS encryption
  - Authentication processes conforming to OAuth2.0 standards
  - Implementation of appropriate access controls

  ### Test and Example Code
  - Mock credentials in unit tests
  - Placeholders in documentation examples
  - Test configurations in development environment
  - Obviously dummy or example data

  ## Verification Requirements
  1. **Complete Attack Chain**: Complete path from credential acquisition to leakage
  2. **Actual Harm**: Ability to prove real security risks
  3. **Malicious Intent**: Clear attack purpose rather than normal functionality
  4. **Environment Applicability**: Actually executable in the target environment

  ## Special Judgment Rules
  - **Test Credential Identification**: If "test", "demo", "example", "dummy" keywords are found, lower the risk level
  - **Development Environment Judgment**: Local development configurations are not considered high risk
  - **Encrypted Transmission Verification**: Do not report if using HTTPS with appropriate validation
  - **Official API Calls**: Do not report standard processes using official SDKs
  
  {{ if .StaticAnalysisResults }}
  ## Static Analysis Results
  {{.StaticAnalysisResults}}
  {{ end }}

  ## Input Data
  Source code path: {{ .CodePath }}
  Directory structure:
  ------
  {{ .DirectoryStructure }}
  ------

  ## Output Requirements
  Only output when finding concrete credential theft attacks:
  - Specific file paths and line number ranges
  - Complete attack code snippets
  - Data flow analysis: complete path from source to sink
  - Technical analysis: attack principles and execution mechanisms
  - Harm assessment: types of credentials that may be leaked and impact scope
  - Evidence chain: complete evidence of malicious behavior 