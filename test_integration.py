#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试整合后的 process_csv.py 功能
"""

import os
import pandas as pd

def test_integration():
    """测试整合功能"""
    print("=== 测试整合后的功能 ===")
    
    # 检查是否存在已处理的文件
    files_to_check = [
        'temp_ipfo_assets/ip_info.csv',
        'temp_ipfo_assets/ips.csv',
        'temp_ipfo_assets/merged_ip.txt',
        'temp_ipfo_assets/conflict_ip.txt',
        'temp_ipfo_assets/conflict_ip.csv'
    ]
    
    print("\n文件存在性检查:")
    for file_path in files_to_check:
        exists = os.path.exists(file_path)
        print(f"  {file_path}: {'✓' if exists else '✗'}")
        
        if exists:
            if file_path.endswith('.csv'):
                # 检查CSV文件的行数和列数
                df = pd.read_csv(file_path)
                print(f"    -> 行数: {len(df)}, 列数: {len(df.columns)}")
                if 'ip' in df.columns:
                    print(f"    -> IP列样本: {df['ip'].head(3).tolist()}")
            elif file_path.endswith('.txt'):
                # 检查TXT文件的行数
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                print(f"    -> 行数: {len(lines)}")
                print(f"    -> 前3行: {[line.strip() for line in lines[:3]]}")
    
    # 特别检查 ips.csv 是否只有一列
    if os.path.exists('temp_ipfo_assets/ips.csv'):
        print("\n=== ips.csv 详细检查 ===")
        ips_df = pd.read_csv('temp_ipfo_assets/ips.csv')
        print(f"列名: {list(ips_df.columns)}")
        print(f"是否只有一列: {'✓' if len(ips_df.columns) == 1 else '✗'}")
        print(f"第一列是否为'ip': {'✓' if ips_df.columns[0] == 'ip' else '✗'}")
        print(f"前5行数据:")
        print(ips_df.head())

if __name__ == "__main__":
    test_integration()
