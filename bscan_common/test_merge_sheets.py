#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本：验证新的分别合并Sheet1和Sheet2功能
"""

import os
import sys
import glob
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bscan_common.core.utils import merge_xlsx_sheets_separately

def test_merge_sheets():
    """测试分别合并Sheet1和Sheet2的功能"""
    
    # 测试数据目录
    chunk_log_dir = "bscan_common/assets/bscan_common/chunk_log"
    
    # 查找所有的xlsx文件
    xlsx_files = []
    for root, dirs, files in os.walk(chunk_log_dir):
        for file in files:
            if file.lower().endswith('.xlsx'):
                xlsx_files.append(os.path.join(root, file))
    
    print(f"找到 {len(xlsx_files)} 个xlsx文件:")
    for f in xlsx_files:
        print(f"  - {f}")
    
    if not xlsx_files:
        print("错误: 未找到任何xlsx文件进行测试")
        return
    
    # 输出目录
    output_dir = "bscan_common/test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 基础文件名
    base_filename = f"test_merged_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"\n开始测试合并功能...")
    print(f"输出目录: {output_dir}")
    print(f"基础文件名: {base_filename}")
    
    # 调用新的合并函数
    sheet1_path, sheet2_path = merge_xlsx_sheets_separately(
        xlsx_files, 
        output_dir, 
        base_filename
    )
    
    print(f"\n=== 测试结果 ===")
    if sheet1_path:
        print(f"✓ Sheet1合并文件: {sheet1_path}")
        if os.path.exists(sheet1_path):
            print(f"  文件大小: {os.path.getsize(sheet1_path)} bytes")
        else:
            print(f"  ✗ 文件不存在!")
    else:
        print("✗ Sheet1合并失败")
    
    if sheet2_path:
        print(f"✓ Sheet2合并文件: {sheet2_path}")
        if os.path.exists(sheet2_path):
            print(f"  文件大小: {os.path.getsize(sheet2_path)} bytes")
        else:
            print(f"  ✗ 文件不存在!")
    else:
        print("✗ Sheet2合并失败")
    
    # 检查CSV文件
    csv_files = glob.glob(os.path.join(output_dir, f"{base_filename}_*.csv"))
    if csv_files:
        print(f"\n✓ 同时生成了CSV文件:")
        for csv_file in csv_files:
            print(f"  - {csv_file} ({os.path.getsize(csv_file)} bytes)")
    else:
        print("\n✗ 未找到CSV文件")

if __name__ == "__main__":
    test_merge_sheets()
