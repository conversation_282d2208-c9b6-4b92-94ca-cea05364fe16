#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证合并结果的脚本
"""

import pandas as pd
import os
import glob

def verify_merged_files():
    """验证合并后的文件"""
    
    # 查找最新的测试输出文件
    output_dir = "bscan_common/test_output"
    
    xlsx_files = glob.glob(os.path.join(output_dir, "*.xlsx"))
    csv_files = glob.glob(os.path.join(output_dir, "*.csv"))
    
    print(f"找到的文件:")
    print(f"XLSX文件: {len(xlsx_files)}")
    for f in xlsx_files:
        print(f"  - {f}")
    print(f"CSV文件: {len(csv_files)}")
    for f in csv_files:
        print(f"  - {f}")
    
    # 检查Sheet1文件
    sheet1_files = [f for f in xlsx_files if 'sheet1' in f]
    if sheet1_files:
        sheet1_file = sheet1_files[0]
        print(f"\n=== 检查Sheet1文件: {sheet1_file} ===")
        try:
            df1 = pd.read_excel(sheet1_file)
            print(f"形状: {df1.shape}")
            print(f"列名: {list(df1.columns)}")
            print("前3行数据:")
            print(df1.head(3).to_string())
        except Exception as e:
            print(f"读取Sheet1文件失败: {e}")
    
    # 检查Sheet2文件
    sheet2_files = [f for f in xlsx_files if 'sheet2' in f]
    if sheet2_files:
        sheet2_file = sheet2_files[0]
        print(f"\n=== 检查Sheet2文件: {sheet2_file} ===")
        try:
            df2 = pd.read_excel(sheet2_file)
            print(f"形状: {df2.shape}")
            print(f"列名: {list(df2.columns)}")
            print("前3行数据:")
            print(df2.head(3).to_string())
        except Exception as e:
            print(f"读取Sheet2文件失败: {e}")

if __name__ == "__main__":
    verify_merged_files()
