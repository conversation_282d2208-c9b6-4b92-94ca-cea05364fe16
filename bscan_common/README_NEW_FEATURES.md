# bscan_common 新功能说明

## 概述

本次重构为 bscan_common 项目新增了根据 Application 列匹配特定漏洞类型的功能，支持从 finger.csv 配置文件中动态加载漏洞信息。

## 新增功能

### 1. 特定漏洞类型匹配

现在系统支持根据 Excel 报告中的 Application 列匹配特定的漏洞类型：

**Sheet1 匹配的漏洞类型（不区分大小写）：**
- Apache-Flink
- Consul  
- cos-public-read

**Sheet2 匹配的漏洞类型（不区分大小写）：**
- presto
- solr-admin
- hadoop-unauthorized-yarn

### 2. 动态配置加载

系统会从 `bscan_common/config/finger.csv` 文件中加载漏洞配置信息，包括：
- `leak_type`: 漏洞类型
- `risk_name`: 风险名称
- `risk_desc`: 风险描述
- `risk_tag`: 风险标签
- `risk_type`: 风险类型ID
- `guide_link`: 修复指引链接

### 3. 新的数据模型

新增了 `SpecificVulnerability` 数据模型，支持：
- 动态配置漏洞信息
- 根据 Application 名称匹配配置
- 生成完整的数据库插入元组

## 文件结构变更

### 新增文件
- `bscan_common/test_new_functionality.py` - 功能测试脚本
- `bscan_common/README_NEW_FEATURES.md` - 本文档

### 修改的文件
- `bscan_common/core/utils.py` - 新增 `load_finger_config()` 函数
- `bscan_common/database/models.py` - 新增 `SpecificVulnerability` 类
- `bscan_common/core/scanner.py` - 新增特定漏洞解析功能
- `bscan_common/main.py` - 更新主流程以支持新功能

## 使用方法

### 1. 配置 finger.csv

确保 `bscan_common/config/finger.csv` 文件包含所需的漏洞配置：

```csv
Keyword,leak_type,risk_name,risk_desc,risk_tag,risk_type,guide_link
Apache-Flink,内网web漏洞,Apache Flink 代码执行漏洞,"高危：...",远程代码执行,7,https://iwiki.woa.com/p/4008500209
Consul,内网web漏洞,Consul 未鉴权,"高危：...",敏感数据泄露,6,https://iwiki.woa.com/p/4007424241
...
```

### 2. 运行扫描

正常运行 bscan_common 扫描：

```python
from bscan_common.main import run_initial_scan

# 执行扫描，系统会自动：
# 1. 处理常规端口扫描漏洞
# 2. 从 Sheet1 中匹配特定漏洞类型
# 3. 从 Sheet2 中匹配特定漏洞类型
# 4. 根据 finger.csv 配置设置漏洞信息
# 5. 将所有漏洞写入数据库
run_initial_scan()
```

### 3. 测试功能

运行测试脚本验证功能：

```bash
python3 bscan_common/test_new_functionality.py
```

## 工作流程

1. **扫描执行**: bscan 工具执行扫描并生成 Excel 报告
2. **文件合并**: 系统将分片结果合并为 Sheet1 和 Sheet2 文件
3. **配置加载**: 从 finger.csv 加载漏洞配置信息
4. **漏洞解析**: 
   - 解析常规端口扫描漏洞（原有功能）
   - 从 Sheet1 解析特定漏洞类型
   - 从 Sheet2 解析特定漏洞类型
5. **信息匹配**: 根据 Application 列匹配对应的漏洞配置
6. **数据库写入**: 将所有漏洞信息写入数据库

## 数据库字段映射

特定漏洞类型的数据库字段会根据 finger.csv 配置动态设置：

- `leak_type` → 从配置文件读取
- `risk_name` → 从配置文件读取  
- `risk_desc` → 从配置文件读取
- `risk_tag` → 从配置文件读取
- `risk_type` → 从配置文件读取（转换为整数）
- `guide_link` → 从配置文件读取

## 兼容性

- 保持与现有功能的完全兼容
- 原有的常规端口扫描功能不受影响
- 如果没有匹配的特定漏洞类型，系统会正常处理常规漏洞

## 注意事项

1. **不区分大小写匹配**: Application 列的匹配不区分大小写
2. **部分匹配**: 支持部分字符串匹配（如 "Apache-Flink Dashboard" 会匹配 "apache-flink"）
3. **配置文件格式**: finger.csv 必须包含所有必需的列
4. **错误处理**: 如果配置文件不存在或格式错误，系统会输出警告但继续运行

## 测试结果示例

```
=== 测试finger.csv配置加载 ===
信息: 成功加载 6 个漏洞配置项。
加载的漏洞类型: cos-public-read, consul, apache-flink, hadoop-unauthorized-yarn, presto, solr-admin

=== 测试SpecificVulnerability模型 ===
测试漏洞: Apache-Flink
  - Host: *************:8081
  - Risk Name: Apache Flink 代码执行漏洞
  - Risk Type: 7
  - Risk Tag: 远程代码执行

=== 测试漏洞解析功能 ===
Sheet1解析结果: 3 个漏洞
  - *************:8081 (Apache-Flink Dashboard)
  - *************:8500 (Consul Web UI)
  - *************:80 (cos-public-read bucket)

Sheet2解析结果: 3 个漏洞
  - *************:8080 (Presto Query Engine)
  - *************:8983 (solr-admin interface)
  - *************:8088 (hadoop-unauthorized-yarn cluster)
```

## 总结

本次重构成功实现了：
1. ✅ 根据 Application 列匹配特定漏洞类型
2. ✅ 从 finger.csv 动态加载漏洞配置
3. ✅ 支持 Sheet1 和 Sheet2 的分别处理
4. ✅ 保持与现有功能的兼容性
5. ✅ 完整的测试覆盖

系统现在能够智能识别和处理 6 种特定的漏洞类型，并根据配置文件自动设置相应的漏洞信息，大大提高了漏洞管理的灵活性和准确性。
