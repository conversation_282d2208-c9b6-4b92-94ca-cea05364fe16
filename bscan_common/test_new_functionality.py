#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的漏洞类型匹配功能
"""

import os
import sys
import pandas as pd

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from bscan_common.core.utils import load_finger_config
from bscan_common.core.scanner import parse_specific_vulnerabilities_from_sheet
from bscan_common.database.models import SpecificVulnerability

def test_finger_config_loading():
    """测试finger.csv配置加载"""
    print("=== 测试finger.csv配置加载 ===")
    
    finger_csv_path = os.path.join(os.path.dirname(__file__), 'config', 'finger.csv')
    finger_config = load_finger_config(finger_csv_path)
    
    print(f"加载的配置数量: {len(finger_config)}")
    
    # 测试特定的配置项
    test_keywords = ['apache-flink', 'consul', 'cos-public-read', 'presto', 'solr-admin', 'hadoop-unauthorized-yarn']
    
    for keyword in test_keywords:
        if keyword in finger_config:
            config = finger_config[keyword]
            print(f"\n{keyword}:")
            print(f"  - leak_type: {config['leak_type']}")
            print(f"  - risk_name: {config['risk_name']}")
            print(f"  - risk_tag: {config['risk_tag']}")
            print(f"  - risk_type: {config['risk_type']}")
            print(f"  - guide_link: {config['guide_link']}")
        else:
            print(f"\n警告: 未找到 {keyword} 的配置")

def test_specific_vulnerability_model():
    """测试SpecificVulnerability模型"""
    print("\n=== 测试SpecificVulnerability模型 ===")
    
    # 加载配置
    finger_csv_path = os.path.join(os.path.dirname(__file__), 'config', 'finger.csv')
    finger_config = load_finger_config(finger_csv_path)
    
    # 创建测试漏洞
    test_vulns = [
        {'host': '*************', 'port': 8081, 'url': 'http://*************:8081', 'application': 'Apache-Flink'},
        {'host': '*************', 'port': 8500, 'url': 'http://*************:8500', 'application': 'Consul'},
        {'host': '*************', 'port': 80, 'url': 'http://*************', 'application': 'cos-public-read'},
        {'host': '*************', 'port': 8080, 'url': 'http://*************:8080', 'application': 'presto'},
        {'host': '*************', 'port': 8983, 'url': 'http://*************:8983', 'application': 'solr-admin'},
        {'host': '*************', 'port': 8088, 'url': 'http://*************:8088', 'application': 'hadoop-unauthorized-yarn'},
    ]
    
    for vuln_data in test_vulns:
        print(f"\n测试漏洞: {vuln_data['application']}")
        
        vuln = SpecificVulnerability(
            host=vuln_data['host'],
            port=vuln_data['port'],
            url=vuln_data['url'],
            application=vuln_data['application']
        )
        
        # 配置漏洞信息
        vuln.configure_from_finger(finger_config)
        
        print(f"  - Host: {vuln.host}:{vuln.port}")
        print(f"  - Application: {vuln.application}")
        print(f"  - Risk Name: {vuln.risk_name}")
        print(f"  - Risk Type: {vuln.risk_type}")
        print(f"  - Risk Tag: {vuln.risk_tag}")
        print(f"  - Leak Type: {vuln.leak_type}")
        print(f"  - Guide Link: {vuln.guide_link}")
        
        # 测试数据库元组生成
        owner_info = {
            'user_name': 'test_user',
            'bg': 'test_bg',
            'dept': 'test_dept',
            'center': 'test_center',
            'oa_group': 'test_group'
        }
        
        db_tuple = vuln.to_db_tuple(owner_info)
        print(f"  - DB Tuple Length: {len(db_tuple)}")

def create_test_excel_files():
    """创建测试用的Excel文件"""
    print("\n=== 创建测试Excel文件 ===")
    
    # 创建测试数据
    sheet1_data = {
        'URL': [
            'http://*************:8081',
            'http://*************:8500',
            'http://*************:80',
            'http://192.168.1.200:80'  # 不匹配的数据
        ],
        'Application': [
            'Apache-Flink Dashboard',
            'Consul Web UI',
            'cos-public-read bucket',
            'nginx'
        ],
        'Status': ['Active', 'Active', 'Active', 'Active']
    }
    
    sheet2_data = {
        'URL': [
            'http://*************:8080',
            'http://*************:8983',
            'http://*************:8088',
            'http://192.168.1.201:3306'  # 不匹配的数据
        ],
        'Application': [
            'Presto Query Engine',
            'solr-admin interface',
            'hadoop-unauthorized-yarn cluster',
            'mysql'
        ],
        'Status': ['Active', 'Active', 'Active', 'Active']
    }
    
    # 创建测试目录
    test_dir = os.path.join(os.path.dirname(__file__), 'test_data')
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建Sheet1测试文件
    sheet1_df = pd.DataFrame(sheet1_data)
    sheet1_path = os.path.join(test_dir, 'test_sheet1.xlsx')
    sheet1_df.to_excel(sheet1_path, sheet_name='Sheet1', index=False)
    print(f"创建Sheet1测试文件: {sheet1_path}")
    
    # 创建Sheet2测试文件
    sheet2_df = pd.DataFrame(sheet2_data)
    sheet2_path = os.path.join(test_dir, 'test_sheet2.xlsx')
    sheet2_df.to_excel(sheet2_path, sheet_name='Sheet2', index=False)
    print(f"创建Sheet2测试文件: {sheet2_path}")
    
    return sheet1_path, sheet2_path

def test_vulnerability_parsing():
    """测试漏洞解析功能"""
    print("\n=== 测试漏洞解析功能 ===")
    
    # 创建测试文件
    sheet1_path, sheet2_path = create_test_excel_files()
    
    # 测试Sheet1解析
    sheet1_targets = ['apache-flink', 'consul', 'cos-public-read']
    sheet1_vulns = parse_specific_vulnerabilities_from_sheet(sheet1_path, 'Sheet1', sheet1_targets)
    
    print(f"\nSheet1解析结果: {len(sheet1_vulns)} 个漏洞")
    for vuln in sheet1_vulns:
        print(f"  - {vuln['host']}:{vuln['port']} ({vuln['application']})")
    
    # 测试Sheet2解析
    sheet2_targets = ['presto', 'solr-admin', 'hadoop-unauthorized-yarn']
    sheet2_vulns = parse_specific_vulnerabilities_from_sheet(sheet2_path, 'Sheet2', sheet2_targets)
    
    print(f"\nSheet2解析结果: {len(sheet2_vulns)} 个漏洞")
    for vuln in sheet2_vulns:
        print(f"  - {vuln['host']}:{vuln['port']} ({vuln['application']})")

def main():
    """主测试函数"""
    print("开始测试新的漏洞类型匹配功能...\n")
    
    try:
        test_finger_config_loading()
        test_specific_vulnerability_model()
        test_vulnerability_parsing()
        
        print("\n=== 所有测试完成 ===")
        print("如果没有错误信息，说明新功能实现正常！")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
