#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from typing import Set
from datetime import datetime

from .config import settings
from .core import scanner, utils
from .database.db_handler import DatabaseHandler

def run_initial_scan():
    """
    任务一: 执行原始扫描，并将结果写入数据库。
    (支持分片)
    """
    print("\n" + "="*20 + " 任务一: 开始执行常规端口原始扫描 " + "="*20)
    
    # 1. 准备工作
    raw_targets_path = os.path.abspath(settings.RAW_TARGETS_FILE)
    if not os.path.exists(raw_targets_path):
        print(f"致命错误: 原始扫描目标文件未找到: {raw_targets_path}")
        return

    bscan_executable_path = os.path.abspath(os.path.join(settings.BSCAN_DIR, settings.BSCAN_EXECUTABLE))

    # 2. 对大文件进行分片
    print("信息: 正在将大型IP列表文件分片...")
    chunk_files = utils.split_file_into_chunks(
        filepath=raw_targets_path,
        chunk_size=settings.IP_CHUNK_SIZE,
        temp_dir=settings.TEMP_CHUNK_DIR
    )
    if not chunk_files:
        print("错误: IP文件分片失败，中止原始扫描。")
        return

    # 3. 准备分片结果的临时存储目录
    if os.path.exists(settings.CHUNK_LOG_DIR):
        utils.cleanup_temp_chunks(settings.CHUNK_LOG_DIR)
    os.makedirs(settings.CHUNK_LOG_DIR, exist_ok=True)
    
    # 4. 遍历分片进行扫描
    total_chunks = len(chunk_files)
    for i, chunk_file in enumerate(chunk_files):
        print(f"\n--- 正在处理分片 {i + 1}/{total_chunks}: {os.path.basename(chunk_file)} ---")
        
        chunk_output_dir = os.path.join(settings.CHUNK_LOG_DIR, f"chunk_{i+1}_output")
        os.makedirs(chunk_output_dir, exist_ok=True)

        scan_success = scanner.run_bscan(
            target_file_path=chunk_file,
            ports=settings.BSCAN_PORTS,
            bscan_executable=bscan_executable_path,
            output_dir=chunk_output_dir
        )

        if not scan_success:
            print(f"警告: 分片 {os.path.basename(chunk_file)} 扫描失败，跳过此分片。")

    # 5. 合并所有分片的XLSX结果 - 分别合并Sheet1和Sheet2
    chunk_xlsx_files = []
    for i in range(total_chunks):
        chunk_output_dir = os.path.join(settings.CHUNK_LOG_DIR, f"chunk_{i+1}_output")
        latest_report = scanner.find_latest_bscan_report(chunk_output_dir)
        if latest_report:
            chunk_xlsx_files.append(latest_report)

    if not chunk_xlsx_files:
        print("错误: 所有分片扫描均未产生有效的结果报告，任务中止。")
        utils.cleanup_temp_chunks(settings.TEMP_CHUNK_DIR)
        # 注释掉清除chunk_log目录的逻辑，保留测试数据
        # utils.cleanup_temp_chunks(settings.CHUNK_LOG_DIR)
        return

    # 使用新的分别合并函数
    base_filename = f"bscan_merged_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    sheet1_path, sheet2_path = utils.merge_xlsx_sheets_separately(
        chunk_xlsx_files,
        settings.BSCAN_ORIGIN_LOG_DIR,
        base_filename
    )

    # 6. 处理合并后的最终报告
    scan_stats = {"found_vulns": 0, "uploaded_to_db": 0}
    if sheet1_path or sheet2_path:
        print("\n信息: 开始处理合并后的最终扫描报告...")
        # 使用sheet1作为主报告路径，系统会自动查找sheet1和sheet2文件
        main_report_path = sheet1_path if sheet1_path else sheet2_path
        scan_stats = process_latest_initial_report(specific_report_path=main_report_path)
    else:
        print("\n错误: 最终合并报告未生成，跳过数据库入库步骤。")

    # 7. 清理临时文件和目录
    utils.cleanup_temp_chunks(settings.TEMP_CHUNK_DIR)
    # 注释掉清除chunk_log目录的逻辑，保留测试数据
    # utils.cleanup_temp_chunks(settings.CHUNK_LOG_DIR)

    print("\n" + "="*20 + " 任务一: 常规端口原始扫描完成 " + "="*20)


def run_audit_scan():
    """
    任务二: 执行复测扫描，并更新漏洞状态。
    """
    print("\n" + "="*20 + " 任务二: 开始执行常规端口复测扫描 " + "="*20)

    # 1. 准备复测目标
    db_handler = DatabaseHandler(settings.DB_CONFIG)
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_COMMON
    )
    if not targets_to_retest:
        print("信息: 数据库中没有需要复测的常规端口扫描活跃漏洞，任务结束。")
        return

    audit_targets_path = os.path.abspath(settings.AUDIT_TARGETS_FILE)
    with open(audit_targets_path, 'w') as f:
        for item in targets_to_retest:
            f.write(f"{item['host']}\n")
    print(f"信息: 已将 {len(targets_to_retest)} 个目标写入复测文件: {audit_targets_path}")
    
    # 2. 执行bscan进行复测
    bscan_executable_path = os.path.abspath(os.path.join(settings.BSCAN_DIR, settings.BSCAN_EXECUTABLE))
    
    scan_success = scanner.run_bscan(
        target_file_path=audit_targets_path,
        ports=settings.BSCAN_PORTS,
        bscan_executable=bscan_executable_path,
        output_dir=settings.BSCAN_AUDIT_LOG_DIR
    )
    
    os.remove(audit_targets_path)
    print(f"信息: 已清理临时复测文件: {audit_targets_path}")
    
    if not scan_success:
        print("警告: 复测扫描bscan执行失败，将中止本次复测流程以防数据错误。")
        return

    # 3. 处理复测结果
    process_latest_audit_report()
    
    print("\n" + "="*20 + " 任务二: 常规端口复测扫描完成 " + "="*20)

def process_latest_initial_report(specific_report_path: str = None):
    """
    功能九: 处理指定的或最新的常规端口扫描原始报告，并添加新漏洞。
    系统会自动查找对应的sheet1和sheet2文件以识别特定漏洞类型。
    """
    print("\n" + "="*20 + " 功能九: 处理常规端口扫描原始报告 " + "="*20)

    # 确定要处理的报告文件
    latest_report = specific_report_path
    if not latest_report:
        print("信息: 未指定特定报告，将自动查找最新报告进行处理...")
        latest_report = scanner.find_latest_bscan_report(settings.BSCAN_ORIGIN_LOG_DIR)

    if not latest_report or not os.path.exists(latest_report):
        print("错误: 在原始扫描目录中未找到任何可处理的报告。")
        return {"found_vulns": 0, "uploaded_to_db": 0}

    db_handler = DatabaseHandler(settings.DB_CONFIG)
    ip_owner_info = utils.load_ip_info(settings.IP_INFO_CSV_PATH)

    # 调用修改后的处理函数，系统会自动查找sheet1和sheet2文件
    scan_stats = scanner.process_bscan_report_to_db(
        latest_report,
        db_handler,
        ip_owner_info
    )

    print("\n" + "="*20 + " 功能九: 处理完成 " + "="*20)
    return scan_stats

def process_latest_audit_report(specific_report_path: str = None):
    """
    功能十: 处理指定的或最新的常规端口扫描复测报告，并更新漏洞状态。
    """
    print("\n" + "="*20 + " 功能十: 处理常规端口扫描复测报告 " + "="*20)

    latest_report = specific_report_path
    if not latest_report:
        print("信息: 未指定特定报告，将自动查找最新报告进行处理...")
        latest_report = scanner.find_latest_bscan_report(settings.BSCAN_AUDIT_LOG_DIR)

    if not latest_report or not os.path.exists(latest_report):
        print("错误: 在复测扫描目录中未找到任何可处理的报告。")
        return

    db_handler = DatabaseHandler(settings.DB_CONFIG)
    scanner.process_audit_report_and_update_db(latest_report, db_handler)
    print("\n" + "="*20 + " 功能十: 处理完成 " + "="*20)
