#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import re
import pandas as pd
from typing import List, Set
from urllib.parse import urlparse
from datetime import datetime
from ..config import settings

def run_bscan(target_file_path: str, ports: str, bscan_executable: str, output_dir: str) -> bool:
    """
    执行bscan扫描 (常规端口扫描)，并将报告输出到指定目录。
    """
    print("-" * 50)
    print(f"信息: 正在启动 bscan 扫描... 输出目录: {output_dir}")

    os.makedirs(output_dir, exist_ok=True)

    # 确定bscan的工作目录，通常是其可执行文件所在的目录，以确保能加载配置文件
    bscan_home = os.path.dirname(bscan_executable)
    if not bscan_home:
        bscan_home = '.' # 如果路径为空，则回退到当前目录

    # 将目标文件路径转换为绝对路径，以避免因切换工作目录(cwd)导致路径找不到的问题
    abs_target_file_path = os.path.abspath(target_file_path)

    # 构建命令，包含 exploit 和 poc 参数
    command = [
        bscan_executable,
        "-target", abs_target_file_path,
        "-ports", ports,
        "-exploit",
        "-poc", settings.BSCAN_POC_DIR,
    ]
    print(f"执行命令: {' '.join(command)}")
    print(f"信息: 将在bscan所在目录 '{bscan_home}' 执行命令, 以确保其能正确加载配置文件。")
    
    try:
        subprocess.run(
            command,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=bscan_home  # 核心改动：切换工作目录以加载配置文件
        )
        print("成功: bscan 扫描执行完成。")

        # bscan默认将报告输出到其当前工作目录(CWD)，即 bscan_home。
        # 我们需要找到最新的报告并将其移动到用户指定的 output_dir。
        latest_report = find_latest_bscan_report(bscan_home)
        if latest_report:
            report_filename = os.path.basename(latest_report)
            destination_path = os.path.join(output_dir, report_filename)
            # 如果目标文件已存在，先删除，以确保os.rename成功
            if os.path.exists(destination_path):
                os.remove(destination_path)
            print(f"信息: 移动报告 '{report_filename}' 从 '{bscan_home}' 到 '{output_dir}'")
            os.rename(latest_report, destination_path)
        else:
            print("警告: bscan执行完毕，但在其工作目录中未找到任何 .xlsx 报告文件。")

        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: bscan 执行失败。返回码: {e.returncode}")
        stderr_output = e.stderr.decode('utf-8', errors='ignore')
        print(f"错误输出: {stderr_output}")
        return False
    except FileNotFoundError:
        print(f"致命错误: bscan可执行文件未找到: {command[0]}")
        return False
    except Exception as e:
        print(f"致命错误: 执行bscan时发生未知错误: {e}")
        return False

def find_latest_bscan_report(directory: str) -> str or None:
    """在一个目录下查找最新修改的 .xlsx 文件"""
    print(f"信息: 正在目录 '{directory}' 中查找最新的 .xlsx 报告...")
    try:
        xlsx_files = [f for f in os.listdir(directory) if f.lower().endswith('.xlsx')]
        if not xlsx_files:
            print(f"警告: 在目录 '{directory}' 中没有找到任何 .xlsx 文件。")
            return None
        
        full_paths = [os.path.join(directory, f) for f in xlsx_files]
        latest_file = max(full_paths, key=os.path.getmtime)
        print(f"信息: 已找到最新报告文件: {os.path.basename(latest_file)}")
        return latest_file
    except FileNotFoundError:
        print(f"错误: 报告目录不存在: {directory}")
        return None
    except Exception as e:
        print(f"错误: 查找最新报告时发生未知错误: {e}")
        return None

def parse_common_report(report_path: str) -> Set[str]:
    """
    使用pandas直接读取xlsx报告的'Sheet1'，解析常规端口扫描漏洞。
    """
    live_vulns = set()
    if not os.path.exists(report_path):
        print(f"警告: bscan报告文件不存在: {report_path}，无法解析。")
        return live_vulns

    try:
        df = pd.read_excel(report_path, sheet_name='Sheet1', engine='openpyxl')

        # --- 详细调试信息 ---
        print(f"[DEBUG] pandas读取Sheet1成功。维度(行,列): {df.shape}")
        print(f"[DEBUG] 检测到的列名: {df.columns.tolist()}")
        if not df.empty:
            print(f"[DEBUG] 前5行数据预览:\n{df.head().to_string()}")
        else:
            print("[DEBUG] 数据表为空。")
        # --- 调试结束 ---

        # 动态确定URL列的名称
        url_col = 'URL' if 'URL' in df.columns else 'Target'

        if url_col not in df.columns:
            print(f"信息: 报告 '{os.path.basename(report_path)}' 的Sheet1中缺少必需的列 ('{url_col}')。")
            return live_vulns

        # 对于常规端口扫描，我们解析所有有效的目标
        for index, row in df.iterrows():
            target_str = row.get(url_col)
            if pd.isna(target_str):
                continue

            host_port_str = str(target_str).strip()
            if "://" in host_port_str:
                parsed = urlparse(host_port_str)
                if parsed.hostname and parsed.port:
                    live_vulns.add(f"{parsed.hostname}:{parsed.port}")
            elif ":" in host_port_str:
                parts = host_port_str.split(':')
                if len(parts) == 2 and parts[1].isdigit():
                    live_vulns.add(host_port_str)

    except Exception as e:
        print(f"错误: 使用pandas解析bscan报告时发生意外错误。原因: {e}")

    print(f"信息: 从报告中筛选出 {len(live_vulns)} 条常规端口扫描漏洞。")
    return live_vulns


def parse_specific_vulnerabilities_from_sheet1(report_path: str, target_applications: list) -> list:
    """
    从Sheet1中解析特定Application类型的漏洞。

    :param report_path: Excel报告文件路径
    :param target_applications: 要匹配的Application列表（不区分大小写）
    :return: 包含漏洞信息的字典列表
    """
    vulnerabilities = []
    if not os.path.exists(report_path):
        print(f"警告: Sheet1报告文件不存在: {report_path}，无法解析。")
        return vulnerabilities

    try:
        df = pd.read_excel(report_path, engine='openpyxl')

        print(f"[DEBUG] pandas读取Sheet1成功。维度(行,列): {df.shape}")
        print(f"[DEBUG] 检测到的列名: {df.columns.tolist()}")

        # 检查必需的列
        app_col = 'Application'
        url_col = 'URL' if 'URL' in df.columns else 'Target'

        if app_col not in df.columns or url_col not in df.columns:
            print(f"信息: 报告 '{os.path.basename(report_path)}' 中缺少必需的列 ('{app_col}' 和 '{url_col}')。")
            return vulnerabilities

        # 转换目标应用列表为小写，用于不区分大小写的匹配
        target_apps_lower = [app.lower() for app in target_applications]

        for index, row in df.iterrows():
            app_name = str(row.get(app_col, '')).strip()
            target_str = row.get(url_col)

            if pd.isna(target_str) or not app_name:
                continue

            # 不区分大小写匹配Application
            app_name_lower = app_name.lower()
            matched = False
            for target_app in target_apps_lower:
                if target_app in app_name_lower:
                    matched = True
                    break

            if not matched:
                continue

            # 解析URL获取host和port
            host_port_str = str(target_str).strip()
            host = None
            port = None

            if "://" in host_port_str:
                parsed = urlparse(host_port_str)
                if parsed.hostname and parsed.port:
                    host = parsed.hostname
                    port = parsed.port
            elif ":" in host_port_str:
                parts = host_port_str.split(':')
                if len(parts) == 2 and parts[1].isdigit():
                    host = parts[0]
                    port = int(parts[1])

            if host and port:
                vulnerabilities.append({
                    'host': host,
                    'port': port,
                    'url': host_port_str if "://" in host_port_str else f"http://{host_port_str}",
                    'application': app_name
                })

    except Exception as e:
        print(f"错误: 使用pandas解析Sheet1时发生意外错误。原因: {e}")

    print(f"信息: 从Sheet1中筛选出 {len(vulnerabilities)} 条特定类型漏洞。")
    return vulnerabilities


def parse_specific_vulnerabilities_from_sheet2(report_path: str, target_applications: list) -> list:
    """
    从Sheet2中解析特定Name类型的漏洞。
    Sheet2包含字段: url, status, length, name

    :param report_path: Excel报告文件路径
    :param target_applications: 要匹配的Name列表（不区分大小写）
    :return: 包含漏洞信息的字典列表
    """
    vulnerabilities = []
    if not os.path.exists(report_path):
        print(f"警告: Sheet2报告文件不存在: {report_path}，无法解析。")
        return vulnerabilities

    try:
        df = pd.read_excel(report_path, engine='openpyxl')

        print(f"[DEBUG] pandas读取Sheet2成功。维度(行,列): {df.shape}")
        print(f"[DEBUG] 检测到的列名: {df.columns.tolist()}")

        # 检查必需的列
        name_col = 'name'
        url_col = 'url'

        if name_col not in df.columns or url_col not in df.columns:
            print(f"信息: 报告 '{os.path.basename(report_path)}' 中缺少必需的列 ('{name_col}' 和 '{url_col}')。")
            return vulnerabilities

        # 转换目标应用列表为小写，用于不区分大小写的匹配
        target_apps_lower = [app.lower() for app in target_applications]

        for index, row in df.iterrows():
            name_value = str(row.get(name_col, '')).strip()
            url_value = row.get(url_col)

            if pd.isna(url_value) or not name_value:
                continue

            # 不区分大小写匹配Name字段
            name_lower = name_value.lower()
            matched = False
            for target_app in target_apps_lower:
                if target_app in name_lower:
                    matched = True
                    break

            if not matched:
                continue

            # 从URL中提取IP和端口
            url_str = str(url_value).strip()
            host = None
            port = None

            if "://" in url_str:
                parsed = urlparse(url_str)
                if parsed.hostname:
                    host = parsed.hostname
                    port = parsed.port if parsed.port else 80
            else:
                # 如果没有协议，尝试直接解析
                if ":" in url_str:
                    parts = url_str.split(':')
                    if len(parts) >= 2:
                        host = parts[0]
                        # 提取端口号，可能包含路径
                        port_part = parts[1].split('/')[0]
                        if port_part.isdigit():
                            port = int(port_part)

            if host and port:
                vulnerabilities.append({
                    'host': host,
                    'port': port,
                    'url': url_str,
                    'application': name_value
                })

    except Exception as e:
        print(f"错误: 使用pandas解析Sheet2时发生意外错误。原因: {e}")

    print(f"信息: 从Sheet2中筛选出 {len(vulnerabilities)} 条特定类型漏洞。")
    return vulnerabilities

def find_latest_sheet_files(base_dir: str) -> tuple:
    """
    在指定目录中查找最新的sheet1和sheet2文件。
    文件名格式: bscan_merged_result_{datetime}_sheet1.xlsx 和 bscan_merged_result_{datetime}_sheet2.xlsx

    :param base_dir: 搜索目录
    :return: (sheet1_path, sheet2_path) 元组，如果找不到则为None
    """
    sheet1_path = None
    sheet2_path = None

    try:
        if not os.path.exists(base_dir):
            print(f"警告: 目录不存在: {base_dir}")
            return sheet1_path, sheet2_path

        # 查找所有匹配的文件
        files = os.listdir(base_dir)
        sheet1_files = [f for f in files if f.startswith('bscan_merged_result_') and f.endswith('_sheet1.xlsx')]
        sheet2_files = [f for f in files if f.startswith('bscan_merged_result_') and f.endswith('_sheet2.xlsx')]

        # 找到最新的文件
        if sheet1_files:
            latest_sheet1 = max(sheet1_files, key=lambda f: os.path.getmtime(os.path.join(base_dir, f)))
            sheet1_path = os.path.join(base_dir, latest_sheet1)
            print(f"信息: 找到Sheet1文件: {latest_sheet1}")

        if sheet2_files:
            latest_sheet2 = max(sheet2_files, key=lambda f: os.path.getmtime(os.path.join(base_dir, f)))
            sheet2_path = os.path.join(base_dir, latest_sheet2)
            print(f"信息: 找到Sheet2文件: {latest_sheet2}")

    except Exception as e:
        print(f"错误: 查找sheet文件时发生错误: {e}")

    return sheet1_path, sheet2_path


def process_bscan_report_to_db(report_path: str, db_handler, ip_owner_info: dict, sheet1_path: str = None, sheet2_path: str = None):
    """
    解析指定的bscan报告，筛选出常规端口扫描漏洞和特定类型漏洞，并将结果写入数据库。

    :param report_path: bscan生成的xlsx报告的路径（用于常规端口扫描）。
    :param db_handler: 已经实例化的DatabaseHandler。
    :param ip_owner_info: IP与负责人信息的映射字典。
    :param sheet1_path: Sheet1文件路径（可选，用于特定漏洞类型）
    :param sheet2_path: Sheet2文件路径（可选，用于特定漏洞类型）
    """
    from ..database.models import Vulnerability, SpecificVulnerability  # 延迟导入以避免循环依赖
    from ..core.utils import load_finger_config
    from ..config import settings

    print(f"\n--- 开始处理报告文件: {os.path.basename(report_path)} ---")

    # 如果没有提供sheet1和sheet2路径，自动查找
    if not sheet1_path or not sheet2_path:
        print("信息: 自动查找最新的sheet1和sheet2文件...")
        auto_sheet1, auto_sheet2 = find_latest_sheet_files(settings.BSCAN_ORIGIN_LOG_DIR)
        sheet1_path = sheet1_path or auto_sheet1
        sheet2_path = sheet2_path or auto_sheet2

    # 加载finger.csv配置
    finger_csv_path = os.path.join(settings.BASE_DIR, 'config', 'finger.csv')
    finger_config = load_finger_config(finger_csv_path)

    total_found_vulns = 0
    total_uploaded_to_db = 0

    # 1. 处理常规端口扫描漏洞（原有逻辑）
    live_vulns_set = parse_common_report(report_path)
    if live_vulns_set:
        print(f"信息: 从报告中解析出 {len(live_vulns_set)} 个常规端口扫描漏洞，正在写入数据库...")
        for host_port in live_vulns_set:
            try:
                host, port_str = host_port.split(":")
                port = int(port_str)
                # 构造一个URL用于存储
                url = f"http://{host}:{port}"
                vuln = Vulnerability(host=host, port=port, url=url)
                owner = ip_owner_info.get(host, {})
                db_handler.add_vulnerability(vuln, owner)
                total_uploaded_to_db += 1
            except Exception as e:
                print(f"错误: 处理漏洞 {host_port} 并存入数据库时失败: {e}")
        total_found_vulns += len(live_vulns_set)

    # 2. 处理Sheet1中的特定漏洞类型
    sheet1_targets = ['apache-flink', 'consul', 'cos-public-read']

    if sheet1_path and os.path.exists(sheet1_path):
        sheet1_vulns = parse_specific_vulnerabilities_from_sheet1(sheet1_path, sheet1_targets)
        if sheet1_vulns:
            print(f"信息: 从Sheet1中解析出 {len(sheet1_vulns)} 个特定类型漏洞，正在写入数据库...")
            for vuln_data in sheet1_vulns:
                try:
                    vuln = SpecificVulnerability(
                        host=vuln_data['host'],
                        port=vuln_data['port'],
                        url=vuln_data['url'],
                        application=vuln_data['application']
                    )
                    vuln.configure_from_finger(finger_config)
                    owner = ip_owner_info.get(vuln_data['host'], {})
                    db_handler.add_vulnerability(vuln, owner)
                    total_uploaded_to_db += 1
                except Exception as e:
                    print(f"错误: 处理Sheet1漏洞 {vuln_data} 并存入数据库时失败: {e}")
            total_found_vulns += len(sheet1_vulns)
    else:
        print("警告: 未找到Sheet1文件，跳过Sheet1特定漏洞处理。")

    # 3. 处理Sheet2中的特定漏洞类型
    sheet2_targets = ['presto', 'solr-admin', 'hadoop-unauthorized-yarn']

    if sheet2_path and os.path.exists(sheet2_path):
        sheet2_vulns = parse_specific_vulnerabilities_from_sheet2(sheet2_path, sheet2_targets)
        if sheet2_vulns:
            print(f"信息: 从Sheet2中解析出 {len(sheet2_vulns)} 个特定类型漏洞，正在写入数据库...")
            for vuln_data in sheet2_vulns:
                try:
                    vuln = SpecificVulnerability(
                        host=vuln_data['host'],
                        port=vuln_data['port'],
                        url=vuln_data['url'],
                        application=vuln_data['application']
                    )
                    vuln.configure_from_finger(finger_config)
                    owner = ip_owner_info.get(vuln_data['host'], {})
                    db_handler.add_vulnerability(vuln, owner)
                    total_uploaded_to_db += 1
                except Exception as e:
                    print(f"错误: 处理Sheet2漏洞 {vuln_data} 并存入数据库时失败: {e}")
            total_found_vulns += len(sheet2_vulns)
    else:
        print("警告: 未找到Sheet2文件，跳过Sheet2特定漏洞处理。")

    print(f"--- 报告文件 {os.path.basename(report_path)} 处理完毕 ---")
    print(f"总计发现漏洞: {total_found_vulns}, 成功上传到数据库: {total_uploaded_to_db}")

    return {"found_vulns": total_found_vulns, "uploaded_to_db": total_uploaded_to_db}


def process_audit_report_and_update_db(report_path: str, db_handler):
    """
    解析指定的复测报告，与数据库中的常规端口扫描活跃漏洞比对，并更新已修复的漏洞状态。

    :param report_path: bscan生成的xlsx报告的路径。
    :param db_handler: 已经实例化的DatabaseHandler。
    """
    from ..config import settings  # 延迟导入
    print(f"\n--- 开始处理常规端口扫描复测报告文件: {os.path.basename(report_path)} ---")

    # 1. 从数据库拉取当前所有活跃漏洞，作为复测的基准
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_COMMON
    )
    if not targets_to_retest:
        print("信息: 数据库中没有需要复测的常规端口扫描活跃漏洞，处理中止。")
        return

    initial_targets_set = {f"{item['host']}:{item['port']}" for item in targets_to_retest}
    print(f"信息: 从数据库中加载了 {len(initial_targets_set)} 个常规端口扫描活跃漏洞作为比对基准。")

    # 2. 从报告中解析出本次扫描仍然存活的漏洞
    live_vulns_in_audit = parse_common_report(report_path)
    print(f"信息: 复测报告中发现 {len(live_vulns_in_audit)} 个仍然存活的常规端口扫描漏洞。")

    # 3. 计算已修复的漏洞（在基准中，但本次未存活）并更新数据库
    fixed_vulns_set = initial_targets_set - live_vulns_in_audit
    print(f"信息: 计算得出 {len(fixed_vulns_set)} 个常规端口扫描漏洞已被修复。")

    if fixed_vulns_set:
        db_handler.batch_update_status_to_fixed(
            fixed_host_ports=list(fixed_vulns_set),
            risk_type=settings.RISK_TYPE_COMMON
        )

    print(f"--- 常规端口扫描复测报告文件 {os.path.basename(report_path)} 处理完毕 ---")