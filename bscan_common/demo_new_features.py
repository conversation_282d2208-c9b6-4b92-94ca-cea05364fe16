#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示新的漏洞类型匹配功能
"""

import os
import sys
import pandas as pd

# 将项目根目录添加到Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from bscan_common.core.utils import load_finger_config
from bscan_common.core.scanner import parse_specific_vulnerabilities_from_sheet
from bscan_common.database.models import SpecificVulnerability

def create_demo_excel_file():
    """创建演示用的Excel文件，包含Sheet1和Sheet2"""
    print("=== 创建演示Excel文件 ===")
    
    # Sheet1数据 - 包含Apache-Flink, Consul, cos-public-read
    sheet1_data = {
        'URL': [
            'http://**********:8081',
            'http://10.0.1.101:8500', 
            'http://10.0.1.102:80',
            'http://10.0.1.103:9200',  # 其他类型，不会被匹配
            'http://10.0.1.104:8081'   # 另一个Flink实例
        ],
        'Application': [
            'Apache-Flink Web Dashboard',
            'Consul Agent UI',
            'cos-public-read Storage',
            'Elasticsearch',
            'Apache Flink Dashboard v1.14'
        ],
        'Status': ['Active', 'Active', 'Active', 'Active', 'Active'],
        'Port': [8081, 8500, 80, 9200, 8081]
    }
    
    # Sheet2数据 - 包含presto, solr-admin, hadoop-unauthorized-yarn
    sheet2_data = {
        'URL': [
            'http://10.0.2.100:8080',
            'http://10.0.2.101:8983',
            'http://10.0.2.102:8088',
            'http://10.0.2.103:3306',  # 其他类型，不会被匹配
            'http://10.0.2.104:8090'   # 另一个Hadoop实例
        ],
        'Application': [
            'Presto Query Engine v0.245',
            'Apache Solr-admin Console',
            'Hadoop YARN ResourceManager (hadoop-unauthorized-yarn)',
            'MySQL Database',
            'Hadoop Yarn API (hadoop-unauthorized-yarn cluster)'
        ],
        'Status': ['Active', 'Active', 'Active', 'Active', 'Active'],
        'Port': [8080, 8983, 8088, 3306, 8090]
    }
    
    # 创建演示目录
    demo_dir = os.path.join(os.path.dirname(__file__), 'demo_data')
    os.makedirs(demo_dir, exist_ok=True)
    
    # 创建包含两个sheet的Excel文件
    demo_file_path = os.path.join(demo_dir, 'demo_scan_results.xlsx')
    
    with pd.ExcelWriter(demo_file_path, engine='openpyxl') as writer:
        pd.DataFrame(sheet1_data).to_excel(writer, sheet_name='Sheet1', index=False)
        pd.DataFrame(sheet2_data).to_excel(writer, sheet_name='Sheet2', index=False)
    
    print(f"创建演示文件: {demo_file_path}")
    print(f"  - Sheet1: {len(sheet1_data['URL'])} 条记录")
    print(f"  - Sheet2: {len(sheet2_data['URL'])} 条记录")
    
    return demo_file_path

def demo_vulnerability_matching():
    """演示漏洞匹配功能"""
    print("\n=== 演示漏洞匹配功能 ===")
    
    # 创建演示文件
    demo_file = create_demo_excel_file()
    
    # 加载finger.csv配置
    finger_csv_path = os.path.join(os.path.dirname(__file__), 'config', 'finger.csv')
    finger_config = load_finger_config(finger_csv_path)
    
    print(f"\n已加载 {len(finger_config)} 个漏洞配置")
    
    # 演示Sheet1匹配
    print("\n--- Sheet1 漏洞匹配 ---")
    sheet1_targets = ['apache-flink', 'consul', 'cos-public-read']
    sheet1_vulns = parse_specific_vulnerabilities_from_sheet(demo_file, 'Sheet1', sheet1_targets)
    
    print(f"匹配目标: {sheet1_targets}")
    print(f"匹配结果: {len(sheet1_vulns)} 个漏洞")
    
    for i, vuln_data in enumerate(sheet1_vulns, 1):
        print(f"\n漏洞 {i}:")
        print(f"  URL: {vuln_data['url']}")
        print(f"  Application: {vuln_data['application']}")
        
        # 创建SpecificVulnerability对象并配置
        vuln = SpecificVulnerability(
            host=vuln_data['host'],
            port=vuln_data['port'],
            url=vuln_data['url'],
            application=vuln_data['application']
        )
        vuln.configure_from_finger(finger_config)
        
        print(f"  配置后的信息:")
        print(f"    - 风险名称: {vuln.risk_name}")
        print(f"    - 风险类型ID: {vuln.risk_type}")
        print(f"    - 风险标签: {vuln.risk_tag}")
        print(f"    - 修复指引: {vuln.guide_link}")
    
    # 演示Sheet2匹配
    print("\n--- Sheet2 漏洞匹配 ---")
    sheet2_targets = ['presto', 'solr-admin', 'hadoop-unauthorized-yarn']
    sheet2_vulns = parse_specific_vulnerabilities_from_sheet(demo_file, 'Sheet2', sheet2_targets)
    
    print(f"匹配目标: {sheet2_targets}")
    print(f"匹配结果: {len(sheet2_vulns)} 个漏洞")
    
    for i, vuln_data in enumerate(sheet2_vulns, 1):
        print(f"\n漏洞 {i}:")
        print(f"  URL: {vuln_data['url']}")
        print(f"  Application: {vuln_data['application']}")
        
        # 创建SpecificVulnerability对象并配置
        vuln = SpecificVulnerability(
            host=vuln_data['host'],
            port=vuln_data['port'],
            url=vuln_data['url'],
            application=vuln_data['application']
        )
        vuln.configure_from_finger(finger_config)
        
        print(f"  配置后的信息:")
        print(f"    - 风险名称: {vuln.risk_name}")
        print(f"    - 风险类型ID: {vuln.risk_type}")
        print(f"    - 风险标签: {vuln.risk_tag}")
        print(f"    - 修复指引: {vuln.guide_link}")

def demo_database_tuple_generation():
    """演示数据库元组生成"""
    print("\n=== 演示数据库元组生成 ===")
    
    # 加载配置
    finger_csv_path = os.path.join(os.path.dirname(__file__), 'config', 'finger.csv')
    finger_config = load_finger_config(finger_csv_path)
    
    # 创建示例漏洞
    vuln = SpecificVulnerability(
        host='**********',
        port=8081,
        url='http://**********:8081',
        application='Apache-Flink Dashboard'
    )
    vuln.configure_from_finger(finger_config)
    
    # 模拟负责人信息
    owner_info = {
        'user_name': 'zhangsan',
        'bg': '技术工程事业群',
        'dept': '平台与内容事业群',
        'center': '基础架构部',
        'oa_group': 'TEG-PCG-基础架构部'
    }
    
    # 生成数据库元组
    db_tuple = vuln.to_db_tuple(owner_info)
    
    print(f"漏洞信息: {vuln.application} ({vuln.host}:{vuln.port})")
    print(f"数据库元组长度: {len(db_tuple)}")
    print("\n数据库字段映射:")
    
    field_names = [
        'date_key', 'leak_type', 'url', 'host', 'port',
        'user_name', 'bg', 'dept', 'center', 'risk_name',
        'risk_desc', 'risk_tag', 'risk_level', 'risk_status',
        'oa_group', 'create_by', 'case_content', 'create_time',
        'risk_type', 'guide_link'
    ]
    
    for i, (field_name, value) in enumerate(zip(field_names, db_tuple)):
        if field_name == 'risk_desc':
            # 风险描述可能很长，只显示前100个字符
            display_value = str(value)[:100] + '...' if len(str(value)) > 100 else value
        else:
            display_value = value
        print(f"  {i+1:2d}. {field_name:15s}: {display_value}")

def main():
    """主演示函数"""
    print("bscan_common 新功能演示")
    print("=" * 50)
    
    try:
        demo_vulnerability_matching()
        demo_database_tuple_generation()
        
        print("\n" + "=" * 50)
        print("演示完成！")
        print("\n主要功能:")
        print("1. ✅ 根据Application列匹配特定漏洞类型")
        print("2. ✅ 从finger.csv动态加载漏洞配置")
        print("3. ✅ 支持Sheet1和Sheet2分别处理")
        print("4. ✅ 生成完整的数据库插入元组")
        print("5. ✅ 不区分大小写的模糊匹配")
        
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
