# database/models.py
from dataclasses import dataclass, field
from datetime import datetime
from ..config import settings

@dataclass
class Vulnerability:
    """用于表示一个常规端口扫描漏洞的数据模型。"""
    host: str
    port: int
    url: str

    # --- 从 settings.py 加载的固定信息 ---
    risk_type: int = field(default=settings.COMMON_VULN_DETAILS['risk_id'], init=False)
    risk_name: str = field(default=settings.COMMON_VULN_DETAILS['risk_type'], init=False)
    risk_level: str = field(default=settings.COMMON_VULN_DETAILS['risk_level'], init=False)
    risk_tag: str = field(default="敏感数据泄露", init=False)
    create_by: str = field(default=settings.VULN_CREATOR, init=False)
    risk_status: str = field(default="2", init=False) # 活跃状态

    # --- 动态生成的字段 ---
    date_key: str = field(init=False)
    create_time: str = field(init=False)
    case_content: str = field(init=False)
    guide_link: str = field(default="", init=False)

    @property
    def risk_desc(self) -> str:
        return ""

    def __post_init__(self):
        """在对象创建后自动填充动态字段"""
        now = datetime.now()
        self.date_key = now.strftime("%Y%m%d%H")
        self.create_time = now.strftime("%Y-%m-%d %H:%M:%S")
        self.case_content = ""

    def to_db_tuple(self, owner_info: dict) -> tuple:
        """将漏洞信息和负责人信息转换为用于数据库插入的元组 (20个字段)。"""
        return (
            self.date_key,
            "内网web漏洞",  # leak_type, 可根据需要调整
            self.url,
            self.host,
            self.port,
            owner_info.get("user_name", ""),
            owner_info.get("bg", ""),
            owner_info.get("dept", ""),
            owner_info.get("center", ""),
            self.risk_name,
            self.risk_desc,
            self.risk_tag,
            self.risk_level,
            self.risk_status,
            owner_info.get("oa_group", ""),
            self.create_by,
            self.case_content,
            self.create_time,
            self.risk_type,
            self.guide_link,
        )


@dataclass
class SpecificVulnerability:
    """用于表示根据finger.csv配置的特定漏洞类型的数据模型。"""
    host: str
    port: int
    url: str
    application: str  # 从Excel中的Application列获取

    # --- 从finger.csv动态加载的信息 ---
    leak_type: str = field(default="内网web漏洞", init=False)
    risk_name: str = field(default="", init=False)
    risk_desc: str = field(default="", init=False)
    risk_tag: str = field(default="", init=False)
    risk_type: int = field(default=0, init=False)
    guide_link: str = field(default="", init=False)

    # --- 固定字段 ---
    create_by: str = field(default=settings.VULN_CREATOR, init=False)
    risk_status: str = field(default="2", init=False)  # 活跃状态
    risk_level: str = field(default="1", init=False)   # 默认高危

    # --- 动态生成的字段 ---
    date_key: str = field(init=False)
    create_time: str = field(init=False)
    case_content: str = field(init=False)

    def __post_init__(self):
        """在对象创建后自动填充动态字段"""
        now = datetime.now()
        self.date_key = now.strftime("%Y%m%d%H")
        self.create_time = now.strftime("%Y-%m-%d %H:%M:%S")
        self.case_content = ""

    def configure_from_finger(self, finger_config: dict):
        """根据finger.csv配置更新漏洞信息"""
        app_lower = self.application.lower()

        # 查找匹配的配置
        matched_config = None
        for keyword, config in finger_config.items():
            if keyword in app_lower:
                matched_config = config
                break

        if matched_config:
            self.leak_type = matched_config.get('leak_type', self.leak_type)
            self.risk_name = matched_config.get('risk_name', self.risk_name)
            self.risk_desc = matched_config.get('risk_desc', self.risk_desc)
            self.risk_tag = matched_config.get('risk_tag', self.risk_tag)
            self.guide_link = matched_config.get('guide_link', self.guide_link)

            # 转换risk_type为整数
            try:
                self.risk_type = int(matched_config.get('risk_type', 0))
            except (ValueError, TypeError):
                self.risk_type = 0
        else:
            # 如果没有匹配的配置，使用默认值
            self.risk_name = f"{self.application}漏洞"
            self.risk_desc = f"发现{self.application}服务存在安全风险"
            self.risk_tag = "敏感数据泄露"

    def to_db_tuple(self, owner_info: dict) -> tuple:
        """将漏洞信息和负责人信息转换为用于数据库插入的元组 (20个字段)。"""
        return (
            self.date_key,
            self.leak_type,
            self.url,
            self.host,
            self.port,
            owner_info.get("user_name", ""),
            owner_info.get("bg", ""),
            owner_info.get("dept", ""),
            owner_info.get("center", ""),
            self.risk_name,
            self.risk_desc,
            self.risk_tag,
            self.risk_level,
            self.risk_status,
            owner_info.get("oa_group", ""),
            self.create_by,
            self.case_content,
            self.create_time,
            self.risk_type,
            self.guide_link,
        )
