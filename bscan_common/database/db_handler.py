# database/db_handler.py
import pymysql
from typing import List, Dict, Set
# 确保从当前目录的models导入
from .models import Vulnerability
from ..config import settings

class DatabaseHandler:
    """处理所有与MySQL数据库的交互。"""

    def __init__(self, db_config: dict):
        self.db_config = db_config
        self.connection = None

    def _connect(self):
        if not self.connection or not self.connection.open:
            self.connection = pymysql.connect(**self.db_config)

    def _close(self):
        if self.connection and self.connection.open:
            self.connection.close()

    def add_vulnerability(self, vuln: Vulnerability, owner_info: Dict):
        """
        添加或更新一条漏洞记录。
        - 如果漏洞不存在，则插入新记录。
        - 如果漏洞存在且状态为'已修复'(3)，则更新其状态为'活跃'(2)。
        - 如果漏洞存在且状态为'活跃'(2)，则跳过。
        """
        self._connect()
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查漏洞是否存在，无论其状态如何
                check_sql = f"""
                    SELECT host, risk_status FROM {settings.DB_TABLE_NAME}
                    WHERE host = %s AND port = %s AND risk_type = %s
                    LIMIT 1
                """
                cursor.execute(check_sql, (vuln.host, vuln.port, vuln.risk_type))
                existing_vuln = cursor.fetchone()

                if existing_vuln:
                    # 漏洞已存在
                    if existing_vuln['risk_status'] == '3':
                        # 状态为“已修复”，需要重新激活
                        print(f"信息: {vuln.host}:{vuln.port} 的漏洞已存在但为“已修复”状态，正在重新激活...")
                        update_sql = f"""
                            UPDATE {settings.DB_TABLE_NAME}
                            SET risk_status = '2', date_key = %s, create_time = %s
                            WHERE host = %s AND port = %s AND risk_type = %s
                        """
                        # 使用Vulnerability对象中的时间信息
                        cursor.execute(update_sql, (vuln.date_key, vuln.create_time, vuln.host, vuln.port, vuln.risk_type))
                        self.connection.commit()
                        print(f"成功: 已将 {vuln.host}:{vuln.port} 的漏洞状态更新为“活跃”。")
                    else:
                        # 状态为“活跃”或其他状态，直接跳过
                        print(f"[DEBUG] 信息: {vuln.host}:{vuln.port} 的活跃漏洞(risk_type={vuln.risk_type})已存在，跳过。")
                    return

                # 漏洞不存在，执行插入
                print(f"信息: {vuln.host}:{vuln.port} 是一个新漏洞，正在添加到数据库...")
                insert_sql = f"""
                    INSERT INTO {settings.DB_TABLE_NAME} (
                        date_key, leak_type, url, host, port, user_name, bg, dept, center, risk_name,
                        risk_desc, risk_tag, risk_level, risk_status, oa_group, create_by, case_content, create_time,
                        risk_type, guide_link
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                data_tuple = vuln.to_db_tuple(owner_info)
                cursor.execute(insert_sql, data_tuple)
                self.connection.commit()
                print(f"成功: 已将 {vuln.host}:{vuln.port} 的新漏洞添加到数据库。")

        except pymysql.Error as e:
            print(f"错误: 数据库操作失败 ({vuln.host}:{vuln.port})。原因: {e}")
            if self.connection.open:
                self.connection.rollback()
        finally:
            self._close()


    def fetch_active_vulnerabilities_for_audit(self, risk_type: int) -> List[Dict]:
        """
        根据漏洞类型(risk_type)拉取所有状态为 '2' (活跃) 的漏洞用于复测。
        """
        self._connect()
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = f"SELECT host, port FROM {settings.DB_TABLE_NAME} WHERE risk_status = '2' AND risk_type = %s"
                cursor.execute(sql, (risk_type,))
                results = cursor.fetchall()
                print(f"信息: 从数据库成功拉取 {len(results)} 条 risk_type={risk_type} 的活跃漏洞记录用于复测。")
                return results
        except Exception as e:
            print(f"错误: 拉取待复测数据失败。原因: {e}")
            return []
        finally:
            self._close()

    def batch_update_status_to_fixed(self, fixed_host_ports: List[str], risk_type: int):
        """
        将一批指定的 'host:port' 的漏洞状态更新为 '3' (已修复)。
        """
        if not fixed_host_ports:
            print("信息: 没有需要更新为“已修复”状态的漏洞。")
            return

        self._connect()
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                placeholders = ', '.join(['%s'] * len(fixed_host_ports))
                sql = f"""
                    UPDATE {settings.DB_TABLE_NAME}
                    SET risk_status = '3'
                    WHERE CONCAT(host, ':', port) IN ({placeholders})
                    AND risk_status = '2'
                    AND risk_type = %s
                """
                # 将 risk_type 添加到参数列表中
                params = fixed_host_ports + [risk_type]
                cursor.execute(sql, params)
                self.connection.commit()
                print(f"成功: 已将 {cursor.rowcount} 条 risk_type={risk_type} 的漏洞状态更新为“已修复”。")
        except Exception as e:
            print(f"错误: 批量更新漏洞状态失败。原因: {e}")
            self.connection.rollback()
        finally:
            self._close()
